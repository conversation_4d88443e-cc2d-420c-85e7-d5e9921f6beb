/*
 * Custom theme for Tifinagh Converter
 * Simplified and more expressive of Latin to Tifinagh conversion
 */

:root {
  /* Colores principales inspirados en la cultura amazigh */
  --amazigh-blue: #1a5f7a;       /* Azul profundo representando el cielo del norte de África */
  --amazigh-yellow: #ffc107;     /* Amarillo representando el sol y las arenas del desierto */
  --amazigh-red: #e63946;        /* Rojo terracota común en artesanías amazigh */
  --amazigh-green: #2a9d8f;      /* Verde representando la vegetación de las montañas Atlas */
  --amazigh-silver: #e9ecef;     /* Plata representando la joyería tradicional amazigh */
  
  /* Asignación de colores a elementos funcionales */
  --primary-color: var(--amazigh-blue);
  --primary-rgb: 26, 95, 122;
  --secondary-color: var(--amazigh-yellow);
  --accent-color: var(--amazigh-red);
  --success-color: var(--amazigh-green);
  --light-bg: var(--amazigh-silver);
  
  /* Colores de texto */
  --text-dark: #212529;
  --text-light: #6c757d;
  --text-white: #ffffff;
  
  /* Sombras y efectos */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  
  /* Transiciones */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
}

/* Estilos generales */
body {
  font-family: 'Inter', sans-serif;
  color: var(--text-dark);
  line-height: 1.6;
}

/* Estilos para texto tifinagh */
.tifinagh-text {
  font-family: 'Noto Sans Tifinagh', sans-serif;
  font-size: 1.25rem;
  line-height: 1.8;
}

/* Botones personalizados */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
  background-color: rgba(var(--primary-rgb), 0.9);
  border-color: rgba(var(--primary-rgb), 0.9);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--text-dark);
}

.btn-accent {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--text-white);
}

/* Encabezado simplificado */
.navbar {
  background-color: var(--text-white);
  box-shadow: var(--shadow-sm);
}

.navbar-brand {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Sección hero simplificada */
.hero-section {
  background-color: var(--light-bg);
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

/* Visualizador de conversión */
.conversion-display {
  background-color: var(--text-white);
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.latin-card, .tifinagh-card {
  background-color: var(--text-white);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-normal);
}

.latin-card {
  border-left: 4px solid var(--primary-color);
}

.tifinagh-card {
  border-left: 4px solid var(--success-color);
}

.conversion-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  color: var(--primary-color);
  margin: 1rem 0;
}

/* Animación de caracteres flotantes */
.floating-chars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.floating-char {
  position: absolute;
  opacity: 0.1;
  animation: float 15s infinite ease-in-out;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* Patrones amazigh */
.amazigh-pattern {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
  background: linear-gradient(90deg, 
    var(--amazigh-blue) 25%, 
    var(--amazigh-yellow) 25%, 
    var(--amazigh-yellow) 50%, 
    var(--amazigh-red) 50%, 
    var(--amazigh-red) 75%, 
    var(--amazigh-green) 75%);
}

/* Tabla de alfabeto */
.alphabet-table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background-color: var(--text-white);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.alphabet-table th, .alphabet-table td {
  padding: 0.75rem 1rem;
  text-align: center;
  border: 1px solid var(--light-bg);
}

.alphabet-table th {
  background-color: var(--primary-color);
  color: var(--text-white);
  font-weight: 600;
}

.alphabet-table tr:nth-child(even) {
  background-color: rgba(var(--primary-rgb), 0.05);
}

/* Responsive */
@media (max-width: 768px) {
  .hero-section {
    padding: 2rem 0;
  }
  
  .conversion-display {
    padding: 1rem;
  }
}
