/**
 * ملف CSS لضمان ظهور شريط تمرير واحد فقط (المحرر الداخلي)
 * يخفي جميع أشرطة التمرير الأخرى في الصفحة
 */

/* إخفاء شريط التمرير الرئيسي للصفحة بالكامل */
html, body {
    overflow: hidden !important;
    height: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* إخفاء شريط التمرير في Webkit browsers */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* إخفاء أشرطة التمرير في جميع العناصر عدا المحررين (البسيط والمتقدم) */
*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable):not(.text-area textarea):not(.text-area .tifinagh-text):not(#latin-text):not(#tifinagh-text) {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable):not(.text-area textarea):not(.text-area .tifinagh-text):not(#latin-text):not(#tifinagh-text)::-webkit-scrollbar {
    display: none !important; /* Safari and Chrome */
    width: 0 !important;
    height: 0 !important;
}

/* التأكد من إخفاء أشرطة التمرير في حاويات المحرر المتقدم فقط */
#advanced-converter .container,
#advanced-converter .container-fluid,
#advanced-converter .row,
#advanced-converter .col,
#advanced-converter [class*="col-"],
#advanced-converter .tab-content,
#advanced-converter .tab-pane,
.advanced-editor-wrapper,
#document-editor {
    overflow: hidden !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

/* السماح بالتمرير الطبيعي في العناصر الأخرى */
.container:not(#advanced-converter .container),
.container-fluid:not(#advanced-converter .container-fluid),
.row:not(#advanced-converter .row),
.col:not(#advanced-converter .col),
[class*="col-"]:not(#advanced-converter [class*="col-"]) {
    overflow: visible !important;
}

/* إخفاء أشرطة التمرير في حاويات المحرر المتقدم فقط */
#advanced-converter .container::-webkit-scrollbar,
#advanced-converter .container-fluid::-webkit-scrollbar,
#advanced-converter .row::-webkit-scrollbar,
#advanced-converter .col::-webkit-scrollbar,
#advanced-converter [class*="col-"]::-webkit-scrollbar,
#advanced-converter .tab-content::-webkit-scrollbar,
#advanced-converter .tab-pane::-webkit-scrollbar,
.advanced-editor-wrapper::-webkit-scrollbar,
#document-editor::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* السماح بشريط التمرير في المحررين (البسيط والمتقدم) */
.document-editor-content,
.ck-content,
.ck-editor__editable {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important; /* Firefox */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ */
}

/* السماح بشريط التمرير في المحرر البسيط - أولوية عالية */
.text-area textarea,
.text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important; /* Firefox */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ */
    /* ضمان ظهور شريط التمرير */
    min-height: 500px !important;
    max-height: 500px !important;
}

/* تخصيص مظهر شريط التمرير المسموح */
.document-editor-content::-webkit-scrollbar,
.ck-content::-webkit-scrollbar,
.ck-editor__editable::-webkit-scrollbar {
    display: block !important;
    width: 12px !important;
    height: 12px !important;
}

/* تخصيص مظهر شريط التمرير في المحرر البسيط */
.text-area textarea::-webkit-scrollbar,
.text-area .tifinagh-text::-webkit-scrollbar {
    display: block !important;
    width: 8px !important;
    height: 8px !important;
}

/* تخصيص مظهر شريط التمرير في المحرر المتقدم */
.document-editor-content::-webkit-scrollbar-track,
.ck-content::-webkit-scrollbar-track,
.ck-editor__editable::-webkit-scrollbar-track {
    background: #f1f3f4 !important;
    border-radius: 6px !important;
    margin: 2px !important;
}

.document-editor-content::-webkit-scrollbar-thumb,
.ck-content::-webkit-scrollbar-thumb,
.ck-editor__editable::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 6px !important;
    border: 2px solid #f1f3f4 !important;
    min-height: 20px !important;
}

.document-editor-content::-webkit-scrollbar-thumb:hover,
.ck-content::-webkit-scrollbar-thumb:hover,
.ck-editor__editable::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

.document-editor-content::-webkit-scrollbar-thumb:active,
.ck-content::-webkit-scrollbar-thumb:active,
.ck-editor__editable::-webkit-scrollbar-thumb:active {
    background: #909090 !important;
}

/* تخصيص مظهر شريط التمرير في المحرر البسيط */
.text-area textarea::-webkit-scrollbar-track,
.text-area .tifinagh-text::-webkit-scrollbar-track {
    background: #f8f9fa !important;
    border-radius: 4px !important;
}

.text-area textarea::-webkit-scrollbar-thumb,
.text-area .tifinagh-text::-webkit-scrollbar-thumb {
    background: #dee2e6 !important;
    border-radius: 4px !important;
    border: 1px solid #f8f9fa !important;
}

.text-area textarea::-webkit-scrollbar-thumb:hover,
.text-area .tifinagh-text::-webkit-scrollbar-thumb:hover {
    background: #adb5bd !important;
}

/* إعدادات خاصة للشاشات الصغيرة */
@media (max-width: 768px) {
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar,
    .ck-editor__editable::-webkit-scrollbar {
        width: 10px !important;
    }
}

@media (max-width: 480px) {
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar,
    .ck-editor__editable::-webkit-scrollbar {
        width: 8px !important;
    }
}

/* إخفاء أي أشرطة تمرير قد تظهر في Bootstrap أو مكتبات أخرى */
.modal,
.modal-body,
.modal-content,
.dropdown-menu,
.navbar,
.nav,
.card,
.card-body {
    overflow: hidden !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.modal::-webkit-scrollbar,
.modal-body::-webkit-scrollbar,
.modal-content::-webkit-scrollbar,
.dropdown-menu::-webkit-scrollbar,
.navbar::-webkit-scrollbar,
.nav::-webkit-scrollbar,
.card::-webkit-scrollbar,
.card-body::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}
