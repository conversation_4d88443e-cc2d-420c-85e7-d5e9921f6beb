<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Getter Fix - Compatibility</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .test-header {
            background: linear-gradient(135deg, #198754, #20c997);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .console-output {
            background: #1e1e1e;
            color: #ffffff;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .test-result {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-left: 4px solid;
        }
        
        .test-pass {
            background: #d1f2eb;
            border-color: #198754;
            color: #0f5132;
        }
        
        .test-fail {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .test-info {
            background: #cff4fc;
            border-color: #0dcaf0;
            color: #055160;
        }
    </style>
</head>
<body class="bg-light">
    
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 اختبار إصلاح Getter Conflicts</h1>
            <p class="mb-0">Testing Fixed Compatibility v1.1.0</p>
        </div>
        
        <!-- اختبارات سريعة -->
        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            
            <div id="quick-tests">
                <!-- سيتم ملء النتائج هنا -->
            </div>
            
            <button class="btn btn-success" onclick="runQuickTests()">
                <i class="bi bi-lightning"></i> تشغيل اختبارات سريعة
            </button>
        </div>
        
        <!-- Console Output -->
        <div class="test-section">
            <h2>🖥️ Console Output</h2>
            <div id="console-output" class="console-output">
                [System] Initializing getter fix test environment...\n
            </div>
            <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearConsole()">
                <i class="bi bi-trash"></i> Clear Console
            </button>
        </div>
        
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Mock API for testing -->
    <script>
        // Mock translations
        window.mockTranslations = {
            "test.hello": {
                "en": "Hello",
                "am": "ⴰⵣⵓⵍ"
            }
        };
        
        // Mock fetch
        window.fetch = function(url, options) {
            if (url === '/get-i18n') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        translations: window.mockTranslations
                    })
                });
            }
            
            if (url === '/set-language') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        message: 'Language changed successfully'
                    })
                });
            }
            
            return Promise.reject(new Error('Unknown endpoint'));
        };
        
        // Console logging
        let consoleOutput = [];
        
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.push(logEntry);
            updateConsoleDisplay();
        }
        
        function updateConsoleDisplay() {
            const console = document.getElementById('console-output');
            console.textContent = consoleOutput.join('\n');
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            consoleOutput = ['[System] Console cleared.'];
            updateConsoleDisplay();
        }
        
        // Test results
        function addTestResult(testName, status, message) {
            const resultsContainer = document.getElementById('quick-tests');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${status}`;
            
            const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : 'ℹ️';
            resultDiv.innerHTML = `
                <strong>${statusIcon} ${testName}</strong><br>
                <small>${message}</small>
            `;
            
            resultsContainer.appendChild(resultDiv);
            logToConsole(`Test ${testName}: ${status} - ${message}`, status === 'pass' ? 'success' : status === 'fail' ? 'error' : 'info');
        }
        
        // Quick tests
        function runQuickTests() {
            logToConsole('Starting quick getter fix tests...', 'info');
            document.getElementById('quick-tests').innerHTML = '';
            
            // Test 1: window.i18n exists
            try {
                if (window.i18n) {
                    addTestResult('window.i18n exists', 'pass', 'window.i18n object is available');
                } else {
                    addTestResult('window.i18n exists', 'fail', 'window.i18n is not available');
                    return;
                }
            } catch (error) {
                addTestResult('window.i18n exists', 'fail', `Error: ${error.message}`);
                return;
            }
            
            // Test 2: onLoad function
            try {
                if (typeof window.i18n.onLoad === 'function') {
                    addTestResult('onLoad function', 'pass', 'onLoad function is available');
                } else {
                    addTestResult('onLoad function', 'fail', 'onLoad function is missing');
                }
            } catch (error) {
                addTestResult('onLoad function', 'fail', `Error: ${error.message}`);
            }
            
            // Test 3: isLoaded property
            try {
                const isLoaded = window.i18n.isLoaded;
                addTestResult('isLoaded property', 'pass', `isLoaded: ${isLoaded} (type: ${typeof isLoaded})`);
            } catch (error) {
                addTestResult('isLoaded property', 'fail', `Error accessing isLoaded: ${error.message}`);
            }
            
            // Test 4: currentLang property
            try {
                const currentLang = window.i18n.currentLang;
                addTestResult('currentLang property', 'pass', `currentLang: ${currentLang} (type: ${typeof currentLang})`);
            } catch (error) {
                addTestResult('currentLang property', 'fail', `Error accessing currentLang: ${error.message}`);
            }
            
            // Test 5: translations property
            try {
                const translations = window.i18n.translations;
                addTestResult('translations property', 'pass', `translations available (type: ${typeof translations})`);
            } catch (error) {
                addTestResult('translations property', 'fail', `Error accessing translations: ${error.message}`);
            }
            
            // Test 6: translate function
            try {
                if (typeof window.i18n.translate === 'function') {
                    const result = window.i18n.translate('test.hello');
                    addTestResult('translate function', 'pass', `translate('test.hello') = "${result}"`);
                } else {
                    addTestResult('translate function', 'fail', 'translate function is missing');
                }
            } catch (error) {
                addTestResult('translate function', 'fail', `Error: ${error.message}`);
            }
            
            // Test 7: setLanguage function
            try {
                if (typeof window.i18n.setLanguage === 'function') {
                    addTestResult('setLanguage function', 'pass', 'setLanguage function is available');
                } else {
                    addTestResult('setLanguage function', 'fail', 'setLanguage function is missing');
                }
            } catch (error) {
                addTestResult('setLanguage function', 'fail', `Error: ${error.message}`);
            }
            
            // Test 8: Object.assign compatibility
            try {
                const testObj = {};
                Object.assign(testObj, { isLoaded: window.i18n.isLoaded });
                addTestResult('Object.assign compatibility', 'pass', 'No getter conflicts detected');
            } catch (error) {
                addTestResult('Object.assign compatibility', 'fail', `Getter conflict: ${error.message}`);
            }
            
            logToConsole('Quick tests completed', 'success');
        }
        
        // Override console methods to capture output
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        console.log = function(...args) {
            logToConsole(args.join(' '), 'info');
            originalConsole.log(...args);
        };
        
        console.warn = function(...args) {
            logToConsole(args.join(' '), 'warning');
            originalConsole.warn(...args);
        };
        
        console.error = function(...args) {
            logToConsole(args.join(' '), 'error');
            originalConsole.error(...args);
        };
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('Test page loaded', 'info');
            
            // تشغيل اختبار تلقائي بعد 2 ثانية
            setTimeout(() => {
                logToConsole('Running automatic tests...', 'info');
                runQuickTests();
            }, 2000);
        });
        
        // Monitor for system events
        document.addEventListener('system:ready', function() {
            logToConsole('System ready event received', 'success');
        });
        
        document.addEventListener('languageSystemLoaded', function() {
            logToConsole('Legacy languageSystemLoaded event received', 'success');
        });
    </script>
    
    <!-- New Language System -->
    <script src="static/js/language-system-new.js"></script>
    <!-- Compatibility Fix v1.1.0 -->
    <script src="static/js/compatibility-fix.js"></script>
    
</body>
</html>
