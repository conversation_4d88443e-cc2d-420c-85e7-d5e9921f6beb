/**
 * Dual Editor System for Tifinagh Converter
 *
 * This script manages the dual editor system with:
 * 1. Simple editor (textarea)
 * 2. Advanced editor (Quill.js)
 */

let quillEditor = null;

document.addEventListener('DOMContentLoaded', function() {
    // Get editor toggle buttons
    const simpleEditorBtn = document.getElementById('simple-editor-btn');
    const advancedEditorBtn = document.getElementById('advanced-editor-btn');

    // Get editor containers
    const simpleEditorContainer = document.getElementById('simple-editor-container');
    const advancedEditorContainer = document.getElementById('advanced-editor-container');

    // Get convert tables button
    const convertTablesBtn = document.getElementById('convert-tables-btn');

    // Initialize Quill editor when advanced editor button is clicked
    if (advancedEditorBtn) {
        advancedEditorBtn.addEventListener('click', function() {
            // Switch to advanced editor
            simpleEditorBtn.classList.remove('active');
            advancedEditorBtn.classList.add('active');
            simpleEditorContainer.classList.remove('active');
            advancedEditorContainer.classList.add('active');

            // Show convert tables button
            if (convertTablesBtn) {
                convertTablesBtn.style.display = 'flex';
            }

            // Initialize Quill if not already initialized
            if (!quillEditor) {
                initQuillEditor();
            }

            // Transfer content from simple editor to advanced editor
            transferContentToAdvancedEditor();
        });
    }

    // Switch back to simple editor
    if (simpleEditorBtn) {
        simpleEditorBtn.addEventListener('click', function() {
            // Switch to simple editor
            advancedEditorBtn.classList.remove('active');
            simpleEditorBtn.classList.add('active');
            advancedEditorContainer.classList.remove('active');
            simpleEditorContainer.classList.add('active');

            // Hide convert tables button
            if (convertTablesBtn) {
                convertTablesBtn.style.display = 'none';
            }

            // Transfer content from advanced editor to simple editor
            transferContentToSimpleEditor();
        });
    }

    // Add event listener for convert tables button
    if (convertTablesBtn) {
        convertTablesBtn.addEventListener('click', function() {
            convertTableCells();
        });
    }
});

/**
 * Initialize Quill editor
 */
function initQuillEditor() {
    // Check if Quill is loaded
    if (typeof Quill === 'undefined') {
        console.error('Quill.js is not loaded');
        return;
    }

    // Check if we have the better table implementation
    if (typeof window.initQuillWithBetterTable === 'function') {
        // Use the better table implementation
        quillEditor = window.initQuillWithBetterTable();
        console.log('Quill initialized with better table support');
        return;
    }

    // Fallback to standard implementation
    console.log('Falling back to standard Quill implementation');

    // Add fonts to whitelist
    const Font = Quill.import('formats/font');
    // Add Aref Ruqaa as the default font
    Font.whitelist = ['aref', 'mirza', 'roboto'];
    Quill.register(Font, true);

    // Initialize Quill editor with basic configuration
    quillEditor = new Quill('#advanced-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                [{ 'font': Font.whitelist }],  // Add font dropdown
                ['bold', 'italic', 'underline', 'strike'],
                ['blockquote', 'code-block'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'script': 'sub'}, { 'script': 'super' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                [{ 'direction': 'rtl' }],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                ['clean'],
                ['link', 'image'],
                ['table']
            ],
            clipboard: {
                matchVisual: false
            }
        },
        placeholder: 'Enter text in Latin script...'
    });

    // Add table insertion functionality
    const toolbar = quillEditor.getModule('toolbar');
    toolbar.addHandler('table', function() {
        insertTable();
    });

    // Listen for content changes
    quillEditor.on('text-change', debounce(function() {
        // Update character counter
        updateCharCounter();

        // Check if content contains tables
        const content = quillEditor.root.innerHTML;
        if (content.includes('<table')) {
            document.getElementById('convert-tables-btn').style.display = 'flex';
        } else {
            // Use regular conversion for non-table content
            convertText();
        }
    }, 300));
}

/**
 * Insert a table into the Quill editor
 */
function insertTable() {
    // Create a table with 3 rows and 3 columns
    const tableHTML = `
        <table>
            <tbody>
                <tr><td>Cell 1</td><td>Cell 2</td><td>Cell 3</td></tr>
                <tr><td>Cell 4</td><td>Cell 5</td><td>Cell 6</td></tr>
                <tr><td>Cell 7</td><td>Cell 8</td><td>Cell 9</td></tr>
            </tbody>
        </table>
        <p><br></p>
    `;

    // Get current selection
    const range = quillEditor.getSelection(true);

    // Insert the table at the current selection
    quillEditor.clipboard.dangerouslyPasteHTML(range.index, tableHTML);

    // Show convert tables button
    document.getElementById('convert-tables-btn').style.display = 'flex';
}

/**
 * Transfer content from simple editor to advanced editor
 */
function transferContentToAdvancedEditor() {
    if (!quillEditor) return;

    const simpleEditor = document.getElementById('latin-text');
    const content = simpleEditor.value;

    // Check if content contains table markup
    if (content.includes('|') && (content.includes('--') || content.includes('-:') || content.includes(':-'))) {
        // Convert markdown tables to HTML
        const htmlContent = convertMarkdownTablesToHtml(content);
        quillEditor.clipboard.dangerouslyPasteHTML(htmlContent);
    } else {
        // Set plain text
        quillEditor.setText(content);
    }
}

/**
 * Transfer content from advanced editor to simple editor
 */
function transferContentToSimpleEditor() {
    if (!quillEditor) return;

    const simpleEditor = document.getElementById('latin-text');

    // Get text content from Quill editor
    const content = quillEditor.getText();

    // Set content in simple editor
    simpleEditor.value = content;

    // Trigger input event to update character counter
    simpleEditor.dispatchEvent(new Event('input'));
}

/**
 * Convert markdown tables to HTML
 */
function convertMarkdownTablesToHtml(text) {
    // Simple markdown table detection and conversion
    const lines = text.split('\n');
    let inTable = false;
    let tableHTML = '';
    let result = '';

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // Check if line is part of a table
        if (line.trim().startsWith('|') && line.trim().endsWith('|')) {
            if (!inTable) {
                // Start of a new table
                inTable = true;
                tableHTML = '<table><tbody>';
            }

            // Skip separator line
            if (line.includes('|-') || line.includes('-|')) {
                continue;
            }

            // Process table row
            const cells = line.split('|').filter(cell => cell.trim() !== '');
            tableHTML += '<tr>';

            for (const cell of cells) {
                tableHTML += `<td>${cell.trim()}</td>`;
            }

            tableHTML += '</tr>';
        } else {
            if (inTable) {
                // End of table
                inTable = false;
                tableHTML += '</tbody></table>';
                result += tableHTML + '\n';
                tableHTML = '';
            }

            // Add non-table line
            result += line + '\n';
        }
    }

    // Close any open table
    if (inTable) {
        tableHTML += '</tbody></table>';
        result += tableHTML;
    }

    return result;
}

/**
 * Convert table cells from Latin to Tifinagh
 */
function convertTableCells() {
    // Check which editor is active
    const isAdvancedEditorActive = document.getElementById('advanced-editor-container').classList.contains('active');
    const targetText = document.getElementById('tifinagh-text');
    
    // Show loading state
    targetText.textContent = 'جاري تحويل الجداول...';
    targetText.classList.add('loading');
    
    try {
        // Get HTML content with tables
        let content;
        if (isAdvancedEditorActive && quillEditor) {
            content = quillEditor.root.innerHTML;
        } else {
            // For simple editor, look for markdown tables
            const simpleEditor = document.getElementById('latin-text');
            content = simpleEditor.value;
            
            // Convert markdown to HTML if markdown tables are detected
            if (content.includes('|') && (content.includes('--') || content.includes('-:') || content.includes(':-'))) {
                content = convertMarkdownTablesToHtml(content);
            } else {
                // No tables found in simple editor content
                targetText.textContent = 'لم يتم العثور على جداول في المحتوى. يرجى التأكد من وجود جداول صالحة.';
                targetText.classList.remove('loading');
                return;
            }
        }
        
        // Parse HTML to extract table cells
        const parser = new DOMParser();
        const doc = parser.parseFromString(content, 'text/html');
        const cells = doc.querySelectorAll('td, th');
        
        if (cells.length === 0) {
            // No table cells found in the content
            targetText.textContent = 'لم يتم العثور على خلايا جداول في المحتوى. يرجى التأكد من وجود جداول صالحة.';
            targetText.classList.remove('loading');
            return;
        }
        
        // Prepare cell data for API request
        const cellsData = [];
        cells.forEach((cell, index) => {
            cellsData.push({
                id: `cell_${index}`,
                text: cell.textContent.trim()
            });
        });

        // إعداد عملية التحويل للاستدعاء المتكرر عند الفشل
        const convertOperation = () => {
            // Set a timeout for the API request
            const requestTimeout = setTimeout(() => {
                targetText.textContent = 'استغرقت عملية التحويل وقتًا طويلاً. يرجى المحاولة مرة أخرى أو تقسيم المحتوى إلى أجزاء أصغر.';
                targetText.classList.remove('loading');
            }, 30000); // 30 second timeout
            
            // Call API to convert table cells
            fetch('/api/convert-table-cells', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    cells: cellsData,
                    chunkSize: 50 // تحديد حجم الكتلة لمعالجة الخلايا على دفعات
                })
            })
            .then(response => {
                clearTimeout(requestTimeout);
                
                if (!response.ok) {
                    if (response.status === 413) {
                        throw new Error('المحتوى كبير جدًا للتحويل. يرجى تقسيمه إلى أجزاء أصغر.');
                    }
                    throw new Error(`خطأ في الاستجابة: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data || !data.cells || !Array.isArray(data.cells)) {
                    throw new Error('تنسيق استجابة غير صالح من الخادم');
                }
                
                // إنشاء نسخة احتياطية من المحتوى الأصلي قبل التحديث
                const originalCells = Array.from(cells).map(cell => cell.textContent);
                
                try {
                    // Update cell content with converted text
                    data.cells.forEach((convertedCell, index) => {
                        if (index < cells.length) {
                            cells[index].textContent = convertedCell.convertedText;
                        }
                    });
                    
                    // Set the converted HTML content to the output
                    const convertedHtml = doc.body.innerHTML;
                    targetText.innerHTML = convertedHtml;
                    targetText.classList.add('contains-table');
                    targetText.classList.remove('loading');
                    
                    // Update character counter
                    updateCharCounter();
                    
                    // Show success message that will fade away
                    showToast('تم تحويل الجداول بنجاح', 'success', 3000);
                } catch (updateError) {
                    console.error('Error updating table cells:', updateError);
                    
                    // استعادة المحتوى الأصلي في حالة الفشل
                    originalCells.forEach((originalText, index) => {
                        if (index < cells.length) {
                            cells[index].textContent = originalText;
                        }
                    });
                    
                    throw new Error('فشل تحديث محتوى الجدول: ' + updateError.message);
                }
            })
            .catch(error => {
                clearTimeout(requestTimeout);
                console.error('Error converting table cells:', error);
                targetText.textContent = '';
                targetText.classList.remove('loading');
                
                // استخدام مدير الأخطاء المتقدم إذا كان متاحًا
                if (window.errorHandler) {
                    window.errorHandler.showError(
                        new Error(`حدث خطأ أثناء تحويل الجداول: ${error.message}`),
                        convertOperation
                    );
                } else {
                    // الرجوع إلى طريقة العرض التقليدية
                    targetText.innerHTML = `<div class="conversion-error">
                        <p>حدث خطأ أثناء تحويل الجداول: ${error.message}</p>
                        <button class="btn retry-btn">إعادة المحاولة</button>
                    </div>`;
                    
                    // إضافة استجابة لزر إعادة المحاولة
                    const retryBtn = targetText.querySelector('.retry-btn');
                    if (retryBtn) {
                        retryBtn.addEventListener('click', convertOperation);
                    }
                }
            });
        };

        // بدء عملية التحويل
        convertOperation();
        
    } catch (error) {
        console.error('Unexpected error in convertTableCells:', error);
        targetText.textContent = '';
        targetText.classList.remove('loading');
        
        // استخدام مدير الأخطاء المتقدم إذا كان متاحًا
        if (window.errorHandler) {
            window.errorHandler.showError(
                new Error(`حدث خطأ غير متوقع: ${error.message}`),
                convertTableCells
            );
        } else {
            targetText.innerHTML = `<div class="conversion-error">
                <p>حدث خطأ غير متوقع: ${error.message}</p>
                <button class="btn retry-btn">إعادة المحاولة</button>
            </div>`;
            
            // إضافة استجابة لزر إعادة المحاولة
            const retryBtn = targetText.querySelector('.retry-btn');
            if (retryBtn) {
                retryBtn.addEventListener('click', convertTableCells);
            }
        }
    }
}

/**
 * Override the original convertText function to work with both editors
 */
window.originalConvertText = window.convertText;
window.convertText = function() {
    // Check which editor is active
    const isAdvancedEditorActive = document.getElementById('advanced-editor-container').classList.contains('active');

    if (isAdvancedEditorActive && quillEditor) {
        // Get text from Quill editor
        const latinText = quillEditor.getText();

        if (!latinText) {
            document.getElementById('tifinagh-text').textContent = '';
            return;
        }

        // Check if content contains tables
        const content = quillEditor.root.innerHTML;
        if (content.includes('<table')) {
            // If there are tables, use the table converter
            return;
        }

        // Call the API to convert the text
        fetch('/api/convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: latinText,
                hasTable: false
            }),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Display result in output area
            document.getElementById('tifinagh-text').textContent = data.result;

            // Update character counter
            updateCharCounter();
        })
        .catch(error => {
            console.error('Error converting text:', error);
            document.getElementById('tifinagh-text').textContent = 'Error converting text. Please try again.';
        });
    } else {
        // Use the original convertText function for the simple editor
        window.originalConvertText();
    }
};

/**
 * Override the original clearEditor function to work with both editors
 */
document.addEventListener('DOMContentLoaded', function() {
    const clearBtn = document.getElementById('clear-latin');
    if (clearBtn) {
        // Remove existing event listeners
        const newClearBtn = clearBtn.cloneNode(true);
        clearBtn.parentNode.replaceChild(newClearBtn, clearBtn);

        // Add new event listener
        newClearBtn.addEventListener('click', function() {
            clearEditor();
        });
    }
});

/**
 * Clear the active editor
 */
function clearEditor() {
    const isAdvancedEditorActive = document.getElementById('advanced-editor-container').classList.contains('active');

    if (isAdvancedEditorActive && quillEditor) {
        // Clear Quill editor
        quillEditor.setText('');
    } else {
        // Clear simple editor
        document.getElementById('latin-text').value = '';
    }

    // Clear output area
    document.getElementById('tifinagh-text').textContent = '';
    document.getElementById('tifinagh-text').classList.remove('contains-table');

    // Update character counter
    updateCharCounter();
}

/**
 * Override the original updateCharCounter function to work with both editors
 */
window.originalUpdateCharCounter = window.updateCharCounter;
window.updateCharCounter = function() {
    const sourceCharCounter = document.querySelector('.latin-area .character-count');
    const targetCharCounter = document.querySelector('.tifinagh-area .character-count');
    const targetText = document.getElementById('tifinagh-text');

    // Check which editor is active
    const isAdvancedEditorActive = document.getElementById('advanced-editor-container').classList.contains('active');

    // Update source text character counter
    if (sourceCharCounter) {
        let sourceLength;

        if (isAdvancedEditorActive && quillEditor) {
            // Get text length from Quill editor
            sourceLength = quillEditor.getText().length - 1; // Subtract 1 for the trailing newline
        } else {
            // Get text length from simple editor
            sourceLength = document.getElementById('latin-text').value.length;
        }

        sourceCharCounter.textContent = `${sourceLength} characters`;

        // Add warning class if approaching limit
        if (sourceLength > 4500) {
            sourceCharCounter.classList.add('warning');
        } else {
            sourceCharCounter.classList.remove('warning');
        }
    }

    // Update target text character counter
    if (targetCharCounter && targetText) {
        let targetLength;

        if (targetText.classList.contains('contains-table')) {
            // For HTML content, create a temporary element to get text length
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = targetText.innerHTML;
            targetLength = (tempDiv.innerText || tempDiv.textContent).length;
        } else {
            targetLength = targetText.textContent.length;
        }

        targetCharCounter.textContent = `${targetLength} characters`;

        // Add warning class if approaching limit
        if (targetLength > 4500) {
            targetCharCounter.classList.add('warning');
        } else {
            targetCharCounter.classList.remove('warning');
        }
    }
};
