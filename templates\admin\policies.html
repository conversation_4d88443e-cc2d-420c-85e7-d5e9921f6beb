{% extends 'admin/base.html' %}

{% block title %}Policies & Legal - Admin Panel{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Policies & Legal</li>
{% endblock %}

{% block extra_css %}
<style>
    .ck-editor__editable_inline {
        min-height: 300px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3">Policies & Legal</h1>
        <p class="text-muted">Manage legal policies and terms for your website.</p>
    </div>
</div>

<!-- Nav tabs -->
<ul class="nav nav-tabs mb-4" id="policyTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="privacy-tab" data-bs-toggle="tab" data-bs-target="#privacy" type="button" role="tab" aria-controls="privacy" aria-selected="true">
            <i class="bi bi-shield-lock me-1"></i> Privacy Policy
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="terms-tab" data-bs-toggle="tab" data-bs-target="#terms" type="button" role="tab" aria-controls="terms" aria-selected="false">
            <i class="bi bi-file-text me-1"></i> Terms of Use
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="cookie-tab" data-bs-toggle="tab" data-bs-target="#cookie" type="button" role="tab" aria-controls="cookie" aria-selected="false">
            <i class="bi bi-cart4 me-1"></i> Cookie Policy
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="banners-tab" data-bs-toggle="tab" data-bs-target="#banners" type="button" role="tab" aria-controls="banners" aria-selected="false">
            <i class="bi bi-window me-1"></i> Consent Banners
        </button>
    </li>
</ul>

<!-- Tab content -->
<div class="tab-content">
    <!-- Privacy Policy Tab -->
    <div class="tab-pane fade show active" id="privacy" role="tabpanel" aria-labelledby="privacy-tab">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Privacy Policy</h5>
            </div>
            <div class="card-body">
                <form id="privacyForm" action="{{ url_for('admin.admin_save_privacy_policy') }}" method="POST">
                    <div class="mb-3">
                        <label for="privacyTitle" class="form-label">Page Title</label>
                        <input type="text" class="form-control" id="privacyTitle" name="privacy_title" value="{{ policies.privacy_title|default('Privacy Policy', true) }}">
                    </div>

                    <div class="mb-3">
                        <label for="privacyContent" class="form-label">Content</label>
                        <textarea class="form-control editor" id="privacyContent" name="privacy_content" rows="15">{{ policies.privacy_content|default(default_privacy_policy, true) }}</textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="privacyEnabled" name="privacy_enabled" {% if policies.privacy_enabled %}checked{% endif %}>
                            <label class="form-check-label" for="privacyEnabled">Enable Privacy Policy</label>
                        </div>
                        <div class="form-text">When enabled, the Privacy Policy page will be accessible to visitors.</div>
                    </div>

                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-save me-1"></i> Save Changes
                        </button>
                        <a href="{{ url_for('privacy_policy') }}" target="_blank" class="btn btn-outline-secondary">
                            <i class="bi bi-eye me-1"></i> Preview
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Terms of Use Tab -->
    <div class="tab-pane fade" id="terms" role="tabpanel" aria-labelledby="terms-tab">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Terms of Use</h5>
            </div>
            <div class="card-body">
                <form id="termsForm" action="{{ url_for('admin.admin_save_terms') }}" method="POST">
                    <div class="mb-3">
                        <label for="termsTitle" class="form-label">Page Title</label>
                        <input type="text" class="form-control" id="termsTitle" name="terms_title" value="{{ policies.terms_title|default('Terms of Use', true) }}">
                    </div>

                    <div class="mb-3">
                        <label for="termsContent" class="form-label">Content</label>
                        <textarea class="form-control editor" id="termsContent" name="terms_content" rows="15">{{ policies.terms_content|default(default_terms, true) }}</textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="termsEnabled" name="terms_enabled" {% if policies.terms_enabled %}checked{% endif %}>
                            <label class="form-check-label" for="termsEnabled">Enable Terms of Use</label>
                        </div>
                        <div class="form-text">When enabled, the Terms of Use page will be accessible to visitors.</div>
                    </div>

                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-save me-1"></i> Save Changes
                        </button>
                        <a href="{{ url_for('terms_of_use') }}" target="_blank" class="btn btn-outline-secondary">
                            <i class="bi bi-eye me-1"></i> Preview
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Cookie Policy Tab -->
    <div class="tab-pane fade" id="cookie" role="tabpanel" aria-labelledby="cookie-tab">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Cookie Policy</h5>
            </div>
            <div class="card-body">
                <form id="cookieForm" action="{{ url_for('admin.admin_save_cookie_policy') }}" method="POST">
                    <div class="mb-3">
                        <label for="cookieTitle" class="form-label">Page Title</label>
                        <input type="text" class="form-control" id="cookieTitle" name="cookie_title" value="{{ policies.cookie_title|default('Cookie Policy', true) }}">
                    </div>

                    <div class="mb-3">
                        <label for="cookieContent" class="form-label">Content</label>
                        <textarea class="form-control editor" id="cookieContent" name="cookie_content" rows="15">{{ policies.cookie_content|default(default_cookie_policy, true) }}</textarea>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="cookieEnabled" name="cookie_enabled" {% if policies.cookie_enabled %}checked{% endif %}>
                            <label class="form-check-label" for="cookieEnabled">Enable Cookie Policy</label>
                        </div>
                        <div class="form-text">When enabled, the Cookie Policy page will be accessible to visitors.</div>
                    </div>

                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-save me-1"></i> Save Changes
                        </button>
                        <a href="{{ url_for('cookie_policy') }}" target="_blank" class="btn btn-outline-secondary">
                            <i class="bi bi-eye me-1"></i> Preview
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Consent Banners Tab -->
    <div class="tab-pane fade" id="banners" role="tabpanel" aria-labelledby="banners-tab">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Cookie Consent Banner</h5>
            </div>
            <div class="card-body">
                <form id="consentForm" action="{{ url_for('admin.admin_save_consent') }}" method="POST">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="consentTitle" class="form-label">Banner Title</label>
                                <input type="text" class="form-control" id="consentTitle" name="consent_title" value="{{ policies.consent_title|default('Cookie Consent', true) }}">
                            </div>

                            <div class="mb-3">
                                <label for="consentMessage" class="form-label">Message</label>
                                <textarea class="form-control" id="consentMessage" name="consent_message" rows="3">{{ policies.consent_message|default('This website uses cookies to ensure you get the best experience on our website.', true) }}</textarea>
                            </div>

                            <div class="mb-3">
                                <label for="acceptButtonText" class="form-label">Accept Button Text</label>
                                <input type="text" class="form-control" id="acceptButtonText" name="accept_button_text" value="{{ policies.accept_button_text|default('Accept All', true) }}">
                            </div>

                            <div class="mb-3">
                                <label for="rejectButtonText" class="form-label">Reject Button Text</label>
                                <input type="text" class="form-control" id="rejectButtonText" name="reject_button_text" value="{{ policies.reject_button_text|default('Reject All', true) }}">
                            </div>

                            <div class="mb-3">
                                <label for="settingsButtonText" class="form-label">Settings Button Text</label>
                                <input type="text" class="form-control" id="settingsButtonText" name="settings_button_text" value="{{ policies.settings_button_text|default('Cookie Settings', true) }}">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bannerPosition" class="form-label">Banner Position</label>
                                <select class="form-select" id="bannerPosition" name="banner_position">
                                    <option value="bottom" {% if policies.banner_position == 'bottom' %}selected{% endif %}>Bottom</option>
                                    <option value="top" {% if policies.banner_position == 'top' %}selected{% endif %}>Top</option>
                                    <option value="bottom-left" {% if policies.banner_position == 'bottom-left' %}selected{% endif %}>Bottom Left</option>
                                    <option value="bottom-right" {% if policies.banner_position == 'bottom-right' %}selected{% endif %}>Bottom Right</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="bannerTheme" class="form-label">Banner Theme</label>
                                <select class="form-select" id="bannerTheme" name="banner_theme">
                                    <option value="light" {% if policies.banner_theme == 'light' %}selected{% endif %}>Light</option>
                                    <option value="dark" {% if policies.banner_theme == 'dark' %}selected{% endif %}>Dark</option>
                                </select>
                            </div>

                            <div class="mb-4">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="showConsentBanner" name="show_consent_banner" {% if policies.show_consent_banner %}checked{% endif %}>
                                    <label class="form-check-label" for="showConsentBanner">Show Cookie Consent Banner</label>
                                </div>
                                <div class="form-text">When enabled, the cookie consent banner will be shown to new visitors.</div>
                            </div>

                            <div class="card mt-4 bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Banner Preview</h6>
                                    <div class="consent-banner-preview p-3 border rounded" id="consentPreview">
                                        <h5 id="previewTitle">Cookie Consent</h5>
                                        <p id="previewMessage">This website uses cookies to ensure you get the best experience on our website.</p>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary" id="previewAccept">Accept All</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="previewReject">Reject All</button>
                                            <button type="button" class="btn btn-sm btn-link" id="previewSettings">Cookie Settings</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save me-1"></i> Save Changes
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdn.ckeditor.com/ckeditor5/36.0.0/classic/ckeditor.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize editors
        document.querySelectorAll('.editor').forEach(editor => {
            ClassicEditor
                .create(editor)
                .catch(error => {
                    console.error(error);
                });
        });

        // Live preview for consent banner
        const consentTitle = document.getElementById('consentTitle');
        const consentMessage = document.getElementById('consentMessage');
        const acceptButtonText = document.getElementById('acceptButtonText');
        const rejectButtonText = document.getElementById('rejectButtonText');
        const settingsButtonText = document.getElementById('settingsButtonText');
        const bannerTheme = document.getElementById('bannerTheme');

        const previewTitle = document.getElementById('previewTitle');
        const previewMessage = document.getElementById('previewMessage');
        const previewAccept = document.getElementById('previewAccept');
        const previewReject = document.getElementById('previewReject');
        const previewSettings = document.getElementById('previewSettings');
        const consentPreview = document.getElementById('consentPreview');

        // Update preview when input fields change
        consentTitle.addEventListener('input', function() {
            previewTitle.textContent = this.value;
        });

        consentMessage.addEventListener('input', function() {
            previewMessage.textContent = this.value;
        });

        acceptButtonText.addEventListener('input', function() {
            previewAccept.textContent = this.value;
        });

        rejectButtonText.addEventListener('input', function() {
            previewReject.textContent = this.value;
        });

        settingsButtonText.addEventListener('input', function() {
            previewSettings.textContent = this.value;
        });

        bannerTheme.addEventListener('change', function() {
            if (this.value === 'dark') {
                consentPreview.style.backgroundColor = '#343a40';
                consentPreview.style.color = '#fff';
            } else {
                consentPreview.style.backgroundColor = '#fff';
                consentPreview.style.color = '#212529';
            }
        });

        // Set initial theme
        if (bannerTheme.value === 'dark') {
            consentPreview.style.backgroundColor = '#343a40';
            consentPreview.style.color = '#fff';
        } else {
            consentPreview.style.backgroundColor = '#fff';
            consentPreview.style.color = '#212529';
        }
    });
</script>
{% endblock %}