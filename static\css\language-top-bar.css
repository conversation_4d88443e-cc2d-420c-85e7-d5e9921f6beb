/**
 * منتقي اللغة في الهيدر - تصميم أنيق ومتطور
 * Header Language Selector - Elegant and Modern Design
 */

/* حاوي منتقي اللغة في الهيدر */
.language-selector-header {
    position: relative;
    display: inline-block;
}

/* زر القائمة المنسدلة المحسن في الهيدر */
.language-dropdown-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 20px;
    border: 1px solid #3b82f6; /* border-blue-500 */
    color: #1d4ed8; /* text-blue-700 */
    background-color: #ffffff; /* bg-white */
    border-radius: 50px; /* rounded-full */
    font-size: 0.875rem; /* text-sm */
    font-weight: 500; /* font-medium */
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    outline: none;
    position: relative;
}

.language-dropdown-btn:hover {
    background-color: #eff6ff; /* hover:bg-blue-50 */
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.language-dropdown-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.language-dropdown-btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #3b82f6;
}

/* تحسين الأيقونة */
.language-dropdown-btn i {
    font-size: 1rem;
    margin-right: 8px;
    color: inherit;
}

/* القائمة المنسدلة المحسنة */
.language-dropdown-menu {
    position: fixed;
    right: 20px;
    top: 80px;
    z-index: 10;
    min-width: 12rem; /* w-48 */
    background-color: #ffffff; /* bg-white */
    border-radius: 0.375rem; /* rounded-md */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
    border: 1px solid rgba(0, 0, 0, 0.05); /* ring-black ring-opacity-5 تأثير مشابه */
    list-style: none;
    margin: 0;
    padding: 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.2s ease-in-out;
    overflow: hidden;
}

.language-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* عناصر القائمة المحسنة */
.language-dropdown-menu li {
    margin: 0;
    padding: 0;
}

.lang-btn-new {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 16px;
    font-size: 0.875rem; /* text-sm */
    color: #374151; /* text-gray-700 */
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    text-align: left;
}

.lang-btn-new:hover {
    background-color: #f3f4f6; /* hover:bg-gray-100 */
}

.lang-btn-new.active-lang-item {
    background-color: #eff6ff; /* bg-blue-50 */
    color: #1d4ed8; /* text-blue-700 */
    font-weight: 600;
}

/* تحسين الأيقونات في القائمة */
.lang-btn-new i {
    font-size: 1rem;
    margin-right: 8px;
    color: inherit;
}

/* النص الأمازيغي */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', 'Tifinagh', sans-serif;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-dropdown-btn {
        padding: 6px 16px;
        font-size: 0.8rem;
    }

    .language-dropdown-btn i {
        font-size: 0.9rem;
        margin-right: 6px;
    }

    .language-dropdown-menu {
        min-width: 10rem;
        right: 15px !important;
        top: 70px !important;
    }

    .lang-btn-new {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .lang-btn-new i {
        font-size: 0.9rem;
        margin-right: 6px;
    }
}

@media (max-width: 480px) {
    .language-dropdown-btn {
        padding: 5px 12px;
        font-size: 0.75rem;
    }

    .language-dropdown-btn i {
        font-size: 0.8rem;
        margin-right: 5px;
    }

    .language-dropdown-menu {
        min-width: 8rem;
        right: 10px !important;
        top: 65px !important;
    }

    .lang-btn-new {
        padding: 5px 10px;
        font-size: 0.75rem;
    }

    .lang-btn-new i {
        font-size: 0.8rem;
        margin-right: 5px;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-dropdown-btn {
        background-color: #374151; /* bg-gray-700 */
        border-color: #6b7280; /* border-gray-500 */
        color: #e5e7eb; /* text-gray-200 */
    }

    .language-dropdown-btn:hover {
        background-color: #4b5563; /* hover:bg-gray-600 */
    }

    .language-dropdown-btn:focus {
        box-shadow: 0 0 0 2px #1f2937, 0 0 0 4px #3b82f6;
    }

    .language-dropdown-menu {
        background-color: #374151; /* bg-gray-700 */
        border-color: rgba(0, 0, 0, 0.1);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    }

    .lang-btn-new {
        color: #e5e7eb; /* text-gray-200 */
    }

    .lang-btn-new:hover {
        background-color: #4b5563; /* hover:bg-gray-600 */
    }

    .lang-btn-new.active-lang-item {
        background-color: #1e40af; /* bg-blue-800 */
        color: #dbeafe; /* text-blue-100 */
    }
}

/* تحسين للطباعة */
@media print {
    .language-selector-header {
        display: none !important;
    }
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .language-dropdown-btn,
    .language-dropdown-btn:hover,
    .language-dropdown-menu,
    .lang-btn-new,
    .lang-btn-new:hover {
        transition: none !important;
        transform: none !important;
    }
}

.lang-btn-simple.active:hover {
    background: white;
    color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* الأيقونات */
.lang-btn-simple i {
    font-size: 0.85rem;
    opacity: 0.9;
}

.lang-btn-simple.active i {
    opacity: 1;
}

/* النص الأمازيغي */
.lang-btn-simple .tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
    font-size: 0.95rem;
}

/* تأثير التحميل */
.lang-btn-simple.loading {
    opacity: 0.7;
    pointer-events: none;
}

.lang-btn-simple.loading::after {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-top-bar {
        padding: 0;
    }
    
    .language-top-bar .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }
    
    .lang-btn-simple {
        padding: 5px 12px;
        font-size: 0.85rem;
    }
    
    .language-switcher-simple {
        gap: 6px;
    }
}

@media (max-width: 480px) {
    .lang-btn-simple {
        padding: 4px 10px;
        font-size: 0.8rem;
    }
    
    .lang-btn-simple i {
        font-size: 0.75rem;
    }
    
    .language-switcher-simple {
        gap: 4px;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-top-bar {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .lang-btn-simple {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.15);
    }
    
    .lang-btn-simple:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .lang-btn-simple.active {
        background: rgba(255, 255, 255, 0.95);
        color: #2d3748;
    }
}

/* تحسين للطباعة */
@media print {
    .language-top-bar {
        display: none !important;
    }
}

/* تحسين إمكانية الوصول */
.lang-btn-simple:focus {
    outline: 2px solid rgba(255, 255, 255, 0.8);
    outline-offset: 2px;
}

.lang-btn-simple.active:focus {
    outline: 2px solid #667eea;
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple,
    .lang-btn-simple:hover,
    .lang-btn-simple.active {
        transition: none !important;
        transform: none !important;
    }
    
    .lang-btn-simple.loading::after {
        animation: none !important;
    }
}

/* تأثير النبضة للزر النشط */
.lang-btn-simple.active {
    animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    0% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    100% {
        box-shadow: 0 2px 12px rgba(102, 126, 234, 0.3);
    }
}

@media (prefers-reduced-motion: reduce) {
    .lang-btn-simple.active {
        animation: none !important;
    }
}

/* تحسين التباعد */
.language-top-bar .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 1200px) {
    .language-top-bar .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* تأثير التدرج المتحرك */
.language-top-bar {
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@media (prefers-reduced-motion: reduce) {
    .language-top-bar {
        animation: none !important;
        background-size: 100% 100%;
    }
}
