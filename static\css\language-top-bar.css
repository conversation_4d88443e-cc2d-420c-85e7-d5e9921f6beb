/**
 * زر تغيير اللغة - Bootstrap 5.3.3 Style
 * Language Switcher - Bootstrap 5.3.3 Compatible
 */

/* حاوي زر اللغة في الهيدر - يطفو فوق كل شيء */
.language-selector-header {
    position: relative;
    display: inline-block;
    z-index: 1052; /* أعلى من القائمة المنسدلة */
}

/* الدروب داون نفسه */
.language-selector-header .dropdown {
    position: relative;
    z-index: 1052;
}

/* تأكيد أن الهيدر له z-index أقل */
header.bg-white,
.navbar {
    z-index: 1000;
}

/* زر تغيير اللغة - Bootstrap Style مع z-index عالي */
.language-dropdown-btn {
    min-width: 140px;
    transition: all 0.2s ease-in-out;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    position: relative;
    z-index: 1052; /* يطفو فوق الهيدر */
}

/* تحسين عرض الأيقونة والنص */
.language-dropdown-btn i {
    margin-right: 0.5rem;
}

/* إزالة outline عند التركيز */
.language-dropdown-btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    z-index: 1053; /* أعلى عند التركيز */
}

/* القائمة المنسدلة - Bootstrap Style مع z-index عالي جداً */
.language-dropdown-menu {
    z-index: 1055 !important; /* أعلى من الزر */
    min-width: 192px !important;
    border: none !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    position: absolute !important; /* تأكيد الموضع */
    top: 100% !important;
    left: auto !important;
    right: 0 !important;
    transform: none !important;
    margin-top: 0.125rem !important;
}

/* عند فتح القائمة */
.language-dropdown-menu.show {
    z-index: 1056 !important; /* أعلى قيمة عند الفتح */
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* عناصر القائمة */
.language-dropdown-menu .dropdown-item {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* أيقونة التحديد */
.language-dropdown-menu .check-icon {
    width: 16px;
    margin-right: 8px;
    color: #0d6efd;
}

.language-dropdown-menu .check-icon.invisible {
    visibility: hidden;
}

/* خط تيفيناغ للنص الأمازيغي */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-dropdown-btn {
        min-width: 120px;
        font-size: 0.875rem;
    }

    .language-dropdown-menu {
        min-width: 160px;
    }
}

@media (max-width: 480px) {
    .language-dropdown-btn {
        min-width: 100px;
        font-size: 0.8rem;
        z-index: 1052; /* الحفاظ على z-index في الشاشات الصغيرة */
    }

    .language-dropdown-menu {
        min-width: 140px;
        z-index: 1055 !important; /* الحفاظ على z-index في الشاشات الصغيرة */
    }
}

/* تأكيد إضافي للطفو فوق جميع العناصر */
.dropdown-menu.language-dropdown-menu {
    z-index: 1055 !important;
}

.dropdown-menu.language-dropdown-menu.show {
    z-index: 1056 !important;
}

/* تأكيد أن عناصر الهيدر الأخرى لها z-index أقل */
.navbar-brand,
.navbar-nav,
.navbar-toggler {
    z-index: 999;
}






