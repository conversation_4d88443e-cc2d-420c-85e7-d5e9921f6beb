/**
 * زر تغيير اللغة - Bootstrap 5.3.3 Style
 * Language Switcher - Bootstrap 5.3.3 Compatible
 */

/* حاوي زر اللغة في الهيدر */
.language-selector-header {
    position: relative;
    display: inline-block;
    z-index: 1001;
}

/* تأكيد أن الهيدر له z-index أقل */
header.bg-white,
.navbar {
    z-index: 1000;
}

/* زر تغيير اللغة - Bootstrap Style */
.language-dropdown-btn {
    min-width: 140px;
    transition: all 0.2s ease-in-out;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* تحسين عرض الأيقونة والنص */
.language-dropdown-btn i {
    margin-right: 0.5rem;
}

/* إزالة outline عند التركيز */
.language-dropdown-btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* القائمة المنسدلة - Bootstrap Style */
.language-dropdown-menu {
    z-index: 1051 !important;
    min-width: 192px;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* عناصر القائمة */
.language-dropdown-menu .dropdown-item {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* أيقونة التحديد */
.language-dropdown-menu .check-icon {
    width: 16px;
    margin-right: 8px;
    color: #0d6efd;
}

.language-dropdown-menu .check-icon.invisible {
    visibility: hidden;
}

/* خط تيفيناغ للنص الأمازيغي */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-dropdown-btn {
        min-width: 120px;
        font-size: 0.875rem;
    }

    .language-dropdown-menu {
        min-width: 160px;
    }
}

@media (max-width: 480px) {
    .language-dropdown-btn {
        min-width: 100px;
        font-size: 0.8rem;
    }

    .language-dropdown-menu {
        min-width: 140px;
    }
}






