/**
 * زر تغيير اللغة البسيط - متوافق مع ستايل الموقع
 * Simple Language Switcher - Compatible with Site Style
 */

/* حاوي زر اللغة في الهيدر */
.language-selector-header {
    position: relative;
    display: inline-block;
}

/* زر تغيير اللغة البسيط */
.language-dropdown-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    color: #495057;
    background-color: #ffffff;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    outline: none;
    min-width: 120px;
}

.language-dropdown-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    color: #212529;
}

.language-dropdown-btn:focus {
    outline: none;
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.language-dropdown-btn:active {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

/* أيقونة الكرة الأرضية */
.language-dropdown-btn i {
    font-size: 1rem;
    margin-right: 0.5rem;
    color: inherit;
}

/* القائمة المنسدلة البسيطة */
.language-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    min-width: 160px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    list-style: none;
    margin: 0.125rem 0 0;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-0.5rem);
    transition: all 0.15s ease-in-out;
}

.language-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* عناصر القائمة */
.language-dropdown-menu li {
    margin: 0;
    padding: 0;
}

.lang-btn-new {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #495057;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    text-align: left;
}

.lang-btn-new:hover {
    background-color: #f8f9fa;
    color: #212529;
}

.lang-btn-new.active-lang-item {
    background-color: #e7f1ff;
    color: #0d6efd;
    font-weight: 600;
}

/* أيقونات القائمة */
.lang-btn-new i {
    font-size: 1rem;
    margin-right: 0.5rem;
    color: inherit;
}

/* النص الأمازيغي */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', 'Tifinagh', sans-serif;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .language-dropdown-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
        min-width: 100px;
    }

    .language-dropdown-btn i {
        font-size: 0.9rem;
        margin-right: 0.375rem;
    }

    .language-dropdown-menu {
        min-width: 140px;
        right: 0;
    }

    .lang-btn-new {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .lang-btn-new i {
        font-size: 0.9rem;
        margin-right: 0.375rem;
    }
}

@media (max-width: 480px) {
    .language-dropdown-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        min-width: 90px;
    }

    .language-dropdown-btn i {
        font-size: 0.8rem;
        margin-right: 0.25rem;
    }

    .language-dropdown-menu {
        min-width: 120px;
    }

    .lang-btn-new {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .lang-btn-new i {
        font-size: 0.8rem;
        margin-right: 0.25rem;
    }
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .language-dropdown-btn {
        background-color: #495057;
        border-color: #6c757d;
        color: #f8f9fa;
    }

    .language-dropdown-btn:hover {
        background-color: #5a6268;
        border-color: #6c757d;
    }

    .language-dropdown-btn:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .language-dropdown-menu {
        background-color: #495057;
        border-color: #6c757d;
    }

    .lang-btn-new {
        color: #f8f9fa;
    }

    .lang-btn-new:hover {
        background-color: #5a6268;
    }

    .lang-btn-new.active-lang-item {
        background-color: #0d6efd;
        color: #ffffff;
    }
}

/* تحسين للطباعة */
@media print {
    .language-selector-header {
        display: none !important;
    }
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    .language-dropdown-btn,
    .language-dropdown-btn:hover,
    .language-dropdown-menu,
    .lang-btn-new,
    .lang-btn-new:hover {
        transition: none !important;
        transform: none !important;
    }
}

/* تأثير التحميل للزر */
.language-dropdown-btn.loading {
    opacity: 0.7;
    pointer-events: none;
}

.language-dropdown-btn.loading::after {
    content: '';
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


