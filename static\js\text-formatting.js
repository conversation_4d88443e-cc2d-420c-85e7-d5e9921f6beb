/**
 * Text formatting functionality for the Tifinagh Converter
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const latinText = document.getElementById('latin-text');
    const tifinaghText = document.getElementById('tifinagh-text');
    const fontSizeValue = document.getElementById('font-size-value');
    const decreaseFontBtn = document.getElementById('decrease-font');
    const increaseFontBtn = document.getElementById('increase-font');
    const alignLeftBtn = document.getElementById('align-left');
    const alignCenterBtn = document.getElementById('align-center');
    const alignRightBtn = document.getElementById('align-right');
    const alignJustifyBtn = document.getElementById('align-justify');
    const downloadBtn = document.getElementById('download-tifinagh');

    // Font size settings
    const fontSizes = [12, 14, 16, 18, 20, 24, 28, 32];
    let currentFontSizeIndex = 2; // Start with 16px (index 2)

    // Initialize
    updateFontSize();

    // Event listeners for font size controls
    if (decreaseFontBtn) {
        decreaseFontBtn.addEventListener('click', () => {
            if (currentFontSizeIndex > 0) {
                currentFontSizeIndex--;
                updateFontSize();
            }
        });
    }

    if (increaseFontBtn) {
        increaseFontBtn.addEventListener('click', () => {
            if (currentFontSizeIndex < fontSizes.length - 1) {
                currentFontSizeIndex++;
                updateFontSize();
            }
        });
    }

    // Event listeners for text alignment controls
    if (alignLeftBtn) {
        alignLeftBtn.addEventListener('click', () => {
            setTextAlignment('left');
        });
    }

    if (alignCenterBtn) {
        alignCenterBtn.addEventListener('click', () => {
            setTextAlignment('center');
        });
    }

    if (alignRightBtn) {
        alignRightBtn.addEventListener('click', () => {
            setTextAlignment('right');
        });
    }

    if (alignJustifyBtn) {
        alignJustifyBtn.addEventListener('click', () => {
            setTextAlignment('justify');
        });
    }

    // Download functionality
    if (downloadBtn) {
        downloadBtn.addEventListener('click', () => {
            downloadTifinaghText();
        });
    }

    // Function to update font size
    function updateFontSize() {
        const fontSize = fontSizes[currentFontSizeIndex];
        
        if (latinText) {
            latinText.style.fontSize = `${fontSize}px`;
        }
        
        if (tifinaghText) {
            tifinaghText.style.fontSize = `${fontSize}px`;
        }
        
        if (fontSizeValue) {
            fontSizeValue.textContent = `${fontSize}px`;
        }
    }

    // Function to set text alignment
    function setTextAlignment(alignment) {
        // Remove active class from all alignment buttons
        [alignLeftBtn, alignCenterBtn, alignRightBtn, alignJustifyBtn].forEach(btn => {
            if (btn) btn.classList.remove('active');
        });

        // Add active class to the selected alignment button
        let activeBtn;
        switch (alignment) {
            case 'left':
                activeBtn = alignLeftBtn;
                break;
            case 'center':
                activeBtn = alignCenterBtn;
                break;
            case 'right':
                activeBtn = alignRightBtn;
                break;
            case 'justify':
                activeBtn = alignJustifyBtn;
                break;
        }
        if (activeBtn) activeBtn.classList.add('active');

        // Remove all alignment classes from text areas
        if (latinText) {
            latinText.classList.remove('text-align-left', 'text-align-center', 'text-align-right', 'text-align-justify');
            latinText.classList.add(`text-align-${alignment}`);
        }
        
        if (tifinaghText) {
            tifinaghText.classList.remove('text-align-left', 'text-align-center', 'text-align-right', 'text-align-justify');
            tifinaghText.classList.add(`text-align-${alignment}`);
        }
    }

    // Function to download Tifinagh text
    function downloadTifinaghText() {
        if (!tifinaghText || !tifinaghText.textContent) return;
        
        const text = tifinaghText.textContent;
        const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'tifinagh-text.txt';
        document.body.appendChild(a);
        a.click();
        
        // Clean up
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 100);
    }

    // Ensure the Tifinagh text preserves the formatting of the Latin text
    const originalConvertText = window.convertText;
    if (typeof originalConvertText === 'function') {
        window.convertText = function() {
            originalConvertText();
            
            // Preserve whitespace and line breaks
            if (latinText && tifinaghText) {
                // Count leading spaces and preserve them
                const leadingSpaces = (latinText.value.match(/^\s*/) || [''])[0];
                const leadingSpacesCount = leadingSpaces.length;
                
                if (leadingSpacesCount > 0) {
                    tifinaghText.innerHTML = ' '.repeat(leadingSpacesCount) + tifinaghText.innerHTML.trimStart();
                }
                
                // Preserve line breaks
                const latinLines = latinText.value.split('\n');
                const tifinaghContent = tifinaghText.textContent;
                
                if (latinLines.length > 1) {
                    let formattedContent = '';
                    let currentPos = 0;
                    
                    for (let i = 0; i < latinLines.length; i++) {
                        const lineLength = latinLines[i].length;
                        if (lineLength === 0 && i < latinLines.length - 1) {
                            formattedContent += '\n';
                        } else if (currentPos + lineLength <= tifinaghContent.length) {
                            formattedContent += tifinaghContent.substr(currentPos, lineLength);
                            if (i < latinLines.length - 1) {
                                formattedContent += '\n';
                            }
                            currentPos += lineLength;
                        }
                    }
                    
                    if (formattedContent) {
                        tifinaghText.textContent = formattedContent;
                    }
                }
            }
        };
    }
});
