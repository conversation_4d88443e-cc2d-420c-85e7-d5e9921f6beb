/**
 * Latin to Tifinagh text translation functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    const sourceText = document.getElementById('latin-text');
    const targetText = document.getElementById('tifinagh-text');
    const clearBtn = document.getElementById('clear-latin');
    const copyBtn = document.getElementById('copy-tifinagh');
    const downloadBtn = document.getElementById('download-tifinagh');
    const sourceCharCounter = document.querySelector('.latin-area .character-count');
    const targetCharCounter = document.querySelector('.tifinagh-area .character-count');

    if (!sourceText || !targetText) return;

    // Convert text as user types
    sourceText.addEventListener('input', debounce(function() {
        // Check if input contains table markup
        const inputText = sourceText.value;

        // Process tables if detected
        if (inputText.includes('<table') || inputText.includes('|') && (inputText.includes('--') || inputText.includes('-:') || inputText.includes(':-'))) {
            console.log('Table detected in input');
            // Make sure we use innerHTML for the output to properly display tables
            targetText.classList.add('contains-table');
        } else {
            targetText.classList.remove('contains-table');
        }

        updateCharCounter();
        convertText();
    }, 300));

    // Clear button functionality
    if (clearBtn) {
        clearBtn.addEventListener('click', () => {
            sourceText.value = '';
            targetText.textContent = '';
            updateCharCounter();
        });
    }

    // Copy button functionality
    if (copyBtn) {
        copyBtn.addEventListener('click', () => {
            // Check if we have tables or HTML content
            let text;
            if (targetText.classList.contains('contains-table')) {
                // For tables, we need to get the HTML content
                text = targetText.innerHTML;

                // Create a temporary element to extract text from HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = text;

                // Get plain text for clipboard
                text = tempDiv.innerText || tempDiv.textContent;
            } else {
                // For regular text
                text = targetText.textContent;
            }

            if (!text) return;

            navigator.clipboard.writeText(text)
                .then(() => {
                    // Show success feedback using Bootstrap tooltip
                    const originalHTML = copyBtn.innerHTML;
                    copyBtn.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                        </svg>`;

                    setTimeout(() => {
                        copyBtn.innerHTML = originalHTML;
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy text: ', err);
                });
        });
    }

    // Download button functionality
    if (downloadBtn) {
        downloadBtn.addEventListener('click', () => {
            // Check if we have tables or HTML content
            let text;
            let fileType = 'text/plain;charset=utf-8';
            let fileName = 'tifinagh-text.txt';

            if (targetText.classList.contains('contains-table')) {
                // For tables, we can download as HTML to preserve formatting
                text = '<!DOCTYPE html>\n<html>\n<head>\n<meta charset="UTF-8">\n';
                text += '<style>\n';
                text += 'table { border-collapse: collapse; width: 100%; margin: 1rem 0; }\n';
                text += 'th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n';
                text += 'th { background-color: #f2f2f2; }\n';
                text += '.tifinagh-text { font-family: "Noto Sans Tifinagh", Arial, sans-serif; }\n';
                text += '</style>\n</head>\n<body>\n';
                text += '<div class="tifinagh-text">\n';
                text += targetText.innerHTML;
                text += '\n</div>\n</body>\n</html>';
                fileType = 'text/html;charset=utf-8';
                fileName = 'tifinagh-text.html';
            } else {
                // For regular text
                text = targetText.textContent;
            }

            if (!text) return;

            // Create a Blob with the text content
            const blob = new Blob([text], { type: fileType });
            const url = URL.createObjectURL(blob);

            // Create a temporary link element and trigger download
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = fileName;

            // Add to document, click and clean up
            document.body.appendChild(a);
            a.click();

            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                // Show success feedback
                const originalHTML = downloadBtn.innerHTML;
                downloadBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                    </svg>`;

                setTimeout(() => {
                    downloadBtn.innerHTML = originalHTML;
                }, 2000);
            }, 100);
        });
    }

    // Update character counter
    function updateCharCounter() {
        // Update source text character counter
        if (sourceCharCounter) {
            const sourceLength = sourceText.value.length;
            sourceCharCounter.textContent = `${sourceLength} characters`;

            // Add warning class if approaching limit
            if (sourceLength > 4500) {
                sourceCharCounter.classList.add('warning');
            } else {
                sourceCharCounter.classList.remove('warning');
            }

            // استدعاء وظيفة ضبط ارتفاع المحرر إذا كانت متاحة
            if (window.editorHeightControl && typeof window.editorHeightControl.adjustEditorHeight === 'function') {
                window.editorHeightControl.adjustEditorHeight();
            }
        }

        // Update target text character counter
        if (targetCharCounter && targetText) {
            let targetLength;

            if (targetText.classList.contains('contains-table')) {
                // For HTML content, create a temporary element to get text length
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = targetText.innerHTML;
                targetLength = (tempDiv.innerText || tempDiv.textContent).length;
            } else {
                targetLength = targetText.textContent.length;
            }

            targetCharCounter.textContent = `${targetLength} characters`;

            // Add warning class if approaching limit
            if (targetLength > 4500) {
                targetCharCounter.classList.add('warning');
            } else {
                targetCharCounter.classList.remove('warning');
            }
        }
    }

    // Function to convert Latin text to Tifinagh
    function convertText() {
        const latinText = sourceText.value;
        if (!latinText) {
            targetText.textContent = '';
            return;
        }

        // Improved table detection
        const hasTable = latinText.includes('<table') ||
                        latinText.includes('<tr') ||
                        latinText.includes('<td') ||
                        latinText.includes('<th') ||
                        (latinText.includes('|') &&
                         (latinText.includes('--') ||
                          latinText.includes('-:') ||
                          latinText.includes(':-') ||
                          (latinText.match(/\|/g) || []).length >= 4)); // At least 2 rows with 2 pipe characters each

        if (hasTable) {
            targetText.classList.add('contains-table');
            console.log('Table detected in input');
        }

        // Show loading indicator
        targetText.textContent = 'Converting...';

        // Call the API to convert the text
        fetch('/api/convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: latinText,
                hasTable: hasTable
            }),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.result) {
                throw new Error('Invalid response from server');
            }

            // Check if the result contains HTML table tags
            const containsTable = data.result.includes('<table') ||
                data.result.includes('<tr') ||
                data.result.includes('<td') ||
                data.result.includes('<th') ||
                data.result.includes('|--') ||
                data.result.includes('|-:') ||
                targetText.classList.contains('contains-table');

            if (containsTable) {
                // If it contains HTML tables, use innerHTML instead of textContent
                targetText.innerHTML = data.result;

                // Add special styling to tables if needed
                const tables = targetText.querySelectorAll('table');
                tables.forEach(table => {
                    table.classList.add('converted-table');
                    table.setAttribute('border', '1');
                    table.setAttribute('cellpadding', '5');
                    table.setAttribute('cellspacing', '0');
                });

                console.log('Table rendered in output');
            } else {
                // Otherwise use textContent for security
                targetText.textContent = data.result;
            }

            // Update character counter after text conversion
            updateCharCounter();

            // استدعاء وظيفة ضبط ارتفاع المحرر بعد تحويل النص
            if (window.editorHeightControl && typeof window.editorHeightControl.adjustEditorHeight === 'function') {
                window.editorHeightControl.adjustEditorHeight();
            }
        })
        .catch(error => {
            console.error('Error converting text:', error);
            targetText.textContent = 'Error converting text: ' + error.message;
        });
    }

    // Expose convertText function globally for other scripts to use
    window.convertText = convertText;

    // Detect language
    function detectLanguage() {
        const latinText = sourceText.value;
        if (!latinText || latinText.length < 5) return;

        fetch('/api/detect-language', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: latinText }),
        })
        .then(response => response.json())
        .then(data => {
            // Update source language if detected
            console.log('Detected language:', data.detected_language);
        })
        .catch(error => {
            console.error('Error detecting language:', error);
        });
    }

    // Debounce function to limit API calls
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func.apply(context, args);
            }, wait);
        };
    }

    // Initial update if there's text
    if (sourceText.value) {
        updateCharCounter();
        convertText();
    }
});
