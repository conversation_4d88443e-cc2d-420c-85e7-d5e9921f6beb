/*
 * Custom styles for Tifinagh Converter - Bootstrap Edition
 */

/*
 * Custom styles for Tifinagh Converter - Bootstrap Edition
 */

:root {
    --bs-primary-rgb: 79, 70, 229;
    --bs-primary: #4f46e5;
    --bs-primary-hover: #4338ca;

    /* Legacy variables needed for compatibility */
    --primary-color: #4f46e5;
    --primary-rgb: 79, 70, 229;
    --primary-hover: #4338ca;
    --text-color: #111827;
    --text-light: #6b7280;
    --bg-color: #ffffff;
    --bg-light: #f9fafb;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition-speed: 0.3s;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--text-color);
    line-height: 1.5;
    background-color: var(--bg-color);
    margin: 0;
    padding: 0;
}

/* Custom styles for Bootstrap components */

/* Header styles with Bootstrap */
.navbar {
    padding: 0.75rem 0;
    box-shadow: var(--shadow-sm);
}

.navbar-brand {
    display: flex;
    align-items: center;
}

.navbar-brand img {
    max-height: 60px;
}

/* تم إزالة تنسيقات النص الوصفي من الهيدر */

.navbar-nav .nav-link {
    color: var(--text-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    border-radius: 0.25rem;
    margin: 0 0.125rem;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.05);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.1);
}

/* Language selector styles - Enhanced */
.language-selector .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    background-color: transparent;
    border-color: var(--border-color);
}

.language-selector .btn:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
    border-color: var(--primary-color);
}

.language-selector .btn .bi-globe2 {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.language-selector .dropdown-menu {
    min-width: 10rem;
    padding: 0.5rem 0;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.language-selector .dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.language-selector .dropdown-item:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
    border-left-color: var(--primary-color);
}

.language-selector .dropdown-item.active {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.language-selector .dropdown-item .bi {
    color: var(--primary-color);
    font-size: 1.1rem;
}

.language-selector .dropdown-item .tifinagh-text {
    color: var(--primary-color);
}

.language-selector .dropdown-item.disabled {
    color: var(--text-light);
    opacity: 0.7;
    cursor: not-allowed;
}

/* تم إزالة دعم RTL لأن اللغة الأمازيغية تُكتب من اليسار إلى اليمين مثل الإنجليزية */

/* Tifinagh font for specific elements */
.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
    font-size: 1.2rem;
}

/* Converter container styles using Bootstrap */
.converter-container {
    min-height: 400px;
}

/* Tab styles with Bootstrap */
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
}

.nav-tabs .nav-link:hover {
    color: var(--bs-primary);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    color: var(--bs-primary);
    border-bottom-color: var(--bs-primary);
    background-color: transparent;
}

.tab-content > .tab-pane {
    transition: opacity 0.3s ease;
}

.language-headers {
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    width: 100%;
}

.language-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.latin-header {
    flex: 1;
    color: var(--primary-color);
}

.tifinagh-header {
    flex: 1;
    justify-content: flex-end;
    text-align: right;
}

.swap-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.swap-btn {
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-light);
}

.text-areas-container {
    display: flex;
    width: 100%;
    margin: 0 auto;
    padding: 1.5rem;
    box-sizing: border-box;
}

.text-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 50%;
    box-sizing: border-box;
}

.latin-area {
    border-right: 1px solid var(--border-color);
}

/* تم نقل تنسيقات المحررين إلى ملف editors.css */

/* تم نقل تنسيقات الجداول في المحررين إلى ملف editors.css */

/* تم نقل تنسيقات المحررين وأزرار التحكم إلى ملف editors.css */

/* Info container styles */
.info-container {
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background-color: #e0f2fe;
    border-radius: 0.375rem;
    color: #0369a1;
    width: 100%;
    box-sizing: border-box;
    max-width: 1200px;
}

.info-icon {
    color: #0284c7;
    flex-shrink: 0;
}

.info-text {
    flex: 1;
}

/* Feature section styles */
.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 2rem;
}

.feature-card {
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    height: 100%;
    transition: transform 0.2s, box-shadow 0.2s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.text-icon {
    background-color: #ede9fe;
}

.file-icon {
    background-color: #e0f2fe;
}

.web-icon {
    background-color: #dcfce7;
}

.feature-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.feature-description {
    color: var(--text-light);
    font-size: 0.875rem;
}

/* Footer styles - Enhanced with Animation */
footer.bg-dark {
    background: linear-gradient(135deg, #1e293b, #0f172a) !important;
    color: #f8fafc;
    padding: 3rem 0 1.5rem;
    margin-top: 3rem;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

footer.bg-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #3b82f6, #8b5cf6, var(--primary-color));
    background-size: 300% 100%;
    animation: gradientAnimation 6s ease infinite;
}

footer h3.h5, footer h4.h6 {
    color: #fff;
    font-weight: 600;
    position: relative;
    display: inline-block;
    margin-bottom: 1.25rem;
}

footer h3.h5::after, footer h4.h6::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

footer .text-muted {
    color: #94a3b8 !important;
    transition: color 0.3s ease;
}

footer a.text-decoration-none {
    position: relative;
    transition: all 0.3s ease;
    padding-left: 0;
}

footer a.text-decoration-none:hover {
    color: #fff !important;
    padding-left: 5px;
}

footer a.text-decoration-none:hover .bi {
    color: var(--primary-color);
    transform: scale(1.2);
}

footer .bi {
    transition: all 0.3s ease;
}

footer hr.border-secondary {
    border-color: rgba(255, 255, 255, 0.1) !important;
    margin: 2rem 0;
}

footer .text-center p {
    font-size: 0.875rem;
    color: #94a3b8;
}

/* Privacy notice styling */
footer .bi-shield-check {
    color: #10b981 !important;
    font-size: 1.2rem;
}

/* Contact icons styling with pulse effect */
footer .bi-envelope, footer .bi-translate {
    color: var(--primary-color) !important;
    font-size: 1.1rem;
    transition: transform 0.3s ease, color 0.3s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

footer .d-flex:hover .bi-envelope,
footer .d-flex:hover .bi-translate {
    animation: pulse 1.5s ease infinite;
    color: #3b82f6 !important;
}

/* Quick links icons styling */
footer .list-unstyled .bi {
    color: var(--primary-color);
    opacity: 0.8;
    transition: all 0.3s ease;
}

footer .list-unstyled a:hover .bi {
    opacity: 1;
    transform: translateX(3px);
}

/* Copyright section with glow effect */
footer #current-year {
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    text-shadow: 0 0 5px rgba(79, 70, 229, 0.3);
}

footer .text-center p:hover #current-year {
    animation: glow 1.5s ease infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 5px rgba(79, 70, 229, 0.3); }
    to { text-shadow: 0 0 10px rgba(79, 70, 229, 0.7); }
}

/* File Upload Area styles with Bootstrap */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 3rem 2rem;
    text-align: center;
    transition: border-color 0.2s;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--bs-primary);
}

/* Progress bar customization */
.progress {
    height: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
}

.progress-bar-striped {
    background-size: 1rem 1rem;
}

/* File upload icon */
.file-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 5rem;
    height: 5rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

/* File Convert Button with Icon and Text */
.file-convert-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    width: auto !important;
    height: auto !important;
    min-width: 120px;
}

.file-convert-btn svg {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

.file-convert-btn span {
    font-size: 1rem;
    display: inline-block;
    white-space: nowrap;
}

.file-convert-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
}

.file-convert-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.file-convert-btn:disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.file-convert-btn.success {
    background-color: #10b981;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

/* Spinning animation for loading state */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1.5s linear infinite;
}

.file-convert-btn .spin,
.convert-btn .spin {
    animation: spin 1.5s linear infinite;
}

/* Website Converter styles */
.web-converter-container {
    padding: 1.5rem;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
    max-width: 1200px;
}

.url-form {
    margin: 0 auto 1.5rem;
    width: 100%;
    padding: 0;
    max-width: 1200px;
}

.url-input-container {
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.url-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 1rem;
    background-color: var(--bg-light);
    color: var(--text-light);
    height: 100%;
}

.url-input-container input {
    flex: 1;
    border: none;
    padding: 0.75rem 1rem;
    outline: none;
    font-size: 1rem;
}

.load-website-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    white-space: nowrap;
}

.load-website-btn:hover {
    background-color: var(--primary-hover);
}

.website-container {
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
    max-width: 1200px;
    opacity: 1;
    transition: opacity 0.3s ease;
    position: relative;
}

.website-container-hidden {
    display: block;
    opacity: 0;
    height: 0;
    overflow: hidden;
    pointer-events: none;
}

.website-controls {
    display: flex;
    justify-content: space-between;
    margin: 0 auto 1.5rem;
    width: 100%;
    max-width: 1200px;
}

.toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.toggle-btn:hover {
    background-color: var(--primary-hover);
}

.refresh-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--bg-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.refresh-btn:hover {
    background-color: white;
}

.website-preview-container {
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    max-width: 1200px;
    position: relative;
}

.website-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.website-loading p {
    color: var(--text-light);
    font-size: 1rem;
}

.website-preview {
    display: none;
    flex-direction: column;
    width: 100%;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: white;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.preview-url {
    font-size: 0.875rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60%;
}

.preview-actions {
    display: flex;
    gap: 0.5rem;
}

.toggle-tifinagh-btn, .open-website-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.toggle-tifinagh-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.toggle-tifinagh-btn:hover {
    background-color: var(--primary-hover);
}

.toggle-tifinagh-btn.active {
    background-color: #dc3545;
}

.open-website-btn {
    background-color: var(--bg-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.open-website-btn:hover {
    background-color: white;
}

.preview-frame-container {
    height: 500px;
    width: 100%;
    position: relative;
    background-color: white;
}

.preview-frame-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: white;
}

/* Alternative conversion styles */
.alternative-conversion {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-light);
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
}

.alternative-conversion h3 {
    margin-bottom: 0.75rem;
    color: var(--primary-color);
    font-size: 1.25rem;
}

.alternative-conversion p {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.alternative-conversion-content {
    display: flex;
    flex-direction: column;
}

.alternative-conversion textarea {
    height: 150px;
    margin-bottom: 1rem;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    font-family: inherit;
    resize: vertical;
}

.alternative-conversion .convert-btn {
    align-self: flex-start;
    margin-bottom: 1.5rem;
}

.result-container {
    margin-top: 1rem;
}

.result-container h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-size: 1rem;
}

.result-container #web-tifinagh-text {
    min-height: 150px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: white;
}

/* Spinning animation for loading state */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 1.5s linear infinite;
}

/* RTL support for extra elements */
[dir="rtl"] .text-start {
    text-align: right !important;
}

[dir="rtl"] .text-end {
    text-align: left !important;
}

/* تم نقل تنسيقات المحررين والجداول إلى ملف editors.css */

/* Enhanced Card styles */
.card {
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background-color: rgba(var(--primary-rgb), 0.05);
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.card-header h5,
.card-header .h5 {
    margin-bottom: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--bg-light);
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

/* Enhanced Button styles */
.btn {
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover,
.btn-outline-primary:focus {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 0 0.25rem rgba(var(--primary-rgb), 0.25);
}

.btn-success {
    background-color: #10b981;
    border-color: #10b981;
}

.btn-success:hover,
.btn-success:focus {
    background-color: #059669;
    border-color: #059669;
    box-shadow: 0 0 0 0.25rem rgba(16, 185, 129, 0.25);
}

.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
}

/* Home page enhancements - Conversion Showcase */
.conversion-showcase {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 20px;
    position: relative;
    margin-bottom: 20px;
}

.conversion-card {
    flex: 1;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    max-width: 220px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.conversion-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 12px;
    background: rgba(0, 0, 0, 0.02);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    text-align: center;
}

.card-content {
    padding: 20px;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.sample-text {
    margin: 8px 0;
    font-size: 1.1rem;
    line-height: 1.5;
}

.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
    font-size: 1.3rem;
}

.latin-card {
    background: linear-gradient(to bottom right, #ffffff, #f8f9fa);
}

.tifinagh-card {
    background: linear-gradient(to bottom right, #ffffff, #f0f9ff);
}

.conversion-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    position: relative;
    z-index: 2;
    width: 50px;
    height: 50px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Add a subtle background pattern */
.conversion-showcase::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25px 25px, rgba(var(--primary-rgb), 0.05) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(var(--primary-rgb), 0.05) 2px, transparent 0);
    background-size: 100px 100px;
    border-radius: 12px;
    z-index: 0;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .conversion-showcase {
        flex-direction: column;
        gap: 30px;
    }

    .conversion-arrow {
        transform: rotate(90deg);
    }

    .conversion-card {
        max-width: 100%;
    }
}

/* Feature cards */
.feature-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    overflow: hidden;
}

.feature-icon-container {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon-bg {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon-bg {
    transform: scale(1.1) rotate(5deg);
}

/* Stats cards */
.stats-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    overflow: hidden;
}

.stats-icon-container {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Gradient background for CTA */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
}

/* Z-index utility */
.z-1 {
    z-index: 1;
}

/* Animation classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate__fadeInUp {
    animation: fadeInUp 0.5s ease forwards;
}

.animate__animated {
    animation-duration: 1s;
    animation-fill-mode: both;
}