<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Language Switcher Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .status { font-weight: bold; font-size: 18px; margin-bottom: 10px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; border-radius: 4px; }
        .test-element { margin: 10px 0; padding: 10px; border: 1px solid #ccc; border-radius: 4px; }
        .live-demo { background: #e9ecef; padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🌐 Clean Language Switcher Test Suite</h1>
    <p><strong>Testing the rebuilt language switching system</strong></p>
    
    <div class="test-section">
        <h2>1. System Status Check</h2>
        <button class="btn-primary" onclick="checkSystemStatus()">Check System Status</button>
        <div id="system-status" class="result">Click button to check system status...</div>
    </div>
    
    <div class="test-section">
        <h2>2. Language Switcher Availability</h2>
        <button class="btn-primary" onclick="checkLanguageSwitcher()">Check Language Switcher</button>
        <div id="switcher-status" class="result">Click button to check language switcher...</div>
    </div>
    
    <div class="test-section">
        <h2>3. Manual Language Testing</h2>
        <button class="btn-success" onclick="testSwitchToEnglish()">Switch to English</button>
        <button class="btn-success" onclick="testSwitchToAmazigh()">Switch to Amazigh</button>
        <button class="btn-warning" onclick="getCurrentLanguage()">Get Current Language</button>
        <div id="manual-test" class="result">Click buttons to test manual language switching...</div>
    </div>
    
    <div class="test-section">
        <h2>4. Button Detection & Event Testing</h2>
        <button class="btn-primary" onclick="testLanguageButtons()">Test Language Buttons</button>
        <div id="button-test" class="result">Click button to test language buttons...</div>
    </div>
    
    <div class="test-section">
        <h2>5. Translation System Integration</h2>
        <button class="btn-primary" onclick="testTranslationIntegration()">Test Translation Integration</button>
        <div id="translation-test" class="result">Click button to test translation integration...</div>
    </div>
    
    <div class="test-section">
        <h2>6. Live Translation Demo</h2>
        <div class="live-demo">
            <h3>Live Translation Elements:</h3>
            <p><strong>English:</strong> <span data-i18n="common.english">English</span></p>
            <p><strong>Amazigh:</strong> <span data-i18n="common.amazigh">ⵜⴰⵎⴰⵣⵉⵖⵜ</span></p>
            <p><strong>App Name:</strong> <span data-i18n="common.app_name">Tifinagh Converter</span></p>
            <p><strong>Language:</strong> <span data-i18n="common.language">Language</span></p>
        </div>
        <button class="btn-success" onclick="updateLiveDemo()">Update Live Demo</button>
        <div id="live-demo-result" class="result">Live demo elements above should change when language switches</div>
    </div>
    
    <div class="test-section">
        <h2>7. Persistence Testing</h2>
        <button class="btn-primary" onclick="testPersistence()">Test Language Persistence</button>
        <button class="btn-warning" onclick="clearStoredLanguage()">Clear Stored Language</button>
        <div id="persistence-test" class="result">Click buttons to test language persistence...</div>
    </div>
    
    <div class="test-section">
        <h2>8. Debug Console</h2>
        <button class="btn-primary" onclick="showDebugInfo()">Show Debug Info</button>
        <button class="btn-warning" onclick="clearConsole()">Clear Console</button>
        <div id="debug-console" class="result">
            <pre id="debug-output">Debug information will appear here...</pre>
        </div>
    </div>

    <script>
        let debugLog = [];
        
        function log(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}`;
            debugLog.push(entry);
            if (data) debugLog.push(JSON.stringify(data, null, 2));
            console.log(entry, data || '');
            updateDebugConsole();
        }
        
        function updateDebugConsole() {
            const output = document.getElementById('debug-output');
            if (output) {
                output.textContent = debugLog.slice(-15).join('\n');
            }
        }
        
        function checkSystemStatus() {
            const result = document.getElementById('system-status');
            let messages = [];
            let status = 'success';
            
            log('Checking system status...');
            
            // Check i18n system
            if (window.i18n) {
                messages.push('✅ i18n system loaded');
                messages.push(`Current language: ${window.i18n.currentLang || 'undefined'}`);
                messages.push(`Is loaded: ${window.i18n.isLoaded}`);
                messages.push(`Translation count: ${Object.keys(window.i18n.translations || {}).length}`);
                log('i18n system status', {
                    currentLang: window.i18n.currentLang,
                    isLoaded: window.i18n.isLoaded,
                    translationCount: Object.keys(window.i18n.translations || {}).length
                });
            } else {
                messages.push('❌ i18n system not found');
                status = 'error';
                log('i18n system not found');
            }
            
            // Check language switcher
            if (window.languageSwitcher) {
                messages.push('✅ Language switcher loaded');
                messages.push(`Is ready: ${window.languageSwitcher.isReady()}`);
                log('Language switcher status', {
                    isReady: window.languageSwitcher.isReady()
                });
            } else {
                messages.push('❌ Language switcher not found');
                status = 'error';
                log('Language switcher not found');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = `result ${status}`;
        }
        
        function checkLanguageSwitcher() {
            const result = document.getElementById('switcher-status');
            let messages = [];
            let status = 'success';
            
            log('Checking language switcher...');
            
            if (window.languageSwitcher) {
                messages.push('✅ Language switcher object found');
                messages.push(`Is initialized: ${window.languageSwitcher.isInitialized}`);
                messages.push(`Is ready: ${window.languageSwitcher.isReady()}`);
                
                // Check available methods
                const methods = ['switchLanguage', 'getCurrentLanguage', 'isReady'];
                methods.forEach(method => {
                    if (typeof window.languageSwitcher[method] === 'function') {
                        messages.push(`✅ Method ${method} available`);
                    } else {
                        messages.push(`❌ Method ${method} missing`);
                        status = 'error';
                    }
                });
                
                log('Language switcher details', {
                    isInitialized: window.languageSwitcher.isInitialized,
                    isReady: window.languageSwitcher.isReady(),
                    availableMethods: methods.filter(m => typeof window.languageSwitcher[m] === 'function')
                });
            } else {
                messages.push('❌ Language switcher not available');
                status = 'error';
                log('Language switcher not available');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = `result ${status}`;
        }
        
        function testSwitchToEnglish() {
            const result = document.getElementById('manual-test');
            log('Testing switch to English...');
            
            if (window.switchToEnglish) {
                const success = window.switchToEnglish();
                if (success) {
                    result.innerHTML = '✅ Successfully switched to English';
                    result.className = 'result success';
                    log('Switch to English successful');
                } else {
                    result.innerHTML = '❌ Failed to switch to English';
                    result.className = 'result error';
                    log('Switch to English failed');
                }
            } else {
                result.innerHTML = '❌ switchToEnglish function not available';
                result.className = 'result error';
                log('switchToEnglish function not available');
            }
        }
        
        function testSwitchToAmazigh() {
            const result = document.getElementById('manual-test');
            log('Testing switch to Amazigh...');
            
            if (window.switchToAmazigh) {
                const success = window.switchToAmazigh();
                if (success) {
                    result.innerHTML = '✅ Successfully switched to Amazigh';
                    result.className = 'result success';
                    log('Switch to Amazigh successful');
                } else {
                    result.innerHTML = '❌ Failed to switch to Amazigh';
                    result.className = 'result error';
                    log('Switch to Amazigh failed');
                }
            } else {
                result.innerHTML = '❌ switchToAmazigh function not available';
                result.className = 'result error';
                log('switchToAmazigh function not available');
            }
        }
        
        function getCurrentLanguage() {
            const result = document.getElementById('manual-test');
            log('Getting current language...');
            
            if (window.getCurrentLang) {
                const currentLang = window.getCurrentLang();
                result.innerHTML = `ℹ️ Current language: ${currentLang}`;
                result.className = 'result info';
                log('Current language retrieved', currentLang);
            } else {
                result.innerHTML = '❌ getCurrentLang function not available';
                result.className = 'result error';
                log('getCurrentLang function not available');
            }
        }
        
        function testLanguageButtons() {
            const result = document.getElementById('button-test');
            let messages = [];
            
            log('Testing language buttons...');
            
            const buttons = document.querySelectorAll('.lang-btn');
            messages.push(`Found ${buttons.length} language buttons`);
            
            buttons.forEach((button, index) => {
                const lang = button.getAttribute('data-lang');
                const text = button.textContent.trim();
                const hasCheckIcon = button.querySelector('.bi-check2') !== null;
                
                messages.push(`Button ${index + 1}: lang="${lang}", text="${text}", hasCheckIcon=${hasCheckIcon}`);
                log(`Button ${index + 1} details`, { lang, text, hasCheckIcon });
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result info';
        }
        
        function testTranslationIntegration() {
            const result = document.getElementById('translation-test');
            let messages = [];
            let status = 'success';
            
            log('Testing translation integration...');
            
            if (window.i18n && window.i18n.translate) {
                const testKeys = ['common.english', 'common.amazigh', 'common.app_name'];
                
                testKeys.forEach(key => {
                    const translation = window.i18n.translate(key);
                    messages.push(`${key}: "${translation}"`);
                    log(`Translation for ${key}`, translation);
                });
            } else {
                messages.push('❌ Translation system not available');
                status = 'error';
                log('Translation system not available');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = `result ${status}`;
        }
        
        function updateLiveDemo() {
            log('Updating live demo...');
            
            if (window.i18n && window.i18n.updateUI) {
                window.i18n.updateUI();
                document.getElementById('live-demo-result').innerHTML = '✅ Live demo updated via i18n.updateUI()';
                document.getElementById('live-demo-result').className = 'result success';
                log('Live demo updated via i18n.updateUI()');
            } else {
                // Manual update
                document.querySelectorAll('[data-i18n]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (window.i18n && window.i18n.translate) {
                        element.textContent = window.i18n.translate(key);
                    }
                });
                document.getElementById('live-demo-result').innerHTML = '✅ Live demo updated manually';
                document.getElementById('live-demo-result').className = 'result success';
                log('Live demo updated manually');
            }
        }
        
        function testPersistence() {
            const result = document.getElementById('persistence-test');
            let messages = [];
            
            log('Testing language persistence...');
            
            const selectedLang = localStorage.getItem('selectedLanguage');
            const preferredLang = localStorage.getItem('preferred_language');
            
            messages.push(`selectedLanguage: ${selectedLang || 'not set'}`);
            messages.push(`preferred_language: ${preferredLang || 'not set'}`);
            
            if (window.i18n) {
                messages.push(`Current i18n language: ${window.i18n.currentLang}`);
            }
            
            log('Language persistence check', {
                selectedLanguage: selectedLang,
                preferredLanguage: preferredLang,
                currentI18nLang: window.i18n?.currentLang
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result info';
        }
        
        function clearStoredLanguage() {
            log('Clearing stored language...');
            
            localStorage.removeItem('selectedLanguage');
            localStorage.removeItem('preferred_language');
            
            document.getElementById('persistence-test').innerHTML = '✅ Stored language preferences cleared';
            document.getElementById('persistence-test').className = 'result success';
            
            log('Stored language preferences cleared');
        }
        
        function showDebugInfo() {
            log('=== DEBUG INFO ===');
            log('window.i18n', window.i18n);
            log('window.languageSwitcher', window.languageSwitcher);
            log('Language buttons count', document.querySelectorAll('.lang-btn').length);
            log('localStorage selectedLanguage', localStorage.getItem('selectedLanguage'));
            log('localStorage preferred_language', localStorage.getItem('preferred_language'));
        }
        
        function clearConsole() {
            debugLog = [];
            updateDebugConsole();
            log('Console cleared');
        }
        
        // Auto-run basic checks on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkSystemStatus();
                checkLanguageSwitcher();
            }, 1000);
        });
    </script>
</body>
</html>
