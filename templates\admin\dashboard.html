{% extends 'admin/base.html' %}

{% block title %}Dashboard - Admin Panel{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Dashboard</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">Dashboard</h1>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="stat-icon bg-primary bg-opacity-10 text-primary">
                    <i class="bi bi-file-text"></i>
                </div>
                <p class="stat-title">Text Conversions</p>
                <h2 class="stat-value">{{ stats.text_conversions }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="stat-icon bg-success bg-opacity-10 text-success">
                    <i class="bi bi-file-earmark"></i>
                </div>
                <p class="stat-title">File Conversions</p>
                <h2 class="stat-value">{{ stats.file_conversions }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="stat-icon bg-info bg-opacity-10 text-info">
                    <i class="bi bi-globe"></i>
                </div>
                <p class="stat-title">Web Conversions</p>
                <h2 class="stat-value">{{ stats.web_conversions }}</h2>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card">
            <div class="card-body">
                <div class="stat-icon bg-warning bg-opacity-10 text-warning">
                    <i class="bi bi-people"></i>
                </div>
                <p class="stat-title">Unique Visitors</p>
                <h2 class="stat-value">{{ stats.unique_visitors }}</h2>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Settings Summary -->
<div class="row mb-4">
    <!-- Usage Chart -->
    <div class="col-lg-8 mb-4 mb-lg-0">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Conversions Overview</h5>
            </div>
            <div class="card-body">
                <canvas id="usageChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Key Settings -->
    <div class="col-lg-4">
        <div class="card h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Key Settings</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Default Language:</span>
                    <span class="badge bg-primary">{{ settings.default_language|default('English', true) }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Bilingual Mode:</span>
                    <span class="badge {% if settings.bilingual_mode %}bg-success{% else %}bg-secondary{% endif %}">
                        {% if settings.bilingual_mode %}Enabled{% else %}Disabled{% endif %}
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Active Conversion Method:</span>
                    <span class="badge bg-info">{{ settings.active_method|default('Standard', true) }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Maintenance Mode:</span>
                    <span class="badge {% if settings.maintenance_mode %}bg-danger{% else %}bg-success{% endif %}">
                        {% if settings.maintenance_mode %}Active{% else %}Inactive{% endif %}
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Cookie Consent:</span>
                    <span class="badge {% if settings.cookie_consent %}bg-success{% else %}bg-secondary{% endif %}">
                        {% if settings.cookie_consent %}Shown{% else %}Hidden{% endif %}
                    </span>
                </div>
                <hr>
                <div class="d-grid">
                    <a href="{{ url_for('admin.admin_interface') }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-gear me-1"></i> Manage Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-lg-8 mb-4 mb-lg-0">
        <div class="card">
            <div class="card-header bg-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Activities</h5>
                <a href="#" class="text-decoration-none">View All</a>
            </div>
            <div class="card-body">
                <div class="activity-timeline">
                    {% for activity in recent_activities %}
                    <div class="activity-item">
                        <p class="time">{{ activity.time }}</p>
                        <p class="description">{{ activity.description }}</p>
                        <p class="meta">{{ activity.type }} | <span class="status-badge bg-{{ activity.status_color }}">{{ activity.status }}</span></p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">System Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <label class="form-label d-flex justify-content-between">
                        <span>CPU Usage</span>
                        <span>{{ system_status.cpu_usage }}%</span>
                    </label>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ system_status.cpu_usage }}%"
                            aria-valuenow="{{ system_status.cpu_usage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="form-label d-flex justify-content-between">
                        <span>Memory Usage</span>
                        <span>{{ system_status.memory_usage }}%</span>
                    </label>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-info" role="progressbar" style="width: {{ system_status.memory_usage }}%"
                            aria-valuenow="{{ system_status.memory_usage }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                <div class="mb-4">
                    <label class="form-label d-flex justify-content-between">
                        <span>Disk Space</span>
                        <span>{{ system_status.disk_space }}%</span>
                    </label>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ system_status.disk_space }}%"
                            aria-valuenow="{{ system_status.disk_space }}" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="bi bi-calendar me-1"></i> Last Backup:</span>
                    <span>{{ system_status.last_backup }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Usage Chart
    const ctx = document.getElementById('usageChart').getContext('2d');
    const usageChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ chart_labels|tojson }},
            datasets: [
                {
                    label: 'Text Conversions',
                    data: {{ chart_data.text_conversions|tojson }},
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.2
                },
                {
                    label: 'File Conversions',
                    data: {{ chart_data.file_conversions|tojson }},
                    borderColor: '#198754',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.2
                },
                {
                    label: 'Web Conversions',
                    data: {{ chart_data.web_conversions|tojson }},
                    borderColor: '#0dcaf0',
                    backgroundColor: 'rgba(13, 202, 240, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.2
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                tooltip: {
                    enabled: true,
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        boxWidth: 8
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
</script>
{% endblock %}