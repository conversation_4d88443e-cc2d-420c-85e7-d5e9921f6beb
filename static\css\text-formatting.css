/**
 * Text formatting styles for the Tifinagh Converter
 */

/* <PERSON>er formatting controls */
.header-formatting-controls {
    display: flex;
    margin-left: auto;
    gap: 0.5rem;
    align-items: center;
    padding: 0 0.5rem;
}

.font-size-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.font-size-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-color);
    transition: all 0.2s;
    padding: 0;
}

.font-size-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.font-size-value {
    min-width: 2rem;
    text-align: center;
    font-size: 0.75rem;
}

/* Text alignment controls */
.text-align-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.text-align-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-color);
    transition: all 0.2s;
    padding: 0;
}

.text-align-btn:hover {
    background-color: var(--bg-color);
}

.text-align-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Adjust language header to accommodate formatting controls */
.language-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

/* Text area with formatting */
.text-area textarea.formatted, .text-area .tifinagh-text.formatted {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.5;
    transition: font-size 0.2s, text-align 0.2s;
}

/* Font size classes */
.font-size-small {
    font-size: 0.875rem !important;
}

.font-size-medium {
    font-size: 1rem !important;
}

.font-size-large {
    font-size: 1.25rem !important;
}

.font-size-xlarge {
    font-size: 1.5rem !important;
}

/* Text alignment classes */
.text-align-left {
    text-align: left !important;
}

.text-align-center {
    text-align: center !important;
}

.text-align-right {
    text-align: right !important;
}

.text-align-justify {
    text-align: justify !important;
}

/* Preserve whitespace */
.preserve-whitespace {
    white-space: pre-wrap !important;
}

/* Preserve line breaks */
.preserve-linebreaks {
    white-space: pre-line !important;
}

/* Tifinagh font */
@font-face {
    font-family: 'Noto Sans Tifinagh';
    src: url('../fonts/NotoSansTifinagh-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
}

.tifinagh-text {
    font-family: 'Noto Sans Tifinagh', Arial, sans-serif !important;
    direction: ltr;
    line-height: 1.6;
}

/* Table styles */
.tifinagh-text table, .formatted table, .converted-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    font-family: 'Noto Sans Tifinagh', Arial, sans-serif;
    border: 1px solid var(--border-color, #e5e7eb);
    border-radius: 4px;
    overflow: hidden;
}

.tifinagh-text th, .tifinagh-text td,
.formatted th, .formatted td,
.converted-table th, .converted-table td {
    border: 1px solid var(--border-color, #e5e7eb);
    padding: 0.75rem;
    text-align: left;
    vertical-align: top;
}

.tifinagh-text th, .formatted th, .converted-table th {
    background-color: var(--bg-light, #f9fafb);
    font-weight: bold;
    color: var(--text-color, #111827);
}

.tifinagh-text tr:nth-child(even),
.formatted tr:nth-child(even),
.converted-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.tifinagh-text tr:hover,
.formatted tr:hover,
.converted-table tr:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Fix for tables in RTL context */
[dir="rtl"] .tifinagh-text table,
[dir="rtl"] .formatted table,
[dir="rtl"] .converted-table {
    direction: rtl;
}

/* Ensure tables are responsive */
.text-area .tifinagh-text.contains-table {
    overflow-x: auto;
    max-width: 100%;
}
