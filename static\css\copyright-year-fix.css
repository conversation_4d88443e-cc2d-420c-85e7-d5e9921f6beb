/**
 * تحسينات مظهر السنة في حقوق النشر
 * Copyright Year Visual Enhancements
 */

/* تحسين مظهر عنصر السنة */
#current-year {
    font-weight: 600;
    color: var(--bs-primary, #0d6efd);
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
    text-shadow: 0 0 3px rgba(13, 110, 253, 0.2);
}

/* تأثير التمرير */
#current-year:hover {
    color: var(--bs-primary-dark, #0a58ca);
    text-shadow: 0 0 6px rgba(13, 110, 253, 0.4);
    transform: scale(1.05);
}

/* تأثير الوميض عند التحديث */
#current-year.updating {
    animation: yearUpdate 0.6s ease-in-out;
}

@keyframes yearUpdate {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
        color: var(--bs-success, #198754);
        text-shadow: 0 0 8px rgba(25, 135, 84, 0.5);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* تحسين مظهر نص حقوق النشر بالكامل */
[data-i18n="footer.copyright"] {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.9rem;
    line-height: 1.4;
    letter-spacing: 0.01em;
}

/* تحسين للوضع المظلم */
@media (prefers-color-scheme: dark) {
    #current-year {
        color: var(--bs-primary-light, #6ea8fe);
        text-shadow: 0 0 3px rgba(110, 168, 254, 0.3);
    }
    
    #current-year:hover {
        color: var(--bs-primary-lighter, #9ec5fe);
        text-shadow: 0 0 6px rgba(110, 168, 254, 0.5);
    }
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    [data-i18n="footer.copyright"] {
        font-size: 0.85rem;
    }
    
    #current-year {
        font-size: inherit;
    }
}

/* تحسين للطباعة */
@media print {
    #current-year {
        color: #000 !important;
        text-shadow: none !important;
        font-weight: 600 !important;
    }
    
    #current-year:hover {
        transform: none !important;
    }
}

/* تأثير خاص للسنة الجديدة */
.new-year-celebration #current-year {
    animation: newYearCelebration 2s ease-in-out;
}

@keyframes newYearCelebration {
    0%, 100% {
        transform: scale(1);
        color: var(--bs-primary, #0d6efd);
    }
    25% {
        transform: scale(1.2);
        color: var(--bs-success, #198754);
        text-shadow: 0 0 10px rgba(25, 135, 84, 0.7);
    }
    50% {
        transform: scale(1.1);
        color: var(--bs-warning, #ffc107);
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.7);
    }
    75% {
        transform: scale(1.15);
        color: var(--bs-info, #0dcaf0);
        text-shadow: 0 0 10px rgba(13, 202, 240, 0.7);
    }
}

/* حالة الخطأ */
#current-year.error {
    color: var(--bs-danger, #dc3545);
    text-shadow: 0 0 3px rgba(220, 53, 69, 0.3);
    animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* حالة التحميل */
#current-year.loading {
    opacity: 0.6;
    animation: loadingPulse 1s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

/* تحسين إمكانية الوصول */
#current-year:focus {
    outline: 2px solid var(--bs-primary, #0d6efd);
    outline-offset: 2px;
    border-radius: 2px;
}

/* تحسين للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    #current-year,
    #current-year:hover,
    #current-year.updating,
    #current-year.loading,
    #current-year.error,
    .new-year-celebration #current-year {
        animation: none !important;
        transition: none !important;
        transform: none !important;
    }
}
