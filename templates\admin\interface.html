{% extends 'admin/base.html' %}

{% block title %}Interface Customization - Admin Panel{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Interface Customization</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3">Interface Customization</h1>
        <p class="text-muted">Customize the appearance and branding of the website.</p>
    </div>
</div>

<!-- Logo & Welcome Message and Colors & Fonts sections have been removed -->

<div class="row">
    <!-- قسم إعدادات مواقع التواصل الاجتماعي الجديد -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Social Media Links</h5>
                <span class="badge bg-primary">Footer Icons</span>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    أيقونات التواصل الاجتماعي ستظهر في الفوتر فقط إذا كانت مفعلة وتم إدخال رابط صحيح لها.
                </div>

                <form id="socialForm" action="{{ url_for('admin.admin_save_social') }}" method="POST">
                    <!-- Facebook -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label d-flex align-items-center mb-0">
                                <div class="social-icon-preview me-2">
                                    <i class="bi bi-facebook"></i>
                                </div>
                                Facebook
                            </label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_facebook" name="enable_facebook" {% if social_media.facebook.enabled %}checked{% endif %}>
                                <label class="form-check-label" for="enable_facebook">Show</label>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text">URL</span>
                            <input type="url" class="form-control" id="facebook_url" name="facebook_url" placeholder="https://facebook.com/yourpage" value="{{ social_media.facebook.url }}">
                        </div>
                    </div>

                    <!-- Twitter -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label d-flex align-items-center mb-0">
                                <div class="social-icon-preview me-2">
                                    <i class="bi bi-twitter-x"></i>
                                </div>
                                X
                            </label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_twitter" name="enable_twitter" {% if social_media.twitter.enabled %}checked{% endif %}>
                                <label class="form-check-label" for="enable_twitter">Show</label>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text">URL</span>
                            <input type="url" class="form-control" id="twitter_url" name="twitter_url" placeholder="https://x.com/yourhandle" value="{{ social_media.twitter.url }}">
                        </div>
                    </div>

                    <!-- Instagram -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label d-flex align-items-center mb-0">
                                <div class="social-icon-preview me-2">
                                    <i class="bi bi-instagram"></i>
                                </div>
                                Instagram
                            </label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_instagram" name="enable_instagram" {% if social_media.instagram.enabled %}checked{% endif %}>
                                <label class="form-check-label" for="enable_instagram">Show</label>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text">URL</span>
                            <input type="url" class="form-control" id="instagram_url" name="instagram_url" placeholder="https://instagram.com/yourhandle" value="{{ social_media.instagram.url }}">
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label d-flex align-items-center mb-0">
                                <div class="social-icon-preview me-2">
                                    <i class="bi bi-envelope"></i>
                                </div>
                                Email
                            </label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enable_email" name="enable_email" {% if social_media.email.enabled %}checked{% endif %}>
                                <label class="form-check-label" for="enable_email">Show</label>
                            </div>
                        </div>
                        <div class="input-group">
                            <span class="input-group-text">Email</span>
                            <input type="email" class="form-control" id="email_url" name="email_url" placeholder="<EMAIL>" value="{{ social_media.email.url }}">
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save me-1"></i> Save Social Links
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ super() }}
<style>
    /* Social Icon Preview Styles */
    .social-icon-preview {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(var(--bs-primary-rgb), 0.1);
        color: var(--bs-primary);
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .social-icon-preview:hover {
        background-color: var(--bs-primary);
        color: white;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block scripts %}
<!-- سيتم إضافة أكواد JavaScript الجديدة هنا -->
{% endblock %}