<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Compatibility Fix - window.i18n.onLoad</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- New Language System CSS -->
    <link rel="stylesheet" href="static/css/language-system-new.css">
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .test-header {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-success { background-color: #198754; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #0dcaf0; }
        
        .console-output {
            background: #1e1e1e;
            color: #ffffff;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .test-result {
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-left: 4px solid;
        }
        
        .test-pass {
            background: #d1f2eb;
            border-color: #198754;
            color: #0f5132;
        }
        
        .test-fail {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .test-pending {
            background: #fff3cd;
            border-color: #ffc107;
            color: #664d03;
        }
    </style>
</head>
<body class="bg-light">
    
    <!-- Header -->
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 اختبار إصلاح التوافق</h1>
            <p class="mb-0">Testing window.i18n.onLoad Compatibility Fix</p>
        </div>
        
        <!-- حالة النظام -->
        <div class="test-section">
            <h2>📊 حالة النظام</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>System Status:</h5>
                    <div id="system-status">
                        <div><span class="status-indicator status-info"></span>Initializing...</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5>Compatibility Status:</h5>
                    <div id="compatibility-status">
                        <div><span class="status-indicator status-warning"></span>Checking...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبارات window.i18n.onLoad -->
        <div class="test-section">
            <h2>🧪 اختبارات window.i18n.onLoad</h2>
            
            <div id="test-results">
                <!-- سيتم ملء النتائج هنا -->
            </div>
            
            <button class="btn btn-primary" onclick="runAllTests()">
                <i class="bi bi-play-circle"></i> تشغيل جميع الاختبارات
            </button>
            <button class="btn btn-secondary" onclick="clearResults()">
                <i class="bi bi-trash"></i> مسح النتائج
            </button>
        </div>
        
        <!-- Console Output -->
        <div class="test-section">
            <h2>🖥️ Console Output</h2>
            <div id="console-output" class="console-output">
                [System] Initializing compatibility test environment...\n
            </div>
            <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearConsole()">
                <i class="bi bi-trash"></i> Clear Console
            </button>
        </div>
        
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Mock API for testing -->
    <script>
        // Mock translations
        window.mockTranslations = {
            "test.hello": {
                "en": "Hello",
                "am": "ⴰⵣⵓⵍ"
            },
            "test.welcome": {
                "en": "Welcome",
                "am": "ⴰⵏⵙⵓⴼ"
            }
        };
        
        // Mock fetch
        window.fetch = function(url, options) {
            if (url === '/get-i18n') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        translations: window.mockTranslations
                    })
                });
            }
            
            if (url === '/set-language') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        message: 'Language changed successfully'
                    })
                });
            }
            
            return Promise.reject(new Error('Unknown endpoint'));
        };
        
        // Console logging
        let consoleOutput = [];
        
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.push(logEntry);
            updateConsoleDisplay();
        }
        
        function updateConsoleDisplay() {
            const console = document.getElementById('console-output');
            console.textContent = consoleOutput.join('\n');
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            consoleOutput = ['[System] Console cleared.'];
            updateConsoleDisplay();
        }
        
        // Test results
        let testResults = [];
        
        function addTestResult(testName, status, message) {
            const result = { testName, status, message, timestamp: new Date() };
            testResults.push(result);
            
            const resultsContainer = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${status}`;
            
            const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⏳';
            resultDiv.innerHTML = `
                <strong>${statusIcon} ${testName}</strong><br>
                <small>${message}</small>
            `;
            
            resultsContainer.appendChild(resultDiv);
            logToConsole(`Test ${testName}: ${status} - ${message}`, status === 'pass' ? 'success' : status === 'fail' ? 'error' : 'info');
        }
        
        function clearResults() {
            testResults = [];
            document.getElementById('test-results').innerHTML = '';
            logToConsole('Test results cleared', 'info');
        }
        
        // Test functions
        function testWindowI18nExists() {
            if (typeof window.i18n === 'object' && window.i18n !== null) {
                addTestResult('window.i18n exists', 'pass', 'window.i18n object is available');
                return true;
            } else {
                addTestResult('window.i18n exists', 'fail', 'window.i18n is not available');
                return false;
            }
        }
        
        function testOnLoadFunction() {
            if (typeof window.i18n.onLoad === 'function') {
                addTestResult('window.i18n.onLoad function', 'pass', 'onLoad function is available');
                return true;
            } else {
                addTestResult('window.i18n.onLoad function', 'fail', 'onLoad function is missing');
                return false;
            }
        }
        
        function testOnLoadExecution() {
            return new Promise((resolve) => {
                let callbackExecuted = false;
                
                try {
                    window.i18n.onLoad(function() {
                        callbackExecuted = true;
                        addTestResult('window.i18n.onLoad execution', 'pass', 'Callback was executed successfully');
                        resolve(true);
                    });
                    
                    // إذا لم يتم تنفيذ callback خلال 3 ثوان، اعتبره فشل
                    setTimeout(() => {
                        if (!callbackExecuted) {
                            addTestResult('window.i18n.onLoad execution', 'fail', 'Callback was not executed within 3 seconds');
                            resolve(false);
                        }
                    }, 3000);
                    
                } catch (error) {
                    addTestResult('window.i18n.onLoad execution', 'fail', `Error: ${error.message}`);
                    resolve(false);
                }
            });
        }
        
        function testMultipleOnLoadCalls() {
            return new Promise((resolve) => {
                let callbackCount = 0;
                const expectedCalls = 3;
                
                try {
                    for (let i = 0; i < expectedCalls; i++) {
                        window.i18n.onLoad(function() {
                            callbackCount++;
                            if (callbackCount === expectedCalls) {
                                addTestResult('Multiple onLoad calls', 'pass', `All ${expectedCalls} callbacks executed`);
                                resolve(true);
                            }
                        });
                    }
                    
                    setTimeout(() => {
                        if (callbackCount < expectedCalls) {
                            addTestResult('Multiple onLoad calls', 'fail', `Only ${callbackCount}/${expectedCalls} callbacks executed`);
                            resolve(false);
                        }
                    }, 3000);
                    
                } catch (error) {
                    addTestResult('Multiple onLoad calls', 'fail', `Error: ${error.message}`);
                    resolve(false);
                }
            });
        }
        
        function testOtherI18nFunctions() {
            const functions = ['translate', 'setLanguage', 'currentLang', 'isLoaded'];
            let allPass = true;
            
            functions.forEach(funcName => {
                if (window.i18n[funcName] !== undefined) {
                    addTestResult(`window.i18n.${funcName}`, 'pass', `${funcName} is available`);
                } else {
                    addTestResult(`window.i18n.${funcName}`, 'fail', `${funcName} is missing`);
                    allPass = false;
                }
            });
            
            return allPass;
        }
        
        async function runAllTests() {
            logToConsole('Starting compatibility tests...', 'info');
            clearResults();
            
            // Test 1: window.i18n exists
            const i18nExists = testWindowI18nExists();
            
            if (!i18nExists) {
                logToConsole('Cannot continue tests - window.i18n not available', 'error');
                return;
            }
            
            // Test 2: onLoad function exists
            const onLoadExists = testOnLoadFunction();
            
            if (!onLoadExists) {
                logToConsole('Cannot test onLoad execution - function not available', 'error');
                return;
            }
            
            // Test 3: onLoad execution
            await testOnLoadExecution();
            
            // Test 4: Multiple onLoad calls
            await testMultipleOnLoadCalls();
            
            // Test 5: Other i18n functions
            testOtherI18nFunctions();
            
            logToConsole('All compatibility tests completed', 'success');
        }
        
        // Status updates
        function updateSystemStatus(status) {
            const statusElement = document.getElementById('system-status');
            
            let indicator, text;
            switch (status) {
                case 'ready':
                    indicator = 'status-success';
                    text = 'System Ready';
                    break;
                case 'error':
                    indicator = 'status-error';
                    text = 'System Error';
                    break;
                default:
                    indicator = 'status-info';
                    text = 'Initializing...';
            }
            
            statusElement.innerHTML = `<div><span class="status-indicator ${indicator}"></span>${text}</div>`;
        }
        
        function updateCompatibilityStatus(status) {
            const statusElement = document.getElementById('compatibility-status');
            
            let indicator, text;
            switch (status) {
                case 'compatible':
                    indicator = 'status-success';
                    text = 'Fully Compatible';
                    break;
                case 'partial':
                    indicator = 'status-warning';
                    text = 'Partially Compatible';
                    break;
                case 'incompatible':
                    indicator = 'status-error';
                    text = 'Incompatible';
                    break;
                default:
                    indicator = 'status-info';
                    text = 'Checking...';
            }
            
            statusElement.innerHTML = `<div><span class="status-indicator ${indicator}"></span>${text}</div>`;
        }
        
        // Event listeners
        document.addEventListener('system:ready', function() {
            logToConsole('System ready event received', 'success');
            updateSystemStatus('ready');
            updateCompatibilityStatus('compatible');
        });
        
        document.addEventListener('languageSystemLoaded', function() {
            logToConsole('Legacy languageSystemLoaded event received', 'success');
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('Test page loaded', 'info');
            updateSystemStatus('initializing');
            updateCompatibilityStatus('checking');
            
            // تشغيل اختبار تلقائي بعد 2 ثانية
            setTimeout(() => {
                logToConsole('Running automatic compatibility test...', 'info');
                runAllTests();
            }, 2000);
        });
    </script>
    
    <!-- New Language System -->
    <script src="static/js/language-system-new.js"></script>
    <!-- Compatibility Fix -->
    <script src="static/js/compatibility-fix.js"></script>
    
</body>
</html>
