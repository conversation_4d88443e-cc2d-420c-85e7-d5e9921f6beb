/**
 * Unified Tab System for Tifinagh Converter
 * This file provides a consistent tab implementation that can be used across the application
 */

class TabSystem {
    /**
     * Initialize the tab system
     * @param {Object} options - Configuration options
     * @param {string} options.tabButtonSelector - CSS selector for tab buttons
     * @param {string} options.tabContentSelector - CSS selector for tab content containers
     * @param {string} options.activeClass - CSS class to apply to active elements
     * @param {string} options.defaultTab - ID of the default tab to show
     * @param {boolean} options.useUrlParams - Whether to use URL parameters for tab state
     * @param {boolean} options.useLocalStorage - Whether to use localStorage for tab state
     * @param {string} options.localStorageKey - Key to use for localStorage
     * @param {Function} options.onTabChange - Callback function when tab changes
     */
    constructor(options = {}) {
        // Default options
        this.options = Object.assign({
            tabButtonSelector: '.tab-btn',
            tabContentSelector: '.tab-content',
            activeClass: 'active',
            defaultTab: 'text-converter',
            useUrlParams: true,
            useLocalStorage: true,
            localStorageKey: 'activeTab',
            onTabChange: null
        }, options);

        // DOM elements
        this.tabButtons = document.querySelectorAll(this.options.tabButtonSelector);
        this.tabContents = document.querySelectorAll(this.options.tabContentSelector);

        // Initialize
        this.init();
    }

    /**
     * Initialize the tab system
     */
    init() {
        // Add click event listeners to tab buttons
        this.tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const tabId = button.getAttribute('data-tab');
                this.switchTab(tabId);
            });
        });

        // Determine which tab to show initially
        let initialTab = this.options.defaultTab;

        // Check URL parameters if enabled - ALWAYS give priority to URL parameters
        if (this.options.useUrlParams) {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');
            if (tabParam && document.getElementById(tabParam)) {
                initialTab = tabParam;
                // Always use URL parameter if available, regardless of localStorage
                console.log('Using tab from URL parameter:', tabParam);
                // Switch to the tab and return early
                this.switchTab(initialTab);
                return;
            }
        }

        // Check localStorage if enabled and URL params didn't provide a tab
        if (this.options.useLocalStorage) {
            const savedTab = localStorage.getItem(this.options.localStorageKey);
            if (savedTab && document.getElementById(savedTab)) {
                initialTab = savedTab;
            }
        }

        // Switch to the initial tab
        this.switchTab(initialTab);

        // Log initialization
        console.log('Tab system initialized with initial tab:', initialTab);
    }

    /**
     * Switch to a specific tab
     * @param {string} tabId - ID of the tab to switch to
     */
    switchTab(tabId) {
        console.log('Switching to tab:', tabId);

        // First deactivate all tab buttons
        this.tabButtons.forEach(btn => {
            btn.classList.remove(this.options.activeClass);
        });

        // Activate the selected tab button
        const selectedButton = document.querySelector(`[data-tab="${tabId}"]`);
        if (selectedButton) {
            selectedButton.classList.add(this.options.activeClass);
        }

        // Hide all tab contents
        this.tabContents.forEach(content => {
            content.style.display = 'none';
            content.classList.remove(this.options.activeClass);
        });

        // Activate the selected tab content
        const selectedTab = document.getElementById(tabId);
        if (selectedTab) {
            selectedTab.style.display = 'block';
            selectedTab.classList.add(this.options.activeClass);

            // Force layout recalculation to ensure smooth transitions
            selectedTab.offsetHeight;
        }

        // Update URL if enabled
        if (this.options.useUrlParams) {
            const url = new URL(window.location);
            url.searchParams.set('tab', tabId);
            window.history.pushState({}, '', url);
        }

        // Save to localStorage if enabled
        if (this.options.useLocalStorage) {
            localStorage.setItem(this.options.localStorageKey, tabId);
        }

        // Call onTabChange callback if provided
        if (typeof this.options.onTabChange === 'function') {
            this.options.onTabChange(tabId, selectedTab);
        }
    }

    /**
     * Get the currently active tab ID
     * @returns {string} The ID of the active tab
     */
    getActiveTab() {
        for (const content of this.tabContents) {
            if (content.classList.contains(this.options.activeClass)) {
                return content.id;
            }
        }
        return null;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if there are tabs on the page
    const tabButtons = document.querySelectorAll('.tab-btn');
    if (tabButtons.length > 0) {
        // Create tab system with default options
        window.tabSystem = new TabSystem();

        // Make the TabSystem class available globally
        window.TabSystem = TabSystem;

        // Add click event listeners to all tab links in the footer and elsewhere
        const tabLinks = document.querySelectorAll('a[href*="tab="]');
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const url = new URL(this.href);
                const tabId = url.searchParams.get('tab');
                if (tabId && document.getElementById(tabId)) {
                    // Update URL to reflect the tab change
                    const currentUrl = new URL(window.location);
                    currentUrl.searchParams.set('tab', tabId);
                    window.history.pushState({}, '', currentUrl);

                    // Switch to the tab
                    window.tabSystem.switchTab(tabId);

                    // Scroll to the top of the tab content
                    document.getElementById(tabId).scrollIntoView({ behavior: 'smooth' });

                    // Log the tab change
                    console.log('Tab changed via link click to:', tabId);
                }
            });
        });
    }
});
