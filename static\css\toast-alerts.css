/**
 * Toast Alerts Styles
 */

.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.toast-alert {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: toast-in 0.3s ease-in-out;
    transition: all 0.3s ease;
    overflow: hidden;
    width: 100%;
}

.toast-alert.closing {
    opacity: 0;
    transform: translateX(40px);
}

@keyframes toast-in {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast-alert .toast-icon {
    margin-right: 12px;
    flex-shrink: 0;
}

.toast-alert .toast-content {
    flex-grow: 1;
}

.toast-alert .toast-title {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 1rem;
}

.toast-alert .toast-message {
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

/* HTML content in toast messages */
.toast-alert .toast-message .success-message {
    font-size: 0.9rem;
}

.toast-alert .toast-message .word-file-tip {
    font-weight: 600;
    margin: 10px 0 5px 0;
}

.toast-alert .toast-message ol {
    margin: 5px 0 0 0;
    padding-left: 20px;
}

.toast-alert .toast-message ol li {
    margin-bottom: 5px;
}

.toast-alert .toast-close {
    background: none;
    border: none;
    color: inherit;
    opacity: 0.7;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    font-size: 1.2rem;
    line-height: 1;
    transition: opacity 0.2s;
    flex-shrink: 0;
}

.toast-alert .toast-close:hover {
    opacity: 1;
}

/* Toast types */
.toast-alert.toast-success {
    background-color: #e7f7ee;
    border-left: 4px solid #28a745;
    color: #155724;
}

.toast-alert.toast-error {
    background-color: #f8e7e7;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.toast-alert.toast-warning {
    background-color: #fff8e6;
    border-left: 4px solid #ffc107;
    color: #856404;
}

.toast-alert.toast-info {
    background-color: #e6f5ff;
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

/* Progress bar for auto-dismiss */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(0, 0, 0, 0.2);
    width: 100%;
    transform-origin: left;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .toast-container {
        right: 10px;
        left: 10px;
        max-width: calc(100% - 20px);
    }
}
