/**
 * ملف مهجور - تم دمج جميع إصلاحات أشرطة التمرير في scrollbar-fix.css
 * يُحتفظ بهذا الملف للتوافق مع الإصدارات السابقة
 */

/* جميع إعدادات أشرطة التمرير موجودة الآن في static/css/scrollbar-fix.css */
/* لا تضع أي إعدادات هنا لتجنب التضارب */

/* Advanced editor wrapper - ensure proper containment */
.advanced-editor-wrapper {
    position: relative;
    height: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
}

/* Make sure CKEditor content has scrollbar */
.ck-content {
    overflow-y: auto !important;
    max-height: 500px !important;
}

/* Ensure the outer editor container allows internal scrolling */
#document-editor {
    overflow: visible !important;
}

/* Make the main container's height fixed */
#advanced-converter.tab-content {
    height: auto !important;
    overflow: visible !important;
}

/* Control the main page scrollbar only */
html, body {
    overflow-x: hidden; /* Prevent horizontal scrollbar */
}

html {
    overflow-y: scroll !important; /* Always show main scrollbar on html element */
}

body {
    overflow-y: visible !important; /* Let body content flow naturally */
}

/* Fix for mobile view */
@media (max-width: 768px) {
    .document-editor-content {
        overflow-y: auto !important;
        max-height: 450px !important;
    }
    
    .ck-content,
    .ck-editor__editable,
    .document-editor__editable,
    .ck.ck-editor__editable_inline {
        overflow-y: auto !important;
        max-height: 450px !important;
    }
    
    #document-editor,
    .advanced-editor-wrapper {
        overflow: visible !important;
    }
}
