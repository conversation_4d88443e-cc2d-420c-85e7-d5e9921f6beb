/**
 * Unified text formatting functionality for all tabs
 */

document.addEventListener('DOMContentLoaded', function() {
    // Debug log
    console.log('Unified formatting script loaded');

    // Text Converter Tab Elements
    const latinText = document.getElementById('latin-text');
    const tifinaghText = document.getElementById('tifinagh-text');
    const fontSizeValue = document.getElementById('font-size-value-text');
    const decreaseFontBtn = document.getElementById('decrease-font-text');
    const increaseFontBtn = document.getElementById('increase-font-text');
    const alignLeftBtn = document.getElementById('align-left-text');
    const alignCenterBtn = document.getElementById('align-center-text');
    const alignRightBtn = document.getElementById('align-right-text');
    const alignJustifyBtn = document.getElementById('align-justify-text');

    // Debug log for elements
    console.log('Text elements:', {
        latinText: latinText ? 'found' : 'not found',
        tifinaghText: tifinaghText ? 'found' : 'not found',
        fontSizeValue: fontSizeValue ? 'found' : 'not found',
        decreaseFontBtn: decreaseFontBtn ? 'found' : 'not found',
        increaseFontBtn: increaseFontBtn ? 'found' : 'not found',
        alignLeftBtn: alignLeftBtn ? 'found' : 'not found',
        alignCenterBtn: alignCenterBtn ? 'found' : 'not found',
        alignRightBtn: alignRightBtn ? 'found' : 'not found',
        alignJustifyBtn: alignJustifyBtn ? 'found' : 'not found'
    });

    // File Converter Tab Elements
    const fontSizeValueFile = document.getElementById('font-size-value-file');
    const decreaseFontBtnFile = document.getElementById('decrease-font-file');
    const increaseFontBtnFile = document.getElementById('increase-font-file');
    const alignLeftBtnFile = document.getElementById('align-left-file');
    const alignCenterBtnFile = document.getElementById('align-center-file');
    const alignRightBtnFile = document.getElementById('align-right-file');
    const alignJustifyBtnFile = document.getElementById('align-justify-file');

    // Web Converter Tab Elements
    const fontSizeValueWeb = document.getElementById('font-size-value-web');
    const decreaseFontBtnWeb = document.getElementById('decrease-font-web');
    const increaseFontBtnWeb = document.getElementById('increase-font-web');
    const alignLeftBtnWeb = document.getElementById('align-left-web');
    const alignCenterBtnWeb = document.getElementById('align-center-web');
    const alignRightBtnWeb = document.getElementById('align-right-web');
    const alignJustifyBtnWeb = document.getElementById('align-justify-web');

    // Font size settings
    const fontSizes = [12, 14, 16, 18, 20, 24, 28, 32];
    let currentFontSizeIndex = 2; // Start with 16px (index 2)

    // Initialize
    updateFontSize();

    // Event listeners for font size controls - Text Converter Tab
    if (decreaseFontBtn) {
        decreaseFontBtn.addEventListener('click', () => {
            if (currentFontSizeIndex > 0) {
                currentFontSizeIndex--;
                updateFontSize();
            }
        });
    }

    if (increaseFontBtn) {
        increaseFontBtn.addEventListener('click', () => {
            if (currentFontSizeIndex < fontSizes.length - 1) {
                currentFontSizeIndex++;
                updateFontSize();
            }
        });
    }

    // Event listeners for font size controls - File Converter Tab
    if (decreaseFontBtnFile) {
        decreaseFontBtnFile.addEventListener('click', () => {
            if (currentFontSizeIndex > 0) {
                currentFontSizeIndex--;
                updateFontSize();
            }
        });
    }

    if (increaseFontBtnFile) {
        increaseFontBtnFile.addEventListener('click', () => {
            if (currentFontSizeIndex < fontSizes.length - 1) {
                currentFontSizeIndex++;
                updateFontSize();
            }
        });
    }

    // Event listeners for font size controls - Web Converter Tab
    if (decreaseFontBtnWeb) {
        decreaseFontBtnWeb.addEventListener('click', () => {
            if (currentFontSizeIndex > 0) {
                currentFontSizeIndex--;
                updateFontSize();
            }
        });
    }

    if (increaseFontBtnWeb) {
        increaseFontBtnWeb.addEventListener('click', () => {
            if (currentFontSizeIndex < fontSizes.length - 1) {
                currentFontSizeIndex++;
                updateFontSize();
            }
        });
    }

    // Event listeners for text alignment controls - Text Converter Tab
    if (alignLeftBtn) {
        alignLeftBtn.addEventListener('click', () => {
            console.log('Left alignment button clicked');
            setTextAlignment('left');
        });
    }

    if (alignCenterBtn) {
        alignCenterBtn.addEventListener('click', () => {
            console.log('Center alignment button clicked');
            setTextAlignment('center');
        });
    }

    if (alignRightBtn) {
        alignRightBtn.addEventListener('click', () => {
            console.log('Right alignment button clicked');
            setTextAlignment('right');
        });
    }

    if (alignJustifyBtn) {
        alignJustifyBtn.addEventListener('click', () => {
            console.log('Justify alignment button clicked');
            setTextAlignment('justify');
        });
    }

    // Event listeners for text alignment controls - File Converter Tab
    if (alignLeftBtnFile) {
        alignLeftBtnFile.addEventListener('click', () => {
            setTextAlignment('left');
        });
    }

    if (alignCenterBtnFile) {
        alignCenterBtnFile.addEventListener('click', () => {
            setTextAlignment('center');
        });
    }

    if (alignRightBtnFile) {
        alignRightBtnFile.addEventListener('click', () => {
            setTextAlignment('right');
        });
    }

    if (alignJustifyBtnFile) {
        alignJustifyBtnFile.addEventListener('click', () => {
            setTextAlignment('justify');
        });
    }

    // Event listeners for text alignment controls - Web Converter Tab
    if (alignLeftBtnWeb) {
        alignLeftBtnWeb.addEventListener('click', () => {
            setTextAlignment('left');
        });
    }

    if (alignCenterBtnWeb) {
        alignCenterBtnWeb.addEventListener('click', () => {
            setTextAlignment('center');
        });
    }

    if (alignRightBtnWeb) {
        alignRightBtnWeb.addEventListener('click', () => {
            setTextAlignment('right');
        });
    }

    if (alignJustifyBtnWeb) {
        alignJustifyBtnWeb.addEventListener('click', () => {
            setTextAlignment('justify');
        });
    }

    // Function to update font size across all tabs
    function updateFontSize() {
        const fontSize = fontSizes[currentFontSizeIndex];

        // Update Text Converter Tab
        if (latinText) {
            latinText.style.fontSize = `${fontSize}px`;
        }

        if (tifinaghText) {
            tifinaghText.style.fontSize = `${fontSize}px`;
        }

        if (fontSizeValue) {
            fontSizeValue.textContent = `${fontSize}px`;
        }

        // Update File Converter Tab
        if (fontSizeValueFile) {
            fontSizeValueFile.textContent = `${fontSize}px`;
        }

        // Update Web Converter Tab
        if (fontSizeValueWeb) {
            fontSizeValueWeb.textContent = `${fontSize}px`;
        }

        // Store the font size in localStorage for persistence
        localStorage.setItem('tifinagh-converter-font-size', fontSize);
    }

    // Function to set text alignment across all tabs
    function setTextAlignment(alignment) {
        console.log('Setting text alignment to:', alignment);

        try {
            // Text Converter Tab
            const textAlignBtns = [alignLeftBtn, alignCenterBtn, alignRightBtn, alignJustifyBtn];
            console.log('Text align buttons:', textAlignBtns.map(btn => btn ? 'found' : 'not found'));
            textAlignBtns.forEach(btn => {
                if (btn) btn.classList.remove('active');
            });
        } catch (error) {
            console.error('Error removing active class from text align buttons:', error);
        }

        try {
            // File Converter Tab
            const fileAlignBtns = [alignLeftBtnFile, alignCenterBtnFile, alignRightBtnFile, alignJustifyBtnFile];
            fileAlignBtns.forEach(btn => {
                if (btn) btn.classList.remove('active');
            });
        } catch (error) {
            console.error('Error removing active class from file align buttons:', error);
        }

        try {
            // Web Converter Tab
            const webAlignBtns = [alignLeftBtnWeb, alignCenterBtnWeb, alignRightBtnWeb, alignJustifyBtnWeb];
            webAlignBtns.forEach(btn => {
                if (btn) btn.classList.remove('active');
            });
        } catch (error) {
            console.error('Error removing active class from web align buttons:', error);
        }

        // Add active class to the selected alignment buttons
        try {
            switch (alignment) {
                case 'left':
                    if (alignLeftBtn) alignLeftBtn.classList.add('active');
                    if (alignLeftBtnFile) alignLeftBtnFile.classList.add('active');
                    if (alignLeftBtnWeb) alignLeftBtnWeb.classList.add('active');
                    break;
                case 'center':
                    if (alignCenterBtn) alignCenterBtn.classList.add('active');
                    if (alignCenterBtnFile) alignCenterBtnFile.classList.add('active');
                    if (alignCenterBtnWeb) alignCenterBtnWeb.classList.add('active');
                    break;
                case 'right':
                    if (alignRightBtn) alignRightBtn.classList.add('active');
                    if (alignRightBtnFile) alignRightBtnFile.classList.add('active');
                    if (alignRightBtnWeb) alignRightBtnWeb.classList.add('active');
                    break;
                case 'justify':
                    console.log('Setting justify button active');
                    if (alignJustifyBtn) {
                        console.log('alignJustifyBtn found:', alignJustifyBtn);
                        alignJustifyBtn.classList.add('active');
                    } else {
                        console.log('alignJustifyBtn not found');
                    }
                    if (alignJustifyBtnFile) alignJustifyBtnFile.classList.add('active');
                    if (alignJustifyBtnWeb) alignJustifyBtnWeb.classList.add('active');
                    break;
                default:
                    console.log('Unknown alignment:', alignment);
            }
        } catch (error) {
            console.error('Error adding active class to alignment buttons:', error);
        }

        // Apply alignment to text areas
        try {
            if (latinText) {
                // Remove all alignment classes first
                latinText.classList.remove('text-align-left', 'text-align-center', 'text-align-right', 'text-align-justify');
                // Add the new alignment class
                latinText.classList.add(`text-align-${alignment}`);
                // Also set the inline style for compatibility
                latinText.style.textAlign = alignment;
            }

            if (tifinaghText) {
                // Remove all alignment classes first
                tifinaghText.classList.remove('text-align-left', 'text-align-center', 'text-align-right', 'text-align-justify');
                // Add the new alignment class
                tifinaghText.classList.add(`text-align-${alignment}`);
                // Also set the inline style for compatibility
                tifinaghText.style.textAlign = alignment;
            }

            console.log(`Applied ${alignment} alignment to text areas`);
        } catch (error) {
            console.error('Error applying alignment to text areas:', error);
        }

        // Store the alignment in localStorage for persistence
        localStorage.setItem('tifinagh-converter-text-align', alignment);
    }

    // Load saved settings from localStorage
    function loadSavedSettings() {
        // Load font size
        const savedFontSize = localStorage.getItem('tifinagh-converter-font-size');
        if (savedFontSize) {
            const sizeIndex = fontSizes.indexOf(parseInt(savedFontSize));
            if (sizeIndex !== -1) {
                currentFontSizeIndex = sizeIndex;
                updateFontSize();
            }
        }

        // Load text alignment
        const savedAlignment = localStorage.getItem('tifinagh-converter-text-align');
        if (savedAlignment) {
            setTextAlignment(savedAlignment);
        }
    }

    // Load saved settings on page load
    loadSavedSettings();
});