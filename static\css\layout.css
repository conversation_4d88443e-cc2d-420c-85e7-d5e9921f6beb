/* Layout CSS - Ensures consistent layout across all pages */

/* Reset critical overflow behavior */
html {
    min-height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden; /* Prevent horizontal scrolling */
    overflow-y: scroll; /* إظهار شريط التمرير دائماً لمنع انحياز العناصر */
}

body {
    min-height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Body remains a flex container from Bootstrap classes */
/* main.main-content will grow to fill available space */
main.main-content {
    position: relative; /* Needed for absolute positioning of children */
    flex: 1 1 auto; /* shorthand for flex-grow, flex-shrink, flex-basis */
    min-height: 0; /* Critical for flexbox to allow scroll */
    width: 100%;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
}

/* Main container */
.main-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem;
    box-sizing: border-box;
}

/* Tab system simplified */
.tabs-container {
    width: 100%;
    margin: 0 auto 1.5rem;
    position: relative;
}

.tabs-nav {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

/* Tab content container */
.tab-content-container {
    width: 100%;
    margin: 0 auto;
    position: relative;
}

/* Tab content simplified */
.tab-content {
    display: none; /* Hidden by default */
    width: 100%;
    margin: 0 auto;
    transition: opacity 0.3s ease;
    opacity: 0;
}

/* Active tab content */
.tab-content.active {
    display: block; /* Just display it normally */
    opacity: 1;
}

/* Converter container - common for all tabs */
.converter-container {
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    width: 100%;
    margin: 0 auto 1.5rem;
    min-height: 400px; /* Minimum height but allow growth */
}

/* Language headers - common for all tabs */
.language-headers {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    width: 100%;
    box-sizing: border-box;
    height: 60px;
}

/* Text converter specific */
.text-areas-container {
    display: flex;
    width: 100%;
    padding: 0;
    box-sizing: border-box;
}

.text-area {
    flex: 1;
    padding: 0;
    box-sizing: border-box;
}

/* تم نقل تنسيقات المحررين إلى ملف editors.css */

/* Info container - common for all tabs */
.info-container {
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background-color: #e0f2fe;
    border-radius: 0.375rem;
    color: #0369a1;
    width: 100%;
    box-sizing: border-box;
}

/* File converter specific */
.file-upload-container {
    padding: 1.5rem;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
}

/* Website converter specific */
.web-converter-container {
    padding: 1.5rem;
    width: 100%;
    box-sizing: border-box;
    margin: 0 auto;
}

.url-form {
    margin: 0 auto 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

.website-container {
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
    opacity: 1;
    transition: opacity 0.3s ease;
    position: relative;
}

.website-container-hidden {
    display: block;
    opacity: 0;
    height: 0;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
}

.website-controls {
    display: flex;
    justify-content: space-between;
    margin: 0 auto 1.5rem;
    width: 100%;
    box-sizing: border-box;
}

.website-frame-container {
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    overflow: hidden;
    height: 500px;
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    position: relative;
    background-color: white;
}

.website-frame-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    background-color: white;
    position: relative;
    z-index: 1;
}

/* Responsive styles */
@media (max-width: 768px) {
    .text-areas-container {
        flex-direction: column;
    }

    .text-area {
        width: 100%;
    }

    .latin-area {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    /* تم نقل تنسيقات المحررين للأجهزة المحمولة إلى ملف editors.css */

    .website-controls {
        flex-direction: column;
        gap: 0.5rem;
    }

    .website-controls button {
        width: 100%;
    }
}
