/**
 * Dropdown Z-Index Fix
 * Ensures language dropdown appears above all other elements
 * Version: 1.0.0
 */

/* Language Selector Dropdown Z-Index Fix */
.language-selector {
    position: relative;
    z-index: 9999; /* Very high z-index to ensure it's above everything */
}

.language-selector .dropdown {
    position: relative;
    z-index: 9999;
}

.language-selector .dropdown-menu {
    position: absolute !important;
    z-index: 10000 !important; /* Even higher for the actual dropdown menu */
    top: 100% !important;
    left: auto !important;
    right: 0 !important; /* Align to right edge of button */
    transform: none !important;
    margin-top: 0.125rem !important;
    
    /* Enhanced visual styling */
    min-width: 12rem !important;
    padding: 0.5rem 0 !important;
    border-radius: 0.5rem !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    background-color: white !important;
    
    /* Animation */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
}

.language-selector .dropdown-menu.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) scale(1) !important;
}

/* Ensure dropdown items have proper styling */
.language-selector .dropdown-item {
    position: relative;
    z-index: 10001;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    padding: 0.75rem 1.25rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    text-decoration: none !important;
    border: none !important;
    background: none !important;
    width: 100% !important;
    text-align: left !important;
    transition: all 0.15s ease !important;
    border-left: 3px solid transparent !important;
}

.language-selector .dropdown-item:hover {
    background-color: rgba(59, 130, 246, 0.05) !important;
    color: #1d4ed8 !important;
    border-left-color: #3b82f6 !important;
}

.language-selector .dropdown-item:focus {
    background-color: rgba(59, 130, 246, 0.1) !important;
    color: #1d4ed8 !important;
    outline: none !important;
    border-left-color: #3b82f6 !important;
}

.language-selector .dropdown-item.active {
    background-color: rgba(59, 130, 246, 0.1) !important;
    color: #1d4ed8 !important;
    border-left-color: #3b82f6 !important;
    font-weight: 600 !important;
}

.language-selector .dropdown-item .bi-check2 {
    color: #10b981 !important;
    font-size: 1.1rem !important;
    font-weight: bold !important;
}

.language-selector .dropdown-item .bi-check2.invisible {
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Language button styling */
.language-selector .btn {
    position: relative;
    z-index: 9998;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
    border-radius: 2rem !important;
    border: 1px solid #d1d5db !important;
    background-color: white !important;
    color: #374151 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.language-selector .btn:hover {
    border-color: #3b82f6 !important;
    color: #1d4ed8 !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
    transform: translateY(-1px) !important;
}

.language-selector .btn:focus {
    border-color: #3b82f6 !important;
    color: #1d4ed8 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

.language-selector .btn .bi-globe2 {
    color: #3b82f6 !important;
    font-size: 1.1rem !important;
}

/* Navbar z-index adjustments */
.navbar {
    position: relative;
    z-index: 1000 !important; /* Lower than dropdown */
}

.navbar-nav {
    position: relative;
    z-index: 1001;
}

/* Ensure other page elements don't interfere */
.container,
.container-fluid,
.row,
.col,
.tab-content,
.tab-pane {
    position: relative;
    z-index: 1 !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .language-selector .dropdown-menu {
        min-width: 10rem !important;
        right: 0 !important;
        left: auto !important;
        margin-top: 0.25rem !important;
    }
    
    .language-selector .dropdown-item {
        padding: 0.625rem 1rem !important;
        font-size: 0.9rem !important;
    }
    
    .language-selector .btn {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.875rem !important;
    }
}

/* Extra small screens */
@media (max-width: 576px) {
    .language-selector .dropdown-menu {
        min-width: 9rem !important;
        font-size: 0.85rem !important;
    }
    
    .language-selector .dropdown-item {
        padding: 0.5rem 0.875rem !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .language-selector .dropdown-menu {
        border: 2px solid #000 !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    }
    
    .language-selector .dropdown-item:hover {
        background-color: #000 !important;
        color: #fff !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .language-selector .dropdown-menu {
        transition: none !important;
        animation: none !important;
    }
    
    .language-selector .dropdown-item {
        transition: none !important;
    }
    
    .language-selector .btn {
        transition: none !important;
        transform: none !important;
    }
}

/* Print styles */
@media print {
    .language-selector {
        display: none !important;
    }
}

/* Focus management for accessibility */
.language-selector .dropdown-menu[aria-expanded="true"] {
    display: block !important;
}

.language-selector .dropdown-toggle[aria-expanded="true"] {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Ensure dropdown doesn't get cut off at viewport edges */
.language-selector .dropdown-menu {
    max-height: 300px !important;
    overflow-y: auto !important;
}

/* Custom scrollbar for dropdown if needed */
.language-selector .dropdown-menu::-webkit-scrollbar {
    width: 4px;
}

.language-selector .dropdown-menu::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
}

.language-selector .dropdown-menu::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

.language-selector .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
