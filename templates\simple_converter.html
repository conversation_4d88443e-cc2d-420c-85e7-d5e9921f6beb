{% extends "base.html" %}

{% block title %}Word to Tifinagh Converter{% endblock %}

{% block extra_css %}
<style>
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        padding: 3rem 2rem;
        text-align: center;
        transition: border-color 0.2s;
        cursor: pointer;
    }
    .file-upload-area:hover, .file-upload-area.highlight {
        border-color: #4f46e5;
        background-color: rgba(79, 70, 229, 0.05);
    }
    .file-input {
        display: none;
    }
    .tifinagh-text {
        font-family: 'Noto Sans Tifinagh', sans-serif;
    }
</style>
{% endblock %}

{% block content %}
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm border-0 rounded-4 mb-4">
                    <div class="card-body p-4">
                        <h1 class="h2 text-center text-primary mb-4">Word to <PERSON><PERSON><PERSON>gh Converter <span class="badge bg-primary">v2</span></h1>

                        <div class="file-upload-area mb-4" id="drop-area">
                            <div class="py-3">
                                <i class="bi bi-file-earmark-word text-primary" style="font-size: 3rem;"></i>
                                <p class="mt-3">Drag & drop Word file here or</p>
                                <input type="file" id="file-input" class="file-input" accept=".docx,.doc">
                                <button class="btn btn-primary px-4" id="select-file-btn">Select File</button>
                                <p class="text-muted mt-3 small">Supported files: .docx, .doc</p>
                            </div>
                        </div>

                        <div class="card mb-4 d-none" id="file-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-1">Selected file: <span class="fw-bold" id="file-name"></span></p>
                                        <p class="text-muted mb-0">File size: <span id="file-size"></span></p>
                                    </div>
                                    <button class="btn btn-outline-danger" id="change-file-btn">
                                        <i class="bi bi-trash me-2"></i>Delete
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-danger d-none" id="error-message"></div>

                        <div class="mb-4 d-none" id="progress-container">
                            <label class="form-label d-flex justify-content-between">
                                <span id="conversion-status">Converting...</span>
                                <span id="progress-percentage">0%</span>
                            </label>
                            <div class="progress" style="height: 1rem;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" id="progress-bar" style="width: 0%"></div>
                            </div>
                        </div>

                        <div class="text-center" id="convert-btn-container">
                            <button class="btn btn-success btn-lg px-5" id="convert-btn" disabled>Convert to Tifinagh</button>
                        </div>

                        <div class="text-center mt-4 d-none" id="result-container">
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                File converted successfully!
                            </div>
                            <a href="#" class="btn btn-primary" id="download-btn">
                                <i class="bi bi-download me-2"></i>
                                Download Converted File
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card border-start border-4 border-primary mb-4">
                    <div class="card-body">
                        <h3 class="h5 text-primary">Tips for Opening Converted Word Files</h3>
                        <ul class="list-unstyled mt-3">
                            <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i><strong>Open Word First</strong>: Instead of double-clicking the file, open Microsoft Word first, then use the "Open" option (File > Open) to open the file.</li>
                            <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i><strong>Use Text Recovery Converter</strong>: If Word suggests using the "Text Recovery Converter," try this option.</li>
                            <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i><strong>Check Permissions</strong>: Make sure you have sufficient permissions to access the file and folder.</li>
                            <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i><strong>Check Disk Space</strong>: Ensure you have enough space on your hard drive.</li>
                            <li class="mb-2"><i class="bi bi-check-circle-fill text-primary me-2"></i><strong>Try Another Application</strong>: Try opening the file in another application like LibreOffice Writer or Google Docs.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
            const dropArea = document.getElementById('drop-area');
            const fileInput = document.getElementById('file-input');
            const selectFileBtn = document.getElementById('select-file-btn');
            const changeFileBtn = document.getElementById('change-file-btn');
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');
            const fileSize = document.getElementById('file-size');
            const convertBtn = document.getElementById('convert-btn');
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            const progressPercentage = document.getElementById('progress-percentage');
            const resultContainer = document.getElementById('result-container');
            const downloadBtn = document.getElementById('download-btn');
            const errorMessage = document.getElementById('error-message');

            let selectedFile = null;

            // إضافة مستمعي الأحداث
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                dropArea.classList.add('highlight');
            }

            function unhighlight() {
                dropArea.classList.remove('highlight');
            }

            dropArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length > 0) {
                    handleFile(files[0]);
                }
            }

            selectFileBtn.addEventListener('click', function() {
                fileInput.click();
            });

            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFile(this.files[0]);
                }
            });

            changeFileBtn.addEventListener('click', function() {
                resetFileSelection();
                fileInput.click();
            });

            function handleFile(file) {
                // التحقق من نوع الملف
                const fileExt = file.name.split('.').pop().toLowerCase();
                if (fileExt !== 'docx' && fileExt !== 'doc') {
                    showError('Unsupported file. Please select a Word file (.docx or .doc).');
                    return;
                }

                // Check file size (max 10MB)
                if (file.size > 10 * 1024 * 1024) {
                    showError('File is too large. Maximum size is 10MB.');
                    return;
                }

                // إخفاء رسالة الخطأ
                hideError();

                // عرض معلومات الملف
                selectedFile = file;
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.classList.remove('d-none');
                dropArea.classList.add('d-none');
                convertBtn.disabled = false;
                resultContainer.classList.add('d-none');
            }

            function formatFileSize(bytes) {
                if (bytes < 1024) return bytes + ' bytes';
                else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
                else return (bytes / 1048576).toFixed(1) + ' MB';
            }

            function resetFileSelection() {
                selectedFile = null;
                fileInput.value = '';
                fileInfo.classList.add('d-none');
                dropArea.classList.remove('d-none');
                convertBtn.disabled = true;
                progressContainer.classList.add('d-none');
                progressBar.style.width = '0%';
                resultContainer.classList.add('d-none');
                // Restablecer el texto de estado de conversión
                document.getElementById('conversion-status').textContent = "Converting...";
                // Asegurarse de que el botón de conversión sea visible
                document.getElementById('convert-btn-container').classList.remove('d-none');
                hideError();
            }

            function showError(message) {
                errorMessage.textContent = message;
                errorMessage.classList.remove('d-none');
            }

            function hideError() {
                errorMessage.classList.add('d-none');
            }

            convertBtn.addEventListener('click', function() {
                if (!selectedFile) return;

                // عرض شريط التقدم
                progressContainer.classList.remove('d-none');
                convertBtn.disabled = true;

                // إنشاء FormData
                const formData = new FormData();
                formData.append('file', selectedFile);

                // محاكاة تقدم التحويل
                let progress = 0;
                const interval = setInterval(function() {
                    if (progress < 90) {
                        progress += Math.random() * 5;
                        updateProgress(progress);
                    }
                }, 200);

                // إرسال الطلب
                fetch('/api/convert-file-simple', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    clearInterval(interval);
                    updateProgress(95);

                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error(text || 'Error during conversion');
                        });
                    }

                    return response.blob();
                })
                .then(blob => {
                    updateProgress(100);

                    // Cambiar el texto de "Converting..." a "Converted"
                    document.getElementById('conversion-status').textContent = "Converted";

                    // Create download link
                    const url = URL.createObjectURL(blob);
                    downloadBtn.href = url;
                    const outputFilename = selectedFile.name.replace(/\.[^/.]+$/, '_tifinagh$&');
                    downloadBtn.download = outputFilename;

                    // Show result section
                    resultContainer.classList.remove('d-none');

                    // Ocultar el botón de conversión
                    document.getElementById('convert-btn-container').classList.add('d-none');

                    // Mantener visible la barra de progreso con 100%
                    // No ocultamos progressContainer para mostrar "Converted" y 100%

                    // Descarga automática
                    setTimeout(() => {
                        downloadBtn.click();
                    }, 500);
                })
                .catch(error => {
                    clearInterval(interval);
                    progressContainer.classList.add('d-none');
                    convertBtn.disabled = false;
                    // Asegurarse de que el botón de conversión sea visible en caso de error
                    document.getElementById('convert-btn-container').classList.remove('d-none');
                    showError('Error during conversion: ' + error.message);
                });
            });

            function updateProgress(percent) {
                const smoothPercent = Math.min(100, Math.round(percent * 10) / 10);
                progressBar.style.width = smoothPercent + '%';
                progressPercentage.textContent = smoothPercent + '%';

                // تغيير لون شريط التقدم حسب النسبة
                if (smoothPercent < 30) {
                    progressBar.classList.remove('bg-warning', 'bg-success');
                    progressBar.classList.add('bg-danger');
                } else if (smoothPercent < 70) {
                    progressBar.classList.remove('bg-danger', 'bg-success');
                    progressBar.classList.add('bg-warning');
                } else {
                    progressBar.classList.remove('bg-danger', 'bg-warning');
                    progressBar.classList.add('bg-success');
                }
            }
        });
</script>
{% endblock %}
