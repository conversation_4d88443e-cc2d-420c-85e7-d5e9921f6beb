{% extends "base.html" %}

{% block title %}About - Latin to Tifinagh Converter{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="about-header mb-4">
        <h1 class="about-title" data-lang-key="about-title">About Latin to Tifinagh Converter</h1>
    </div>

    <div class="about-content">
        <div class="about-grid">
            <div class="about-card">
                <div class="about-card-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V11H13V17ZM13 9H11V7H13V9Z" fill="currentColor"/>
                    </svg>
                </div>
                <h2 class="about-card-title" data-lang-key="about-what">What is Tifinagh?</h2>
                <div class="about-card-content" data-lang-key="about-what-content">
                    <p>
                        Tif<PERSON><PERSON> (ⵜⵉⴼⵉⵏⴰⵖ) is an abjad script used to write the Amazigh (Berber) languages.
                        It's one of the three official scripts in Morocco for writing Amazigh, alongside Arabic and Latin scripts.
                    </p>
                    <p>
                        The modern standardized form is called Neo-Tifinagh, which was developed in the 20th century based on ancient Libyco-Berber scripts.
                    </p>
                </div>
            </div>

            <div class="about-card">
                <div class="about-card-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19.14 12.94C19.18 12.64 19.2 12.33 19.2 12C19.2 11.68 19.18 11.36 19.13 11.06L21.16 9.48C21.34 9.34 21.39 9.07 21.28 8.87L19.36 5.55C19.24 5.33 18.99 5.26 18.77 5.33L16.38 6.29C15.88 5.91 15.35 5.59 14.76 5.35L14.4 2.81C14.36 2.57 14.16 2.4 13.92 2.4H10.08C9.84 2.4 9.65 2.57 9.61 2.81L9.25 5.35C8.66 5.59 8.12 5.92 7.63 6.29L5.24 5.33C5.02 5.25 4.77 5.33 4.65 5.55L2.74 8.87C2.62 9.08 2.66 9.34 2.86 9.48L4.89 11.06C4.84 11.36 4.8 11.69 4.8 12C4.8 12.31 4.82 12.64 4.87 12.94L2.84 14.52C2.66 14.66 2.61 14.93 2.72 15.13L4.64 18.45C4.76 18.67 5.01 18.74 5.23 18.67L7.62 17.71C8.12 18.09 8.65 18.41 9.24 18.65L9.6 21.19C9.65 21.43 9.84 21.6 10.08 21.6H13.92C14.16 21.6 14.36 21.43 14.39 21.19L14.75 18.65C15.34 18.41 15.88 18.09 16.37 17.71L18.76 18.67C18.98 18.75 19.23 18.67 19.35 18.45L21.27 15.13C21.39 14.91 21.34 14.66 21.15 14.52L19.14 12.94ZM12 15.6C10.02 15.6 8.4 13.98 8.4 12C8.4 10.02 10.02 8.4 12 8.4C13.98 8.4 15.6 10.02 15.6 12C15.6 13.98 13.98 15.6 12 15.6Z" fill="currentColor"/>
                    </svg>
                </div>
                <h2 class="about-card-title" data-lang-key="about-how">How This Converter Works</h2>
                <div class="about-card-content" data-lang-key="about-how-content">
                    <p>
                        This converter uses a mapping system to transform Latin characters into their Tifinagh equivalents.
                        The conversion is done character by character, following standardized transliteration rules.
                    </p>
                    <p>
                        For website conversion, we use JavaScript to dynamically replace text content on web pages without modifying the original source.
                    </p>
                </div>
            </div>

            <div class="about-card">
                <div class="about-card-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H19C20.11 21 21 20.1 21 19V5C21 3.9 20.11 3 19 3ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" fill="currentColor"/>
                    </svg>
                </div>
                <h2 class="about-card-title" data-lang-key="about-features">Features</h2>
                <div class="about-card-content">
                    <ul class="feature-list">
                        <li data-lang-key="about-feature-1">Convert Latin text to Tifinagh instantly</li>
                        <li data-lang-key="about-feature-2">Upload and convert text files (.txt)</li>
                        <li data-lang-key="about-feature-3">Convert text on any website to Tifinagh</li>
                        <li data-lang-key="about-feature-4">Bilingual interface (English and Amazigh)</li>
                    </ul>
                </div>
            </div>

            <div class="about-card">
                <div class="about-card-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" fill="currentColor"/>
                    </svg>
                </div>
                <h2 class="about-card-title" data-lang-key="about-contact">Contact</h2>
                <div class="about-card-content" data-lang-key="about-contact-content">
                    <p>
                        For questions, suggestions, or feedback, please contact us at:
                        <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
