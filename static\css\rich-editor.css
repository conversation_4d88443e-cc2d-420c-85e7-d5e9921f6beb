/**
 * Rich Text Editor styles for the Tifinagh Converter
 */

/* Editor toggle buttons */
.editor-toggle {
    display: flex;
    margin-bottom: 10px;
    border-radius: 4px;
    overflow: hidden;
}

.editor-toggle button {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background: #f5f5f5;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.editor-toggle button:first-child {
    border-radius: 4px 0 0 4px;
}

.editor-toggle button:last-child {
    border-radius: 0 4px 4px 0;
}

.editor-toggle button.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Editor containers */
.editor-container {
    display: none;
    width: 100%;
    height: 100%;
    min-height: 250px;
}

.editor-container.active {
    display: block;
}

/* Quill editor customizations */
#advanced-editor-container .ql-editor {
    min-height: 250px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
}

#advanced-editor-container .ql-toolbar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #f9f9f9;
    border-color: var(--border-color);
}

#advanced-editor-container .ql-container {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-color: var(--border-color);
}

/* Table styles within the editor */
.ql-editor table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    border: 1px solid var(--border-color);
}

.ql-editor td,
.ql-editor th {
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    text-align: left;
    vertical-align: top;
    min-width: 2rem;
}

.ql-editor th {
    background-color: var(--bg-light);
    font-weight: bold;
}

/* Ensure tables are visible in the editor */
.ql-clipboard {
    position: fixed;
    display: none;
}

/* Add a special class for pasted tables */
.ql-editor .pasted-table {
    border: 1px solid var(--primary-color);
    margin: 1rem 0;
}

.ql-editor .pasted-table td,
.ql-editor .pasted-table th {
    border: 1px solid var(--border-color);
    padding: 0.75rem;
}

/* Convert tables button */
.convert-tables-btn {
    display: none;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    cursor: pointer;
    color: var(--text-light);
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    transition: all var(--transition-speed) ease;
}

.convert-tables-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.05);
}

/* Tifinagh text area with tables */
.tifinagh-text table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.tifinagh-text table th,
.tifinagh-text table td {
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    text-align: left;
    vertical-align: top;
}

.tifinagh-text table th {
    background-color: var(--bg-light);
    font-weight: bold;
}

/* Zebra striping for table rows */
.tifinagh-text table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Hover effect for table rows */
.tifinagh-text table tr:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
}

/* RTL support */
.ql-editor[dir="rtl"] {
    text-align: right;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .editor-toggle button {
        padding: 6px 8px;
        font-size: 12px;
    }

    #advanced-editor-container .ql-toolbar {
        flex-wrap: wrap;
    }

    .ql-editor table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}
