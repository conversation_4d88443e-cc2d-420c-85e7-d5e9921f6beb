/*
 * Unified Tab System CSS
 * This file provides consistent styling for tabs across the application
 */

/* Tab navigation container */
.tabs-container {
    width: 100%;
    margin: 0 auto 1.5rem;
    box-sizing: border-box;
}

/* Tab navigation */
.tabs-nav {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    overflow-x: auto; /* Allow scrolling on small screens */
    -webkit-overflow-scrolling: touch;
}

/* Tab buttons */
.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    margin: 0 0.25rem;
}

.tab-btn:hover {
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb, 0, 123, 255), 0.05);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    font-weight: 600;
    box-shadow: 0 1px 0 var(--primary-color);
}

/* Tab content container */
.tab-content-container {
    position: relative;
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
}

/* Tab content */
.tab-content {
    display: none;
    width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.tab-content.active {
    display: block !important;
    opacity: 1;
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tabs-nav {
        flex-wrap: nowrap;
        justify-content: flex-start;
    }

    .tab-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Accessibility improvements */
.tab-btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

.tab-btn:focus:not(:focus-visible) {
    outline: none;
}

/* Animation for tab transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tab-content.active {
    animation: fadeInUp 0.4s ease forwards;
}
