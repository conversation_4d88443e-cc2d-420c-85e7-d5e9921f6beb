/**
 * محول ملفات بسيط - يحتفظ فقط بوظيفة التحويل المتكامل
 */
document.addEventListener('DOMContentLoaded', function() {
    // عناصر DOM الأساسية
    const fileUpload = document.getElementById('file-upload');
    const dropArea = document.getElementById('drop-area');
    const fileStatus = document.querySelector('.file-status');
    const fileUploadArea = document.querySelector('.file-upload-area');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const removeFileBtn = document.getElementById('remove-file');
    const progressContainer = document.querySelector('.progress-container');
    const progressBar = document.getElementById('file-progress-bar');
    
    // متغيرات الحالة
    let selectedFile = null;
    const maxFileSize = 10 * 1024 * 1024; // 10 ميغابايت
    
    // إضافة مستمعي الأحداث بشكل آمن
    if (dropArea) {
        dropArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dropArea.classList.add('dragover');
        });
        
        dropArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dropArea.classList.remove('dragover');
        });
        
        dropArea.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dropArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
    }
    
    if (fileUpload) {
        fileUpload.addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
    }
    
    if (removeFileBtn) {
        removeFileBtn.addEventListener('click', resetFileSelection);
    }
    
    // زر اختيار الملف
    const selectFileBtn = document.querySelector('.select-file-btn');
    if (selectFileBtn && fileUpload) {
        selectFileBtn.addEventListener('click', function() {
            fileUpload.click();
        });
    }
    
    // معالجة الملف المختار
    function handleFile(file) {
        // تحديد ما إذا كان الملف مدعومًا
        const fileExt = file.name.split('.').pop().toLowerCase();
        if (fileExt !== 'txt' && fileExt !== 'docx') {
            showToast('خطأ: تنسيق الملف غير مدعوم. الرجاء اختيار ملف .txt أو .docx.', 'error', 5000);
            return;
        }
        
        // التحقق من حجم الملف
        if (file.size > maxFileSize) {
            showToast('خطأ: حجم الملف كبير جدًا. الحد الأقصى هو 10 ميغابايت.', 'error', 5000);
            return;
        }
        
        // تعيين الملف المختار
        selectedFile = file;
        
        // عرض معلومات الملف
        if (fileName) fileName.textContent = file.name;
        if (fileSize) fileSize.textContent = formatFileSize(file.size);
        if (fileStatus) fileStatus.classList.remove('d-none');
        if (fileUploadArea) fileUploadArea.classList.add('d-none');
        
        // بدء عملية التحويل تلقائيًا
        if (fileExt === 'docx') {
            showToast('جاري تحويل ملف Word...', 'info', 3000);
        }
        translateFile();
    }
    
    // إعادة تعيين اختيار الملف
    function resetFileSelection() {
        selectedFile = null;
        if (fileUpload) fileUpload.value = '';
        if (fileStatus) fileStatus.classList.add('d-none');
        if (fileUploadArea) fileUploadArea.classList.remove('d-none');
        if (progressContainer) progressContainer.classList.add('d-none');
        if (progressBar) progressBar.style.width = '0%';
    }
    
    // تحويل الملف
    function translateFile() {
        if (!selectedFile) return;
        
        // عرض شريط التقدم
        if (progressContainer) progressContainer.classList.remove('d-none');
        
        // إنشاء FormData
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        // تحديد تنسيق الإخراج المطلوب (نفس تنسيق الملف المدخل)
        const fileExt = selectedFile.name.split('.').pop().toLowerCase();
        formData.append('output_format', fileExt);
        
        // متتبع التقدم
        let lastProgress = 0;
        let progressInterval = setInterval(function() {
            // محاكاة تقدم التحويل
            if (lastProgress < 90) {
                lastProgress += Math.random() * 3;
                updateProgress(lastProgress);
            }
        }, 200);
        
        // طلب التحويل
        fetch('/api/convert-file', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // عرض تقدم 95% عند استلام الاستجابة
            clearInterval(progressInterval);
            updateProgress(95);
            
            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(`خطأ في الخادم: ${text || response.status}`);
                });
            }
            
            // العودة إلى البيانات الثنائية للملف
            return response.blob();
        })
        .then(blob => {
            // إكمال شريط التقدم
            updateProgress(100);
            
            // تنزيل الملف المحول
            const fileExt = selectedFile.name.split('.').pop().toLowerCase();
            const outputFilename = selectedFile.name.replace(`.${fileExt}`, `-tifinagh.${fileExt}`);
            downloadFile(blob, outputFilename);
            
            // عرض رسالة نجاح
            if (fileExt === 'docx') {
                showToast(`تم تحويل ملف Word بنجاح! جارِ تنزيل "${outputFilename}". يرجى فتح الملف باستخدام Microsoft Word مباشرة.`, 'success', 10000);
            } else {
                showToast(`تم تحويل الملف بنجاح! جارِ تنزيل "${outputFilename}"`, 'success', 5000);
            }
            
            // إخفاء شريط التقدم بعد ثانيتين
            setTimeout(() => {
                if (progressContainer) progressContainer.classList.add('d-none');
            }, 2000);
        })
        .catch(error => {
            clearInterval(progressInterval);
            if (progressContainer) progressContainer.classList.add('d-none');
            console.error('خطأ في تحويل الملف:', error);
            showToast(`فشل في تحويل الملف: ${error.message}`, 'error', 5000);
        });
    }
    
    // تحديث شريط التقدم
    function updateProgress(percent) {
        if (!progressBar) return;
        
        // جعل الحركة سلسة
        const smoothPercent = Math.min(100, Math.round(percent * 10) / 10);
        progressBar.style.width = `${smoothPercent}%`;
        
        // تغيير لون شريط التقدم بناءً على النسبة المئوية
        if (smoothPercent < 30) {
            progressBar.style.backgroundColor = '#f44336'; // أحمر
        } else if (smoothPercent < 70) {
            progressBar.style.backgroundColor = '#ff9800'; // برتقالي
        } else {
            progressBar.style.backgroundColor = '#4CAF50'; // أخضر
        }
    }
    
    // تنزيل الملف
    function downloadFile(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }
    
    // تنسيق حجم الملف
    function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
        else return (bytes / 1048576).toFixed(1) + ' MB';
    }
    
    // دالة لعرض رسائل التنبيه
    function showToast(message, type = 'info', duration = 5000) {
        // التحقق من وجود كائن toast العام
        if (window.toast && typeof window.toast.show === 'function') {
            // استخدام نظام التنبيهات المتقدم إذا كان متاحًا
            window.toast.show({
                type: type,
                message: message,
                duration: duration
            });
        } else {
            // إنشاء عنصر تنبيه بسيط
            const toastElement = document.createElement('div');
            toastElement.className = `simple-toast toast-${type}`;
            toastElement.textContent = message;
            
            // إضافة أنماط CSS للتنبيه
            toastElement.style.position = 'fixed';
            toastElement.style.bottom = '20px';
            toastElement.style.right = '20px';
            toastElement.style.padding = '10px 15px';
            toastElement.style.borderRadius = '4px';
            toastElement.style.color = '#fff';
            toastElement.style.zIndex = '9999';
            toastElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            
            // تعيين لون الخلفية حسب النوع
            switch (type) {
                case 'success':
                    toastElement.style.backgroundColor = '#4CAF50';
                    break;
                case 'error':
                    toastElement.style.backgroundColor = '#f44336';
                    break;
                case 'warning':
                    toastElement.style.backgroundColor = '#ff9800';
                    break;
                case 'info':
                default:
                    toastElement.style.backgroundColor = '#2196F3';
                    break;
            }
            
            // إضافة التنبيه إلى الصفحة
            document.body.appendChild(toastElement);
            
            // إزالة التنبيه بعد المدة المحددة
            setTimeout(() => {
                if (toastElement.parentNode) {
                    toastElement.parentNode.removeChild(toastElement);
                }
            }, duration);
        }
    }
});
