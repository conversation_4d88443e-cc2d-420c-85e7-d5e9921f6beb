<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scrollbar Diagnosis Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .diagnosis-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
        .test-area { width: 100%; height: 200px; border: 1px solid #ccc; padding: 10px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 Simple Text Converter Scrollbar Diagnosis</h1>
    
    <div class="diagnosis-section">
        <h2>1. CSS Files Loading Check</h2>
        <button onclick="checkCSSFiles()">Check CSS Files</button>
        <div id="css-check-result" class="result"></div>
    </div>
    
    <div class="diagnosis-section">
        <h2>2. Element Existence Check</h2>
        <button onclick="checkElements()">Check Elements</button>
        <div id="element-check-result" class="result"></div>
    </div>
    
    <div class="diagnosis-section">
        <h2>3. Computed Styles Check</h2>
        <button onclick="checkComputedStyles()">Check Computed Styles</button>
        <div id="styles-check-result" class="result"></div>
    </div>
    
    <div class="diagnosis-section">
        <h2>4. CSS Rules Conflict Analysis</h2>
        <button onclick="analyzeCSSConflicts()">Analyze CSS Conflicts</button>
        <div id="conflicts-result" class="result"></div>
    </div>
    
    <div class="diagnosis-section">
        <h2>5. Scrollbar Test Area</h2>
        <p>Test scrollbar with long content:</p>
        <textarea class="test-area" placeholder="This should have a scrollbar...">
Line 1 - This is a test of scrollbar functionality
Line 2 - Testing scrollbar visibility
Line 3 - More content to trigger scrollbar
Line 4 - Additional content
Line 5 - Even more content
Line 6 - Testing scrollbar
Line 7 - More lines
Line 8 - Additional lines
Line 9 - Testing overflow
Line 10 - Final test line
Line 11 - Extra content
Line 12 - More extra content
Line 13 - Additional test content
Line 14 - Final additional content
Line 15 - Last line for testing
        </textarea>
        <button onclick="testScrollbarVisibility()">Test Scrollbar Visibility</button>
        <div id="scrollbar-test-result" class="result"></div>
    </div>
    
    <div class="diagnosis-section">
        <h2>6. Fix Application</h2>
        <button onclick="applyScrollbarFix()">Apply Scrollbar Fix</button>
        <div id="fix-result" class="result"></div>
    </div>

    <script>
        function checkCSSFiles() {
            const result = document.getElementById('css-check-result');
            const stylesheets = Array.from(document.styleSheets);
            let messages = [];
            
            const requiredCSS = [
                'scrollbar-fix.css',
                'single-scrollbar-only.css', 
                'simple-editor-scrollbar.css',
                'fix-editor-scrollbar.css'
            ];
            
            requiredCSS.forEach(css => {
                const found = stylesheets.find(sheet => 
                    sheet.href && sheet.href.includes(css)
                );
                if (found) {
                    messages.push(`✅ ${css} loaded from: ${found.href}`);
                } else {
                    messages.push(`❌ ${css} not found`);
                }
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        function checkElements() {
            const result = document.getElementById('element-check-result');
            let messages = [];
            
            const elements = [
                { id: 'text-converter', desc: 'Main container' },
                { id: 'latin-text', desc: 'Latin textarea' },
                { id: 'tifinagh-text', desc: 'Tifinagh output div' }
            ];
            
            elements.forEach(elem => {
                const element = document.getElementById(elem.id);
                if (element) {
                    messages.push(`✅ ${elem.desc} (${elem.id}) found: ${element.tagName}`);
                } else {
                    messages.push(`❌ ${elem.desc} (${elem.id}) not found`);
                }
            });
            
            // Check for .text-area elements
            const textAreas = document.querySelectorAll('.text-area');
            messages.push(`📊 Found ${textAreas.length} .text-area elements`);
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        function checkComputedStyles() {
            const result = document.getElementById('styles-check-result');
            let messages = [];
            
            const latinText = document.getElementById('latin-text');
            const tifinagh = document.getElementById('tifinagh-text');
            
            if (latinText) {
                const latinStyle = getComputedStyle(latinText);
                messages.push(`<strong>Latin Text (#latin-text):</strong>`);
                messages.push(`  overflow-y: ${latinStyle.overflowY}`);
                messages.push(`  overflow-x: ${latinStyle.overflowX}`);
                messages.push(`  scrollbar-width: ${latinStyle.scrollbarWidth}`);
                messages.push(`  height: ${latinStyle.height}`);
                messages.push(`  max-height: ${latinStyle.maxHeight}`);
                messages.push(`  min-height: ${latinStyle.minHeight}`);
                messages.push(`  display: ${latinStyle.display}`);
                messages.push(`  visibility: ${latinStyle.visibility}`);
            } else {
                messages.push(`❌ Latin text element not found`);
            }
            
            if (tifinagh) {
                const tifinagStyle = getComputedStyle(tifinagh);
                messages.push(`<br><strong>Tifinagh Text (#tifinagh-text):</strong>`);
                messages.push(`  overflow-y: ${tifinagStyle.overflowY}`);
                messages.push(`  overflow-x: ${tifinagStyle.overflowX}`);
                messages.push(`  scrollbar-width: ${tifinagStyle.scrollbarWidth}`);
                messages.push(`  height: ${tifinagStyle.height}`);
                messages.push(`  max-height: ${tifinagStyle.maxHeight}`);
                messages.push(`  min-height: ${tifinagStyle.minHeight}`);
                messages.push(`  display: ${tifinagStyle.display}`);
                messages.push(`  visibility: ${tifinagStyle.visibility}`);
            } else {
                messages.push(`❌ Tifinagh text element not found`);
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result';
        }
        
        function analyzeCSSConflicts() {
            const result = document.getElementById('conflicts-result');
            let messages = [];
            
            const latinText = document.getElementById('latin-text');
            if (latinText) {
                // Get all CSS rules that apply to this element
                const rules = [];
                for (let sheet of document.styleSheets) {
                    try {
                        for (let rule of sheet.cssRules || sheet.rules) {
                            if (rule.style && rule.selectorText) {
                                if (latinText.matches(rule.selectorText)) {
                                    if (rule.style.overflowY || rule.style.overflow || rule.style.scrollbarWidth) {
                                        rules.push({
                                            selector: rule.selectorText,
                                            overflowY: rule.style.overflowY,
                                            overflow: rule.style.overflow,
                                            scrollbarWidth: rule.style.scrollbarWidth,
                                            sheet: sheet.href || 'inline'
                                        });
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        // Cross-origin or other access issues
                    }
                }
                
                messages.push(`<strong>CSS Rules affecting #latin-text:</strong>`);
                if (rules.length === 0) {
                    messages.push(`⚠️ No overflow/scrollbar rules found`);
                } else {
                    rules.forEach(rule => {
                        messages.push(`📋 ${rule.selector} (${rule.sheet})`);
                        if (rule.overflowY) messages.push(`  overflow-y: ${rule.overflowY}`);
                        if (rule.overflow) messages.push(`  overflow: ${rule.overflow}`);
                        if (rule.scrollbarWidth) messages.push(`  scrollbar-width: ${rule.scrollbarWidth}`);
                    });
                }
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result';
        }
        
        function testScrollbarVisibility() {
            const result = document.getElementById('scrollbar-test-result');
            const testArea = document.querySelector('.test-area');
            let messages = [];
            
            if (testArea) {
                const style = getComputedStyle(testArea);
                messages.push(`Test area overflow-y: ${style.overflowY}`);
                messages.push(`Test area scrollHeight: ${testArea.scrollHeight}px`);
                messages.push(`Test area clientHeight: ${testArea.clientHeight}px`);
                
                if (testArea.scrollHeight > testArea.clientHeight) {
                    messages.push(`✅ Content exceeds container (scrollbar should be visible)`);
                } else {
                    messages.push(`⚠️ Content fits within container`);
                }
                
                // Check if scrollbar is actually visible
                const hasScrollbar = testArea.scrollHeight > testArea.clientHeight && 
                                   (style.overflowY === 'auto' || style.overflowY === 'scroll');
                
                if (hasScrollbar) {
                    messages.push(`✅ Scrollbar should be visible`);
                } else {
                    messages.push(`❌ Scrollbar may not be visible`);
                }
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        function applyScrollbarFix() {
            const result = document.getElementById('fix-result');
            let messages = [];
            
            try {
                // Apply direct CSS fixes
                const latinText = document.getElementById('latin-text');
                const tifinagh = document.getElementById('tifinagh-text');
                
                if (latinText) {
                    latinText.style.overflowY = 'auto';
                    latinText.style.overflowX = 'hidden';
                    latinText.style.scrollbarWidth = 'auto';
                    latinText.style.msOverflowStyle = 'auto';
                    latinText.style.display = 'block';
                    latinText.style.visibility = 'visible';
                    latinText.style.minHeight = '500px';
                    latinText.style.maxHeight = '500px';
                    messages.push(`✅ Applied fix to Latin text area`);
                }
                
                if (tifinagh) {
                    tifinagh.style.overflowY = 'auto';
                    tifinagh.style.overflowX = 'hidden';
                    tifinagh.style.scrollbarWidth = 'auto';
                    tifinagh.style.msOverflowStyle = 'auto';
                    tifinagh.style.display = 'block';
                    tifinagh.style.visibility = 'visible';
                    tifinagh.style.minHeight = '500px';
                    tifinagh.style.maxHeight = '500px';
                    messages.push(`✅ Applied fix to Tifinagh text area`);
                }
                
                // Add some test content to trigger scrollbar
                if (latinText && latinText.value.length < 100) {
                    latinText.value = 'Test line 1\nTest line 2\nTest line 3\nTest line 4\nTest line 5\nTest line 6\nTest line 7\nTest line 8\nTest line 9\nTest line 10\nTest line 11\nTest line 12\nTest line 13\nTest line 14\nTest line 15\nTest line 16\nTest line 17\nTest line 18\nTest line 19\nTest line 20';
                    messages.push(`✅ Added test content to trigger scrollbar`);
                }
                
                if (tifinagh && tifinagh.textContent.length < 100) {
                    tifinagh.textContent = 'ⴰⵙⵏⴼⴰⵍ\nⵜⴰⵎⴰⵣⵉⵖⵜ\nⴰⵎⴰⵣⵉⵖ\nⵜⵉⴼⵉⵏⴰⵖ\nLine 5\nLine 6\nLine 7\nLine 8\nLine 9\nLine 10\nLine 11\nLine 12\nLine 13\nLine 14\nLine 15\nLine 16\nLine 17\nLine 18\nLine 19\nLine 20';
                    messages.push(`✅ Added test content to Tifinagh area`);
                }
                
                messages.push(`🔄 Please check if scrollbars are now visible`);
                
            } catch (error) {
                messages.push(`❌ Error applying fix: ${error.message}`);
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        // Auto-run checks on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkCSSFiles();
                checkElements();
                checkComputedStyles();
            }, 1000);
        });
    </script>
</body>
</html>
