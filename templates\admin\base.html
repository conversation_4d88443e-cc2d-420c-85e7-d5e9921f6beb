<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>{% block title %}Admin Dashboard - Tifinagh Converter{% endblock %}</title>
    <!-- CSS Reset -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/reset.css') }}?v=1.0.1">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Layout CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout.css') }}?v=1.0.1">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/new-style.css') }}?v=1.0.1">
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Tifinagh&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --sidebar-width: 280px;
        }
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: var(--sidebar-width);
            background-color: #212529;
            color: #fff;
            z-index: 1000;
            transition: all 0.3s;
            overflow-y: auto;
        }
        .sidebar-header {
            padding: 1.5rem 1rem;
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .sidebar-header img {
            height: 45px;
            margin-right: 10px;
        }
        .sidebar-header h5 {
            margin: 0;
            color: #fff;
            font-weight: 600;
        }
        .sidebar-nav {
            padding: 1rem 0;
        }
        .sidebar-nav .nav-item {
            width: 100%;
        }
        .sidebar-nav .nav-link {
            padding: 0.8rem 1rem;
            color: rgba(255, 255, 255, 0.75);
            border-left: 3px solid transparent;
            display: flex;
            align-items: center;
        }
        .sidebar-nav .nav-link i {
            margin-right: 10px;
            font-size: 1.1rem;
        }
        .sidebar-nav .nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.05);
        }
        .sidebar-nav .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: var(--bs-primary);
        }
        .sidebar-footer {
            padding: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        .content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: all 0.3s;
            padding: 20px;
        }
        .topbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background-color: #fff;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }
        .breadcrumb-wrapper {
            flex-grow: 1;
        }
        .breadcrumb {
            margin-bottom: 0;
        }
        .user-dropdown img {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            object-fit: cover;
        }
        .dropdown-menu {
            min-width: 250px;
        }
        .user-info {
            padding: 10px 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        .user-info h6 {
            margin-bottom: 5px;
        }
        .user-info p {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 0;
        }
        /* Card styles */
        .card.stat-card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s, box-shadow 0.2s;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card.stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .stat-card .card-body {
            padding: 1.5rem;
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 1rem;
            font-size: 1.8rem;
            margin-bottom: 1rem;
        }
        .stat-title {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0;
        }
        .activity-item {
            position: relative;
            padding-left: 40px;
            margin-bottom: 1.5rem;
        }
        .activity-item:before {
            content: '';
            position: absolute;
            top: 0;
            left: 15px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--bs-primary);
            z-index: 1;
        }
        .activity-item:after {
            content: '';
            position: absolute;
            top: 10px;
            left: 19px;
            width: 2px;
            height: calc(100% + 10px);
            background-color: #e5e7eb;
            z-index: 0;
        }
        .activity-item:last-child:after {
            display: none;
        }
        .activity-item .time {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 0.25rem;
        }
        .activity-item .description {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }
        .activity-item .meta {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 0.25rem;
        }

        /* Toggle button */
        .form-check-input {
            cursor: pointer;
            width: 3rem;
            height: 1.5rem;
            margin-left: 0;
            background-color: rgba(0, 0, 0, 0.25);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
            background-position: left center;
            background-repeat: no-repeat;
            background-size: contain;
            border: none;
            transition: background-position .15s ease-in-out;
        }
        .form-check-input:checked {
            background-position: right center;
            background-color: var(--bs-primary);
        }
        .form-check-input:focus {
            box-shadow: none;
        }

        /* Color picker */
        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin: 0 5px;
            cursor: pointer;
            display: inline-block;
            border: 3px solid transparent;
        }
        .color-option.selected {
            border-color: #212529;
        }

        /* File uploader */
        .drop-zone {
            border: 2px dashed #e5e7eb;
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .drop-zone:hover {
            border-color: var(--bs-primary);
        }
        .drop-zone i {
            font-size: 2rem;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
            }
            .content {
                margin-left: 0;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .content.sidebar-shown {
                margin-left: var(--sidebar-width);
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-light">
    <div class="admin-layout">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/tifinagh-logo.svg') }}" alt="Logo">
                <h5>Admin Panel</h5>
            </div>
            <ul class="sidebar-nav nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'dashboard' %}active{% endif %}" href="{{ url_for('admin.dashboard') }}">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'translations' %}active{% endif %}" href="{{ url_for('admin.admin_i18n') }}">
                        <i class="bi bi-translate"></i> Translation Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'users' %}active{% endif %}" href="{{ url_for('admin.users') }}">
                        <i class="bi bi-people"></i> User Management
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'interface' %}active{% endif %}" href="{{ url_for('admin.admin_interface') }}">
                        <i class="bi bi-palette"></i> Interface Customization
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'tools' %}active{% endif %}" href="{{ url_for('admin.admin_tools') }}">
                        <i class="bi bi-tools"></i> Tools & Diagnostics
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if active_page == 'policies' %}active{% endif %}" href="{{ url_for('admin.admin_policies') }}">
                        <i class="bi bi-shield-check"></i> Policies & Legal
                    </a>
                </li>
            </ul>
            <div class="sidebar-footer">
                <a href="{{ url_for('admin.logout') }}" class="btn btn-outline-light btn-sm">
                    <i class="bi bi-box-arrow-left"></i> Logout
                </a>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <!-- Topbar -->
            <div class="topbar">
                <button class="btn btn-sm btn-outline-secondary d-lg-none" id="sidebar-toggle">
                    <i class="bi bi-list"></i>
                </button>
                <div class="breadcrumb-wrapper ms-3">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('admin.dashboard') }}">Admin</a></li>
                            {% block breadcrumb %}{% endblock %}
                        </ol>
                    </nav>
                </div>
                <div class="dropdown ms-auto">
                    <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle me-2"></i>
                        <span>Admin</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <div class="user-info">
                                <h6 class="mb-0">Administrator</h6>
                                <p>Last login: {{ last_login }}</p>
                            </div>
                        </li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.users') }}?action=change_password"><i class="bi bi-key me-2"></i> Change Password</a></li>
                        <li><a class="dropdown-item" href="{{ url_for('index') }}" target="_blank"><i class="bi bi-house me-2"></i> View Site</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('admin.logout') }}"><i class="bi bi-box-arrow-right me-2"></i> Logout</a></li>
                    </ul>
                </div>
            </div>

            <!-- Page Content -->
            <div class="container-fluid">
                {% for category, message in get_flashed_messages(with_categories=true) %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            document.querySelector('.sidebar').classList.toggle('show');
            document.querySelector('.content').classList.toggle('sidebar-shown');
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>