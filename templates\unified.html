{% extends "base.html" %}

{% block title %}Tifinagh Converter{% endblock %}

{% block content %}
<div class="main-container">
    <!-- Tabs Navigation -->
    <div class="tabs-container">
        <div class="tabs-nav">
            <button class="tab-btn active" data-tab="text-converter" data-i18n="text_converter.tab_simple_text">Simple Text Converter</button>
            <button class="tab-btn" data-tab="advanced-converter" data-i18n="text_converter.tab_advanced_text">Advanced Text Converter</button>
            <button class="tab-btn" data-tab="file-converter" data-i18n="text_converter.tab_file_converter">File Converter</button>
            <button class="tab-btn" data-tab="web-converter" data-i18n="text_converter.tab_website_converter">Website Converter</button>
        </div>
    </div>

    <div class="tab-content-container">
            <!-- Text Converter Tab -->
            <div class="tab-content active" id="text-converter">
                <div class="converter-container">
                    <div class="language-headers">
                        <div class="language-header latin-header">
                            <span data-i18n="text_converter.latin_header">Latin</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                            </svg>

                            <!-- Text Formatting Controls - Kept only in Text Converter tab -->
                            <div class="header-formatting-controls">
                                <div class="font-size-controls">
                                    <button id="decrease-font-text" class="font-size-btn" title="Decrease font size">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM4.5 7.5a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1h-7z"/>
                                        </svg>
                                    </button>
                                    <span id="font-size-value-text" class="font-size-value">16px</span>
                                    <button id="increase-font-text" class="font-size-btn" title="Increase font size">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="text-align-controls">
                                    <button id="align-left-text" class="text-align-btn active" title="Align left">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M2 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                                        </svg>
                                    </button>
                                    <button id="align-center-text" class="text-align-btn" title="Align center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M4 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm2-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-2-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                                        </svg>
                                    </button>
                                    <button id="align-right-text" class="text-align-btn" title="Align right">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M6 12.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm4-3a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm-4-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                                        </svg>
                                    </button>
                                    <button id="align-justify-text" class="text-align-btn" title="Justify">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M2 12.5a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5zm0-3a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11a.5.5 0 0 1-.5-.5z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                        </div>
                        <div class="swap-btn-container">
                            <button class="swap-btn" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="language-header tifinagh-header">
                            <span style="margin-left: auto;" data-i18n="text_converter.tifinagh_header">Tifinagh</span>
                        </div>
                    </div>





                    <div class="text-areas-container">
                        <div class="text-area latin-area">
                            <textarea id="latin-text" class="formatted preserve-whitespace text-align-left" placeholder="Enter text in Latin script..." data-i18n="text_converter.latin_placeholder"></textarea>

                            <div class="text-area-footer">
                                <div class="character-count" data-i18n="text_converter.characters_count">0 characters</div>
                                <div class="text-actions">
                                    <button class="clear-btn" id="clear-latin" title="Clear">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                            <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                        </svg>
                                    </button>
                                    <button class="convert-tables-btn" id="convert-tables-btn" title="Convert Tables" style="display: none;">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zm8 0A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm-8 8A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm8 0A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5v-3z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="text-area tifinagh-area">
                            <div id="tifinagh-text" class="tifinagh-text formatted preserve-whitespace text-align-left"></div>
                            <div class="text-area-footer">
                                <div class="character-count" data-i18n="text_converter.characters_count">0 characters</div>
                                <div class="text-actions">
                                    <button class="copy-btn" id="copy-tifinagh" title="Copy">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                                            <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                                        </svg>
                                    </button>
                                    <button class="download-btn" id="download-tifinagh" title="Download">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                                            <path d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="info-container">
                    <div class="info-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                        </svg>
                    </div>
                    <div class="info-text" data-i18n="text_converter.info_text">
                        Type or paste Latin text in the left box to convert it to Tifinagh script. The conversion happens automatically as you type.
                    </div>
                </div>
            </div>

            <!-- Advanced Text Converter Tab -->
            <div class="tab-content" id="advanced-converter">
                <div class="converter-container">
                    <div class="language-headers">
                        <div class="language-header latin-header">
                            <span data-i18n="text_converter.latin_header">Latin</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                            </svg>
                        </div>
                        <div class="swap-btn-container">
                            <button class="swap-btn" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="language-header tifinagh-header">
                            <span style="margin-left: auto;" data-i18n="text_converter.tifinagh_header">Tifinagh</span>
                        </div>
                    </div>

                    <div class="advanced-editor-wrapper">
                        <div id="document-editor">
                            <div id="document-editor-toolbar"></div>
                            <div id="document-editor-content" class="document-editor-content"></div>
                        </div>
                        <div class="text-area-footer">
                            <div class="character-count" data-i18n="text_converter.characters_count">0 characters</div>
                            <div class="text-actions">
                                <button id="clear-document-btn" class="clear-btn" title="Clear">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                        <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                    </svg>
                                </button>
                                <button id="copy-document-btn" class="copy-btn" title="Copy">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                                        <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                                    </svg>
                                </button>

                                <button id="convert-document-btn" class="convert-btn" title="Convert to Tifinagh">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                                    </svg>
                                    <span data-i18n="text_converter.convert_button">Convert</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="info-container">
                    <div class="info-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                        </svg>
                    </div>
                    <div class="info-text" data-i18n="text_converter.advanced_info_text">
                        This advanced editor lets you convert detailed documents to Tifinagh, preserving all styles and formatting.
                    </div>
                </div>
            </div>

            <!-- File Converter Tab -->
            <div class="tab-content" id="file-converter">
                <div class="converter-container">
                    <div class="language-headers">
                        <div class="language-header latin-header">
                            <span data-i18n="text_converter.latin_header">Latin</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                            </svg>
                        </div>
                        <div class="swap-btn-container">
                            <button class="swap-btn" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="language-header tifinagh-header">
                            <span style="margin-left: auto;" data-i18n="text_converter.tifinagh_header">Tifinagh</span>
                        </div>
                    </div>

                    <!-- File Upload Area -->
                    <div class="file-upload-container">
                        <div class="file-upload-area" id="drop-area">
                            <div class="file-upload-content">
                                <div class="file-icon d-flex justify-content-center">
                                    <i class="bi bi-file-earmark-word text-primary me-2" style="font-size: 3rem;"></i>
                                    <i class="bi bi-file-earmark-text text-success" style="font-size: 3rem;"></i>
                                </div>
                                <h3 data-i18n="file_converter.drag_drop">Drag & Drop File</h3>
                                <p class="upload-divider" data-i18n="file_converter.or">or</p>
                                <input type="file" id="file-upload" accept=".txt,.docx,.doc" hidden>
                                <button class="btn btn-primary px-4" id="select-file-btn">
                                    <i class="bi bi-upload me-2"></i><span data-i18n="file_converter.select_file">Select File</span>
                                </button>
                                <p class="file-format-info" data-i18n="file_converter.supported_formats">Supported formats: .txt, .docx, .doc</p>
                            </div>
                        </div>

                        <!-- File Info Card -->
                        <div class="card mb-2 d-none" id="file-info-card">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-1" data-i18n="file_converter.selected_file">Selected file: <span class="fw-bold" id="file-name"></span></p>
                                        <p class="text-muted mb-0" data-i18n="file_converter.file_size">File size: <span id="file-size"></span></p>
                                    </div>
                                    <button class="btn btn-outline-danger" id="remove-file">
                                        <i class="bi bi-trash me-2"></i><span data-i18n="common.delete">Delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-2 d-none" id="progress-container">
                            <label class="form-label d-flex justify-content-between mb-1">
                                <span data-i18n="file_converter.converting">Converting...</span>
                                <span id="progress-percentage">0%</span>
                            </label>
                            <div class="progress" style="height: 1rem;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" id="file-progress-bar" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- Convert Button - Will be shown after file selection -->
                        <div class="text-center mb-2 d-none" id="convert-btn-container">
                            <button class="btn btn-danger btn-lg px-5" id="convert-btn">
                                <i class="bi bi-translate me-2"></i><span data-i18n="file_converter.convert_button">Convert to Tifinagh</span>
                            </button>
                        </div>

                        <!-- Result Container -->
                        <div class="text-center mt-1 d-none" id="result-container">
                            <div class="alert alert-success py-2">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <span data-i18n="file_converter.conversion_success">File converted successfully. Download will start automatically, or click "Download File" to proceed.</span>
                            </div>
                            <a href="#" class="btn btn-danger mt-1" id="download-btn">
                                <i class="bi bi-download me-2"></i>
                                <span data-i18n="file_converter.download_button">Download File</span>
                            </a>
                        </div>

                        <!-- File Preview Section (for text files) -->
                        <div class="file-preview-container d-none">
                            <div class="file-preview-header">
                                <h4 data-i18n="file_converter.file_preview">File Preview</h4>
                                <div class="preview-controls">
                                    <button id="show-more-preview" class="preview-control-btn" title="Show more">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M8 4a.5.5 0 0 1 .5.5v5.793l2.146-2.147a.5.5 0 0 1 .708.708l-3 3a.5.5 0 0 1-.708 0l-3-3a.5.5 0 1 1 .708-.708L7.5 10.293V4.5A.5.5 0 0 1 8 4z"/>
                                        </svg>
                                    </button>
                                    <button id="show-less-preview" class="preview-control-btn" title="Show less">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M8 12a.5.5 0 0 0 .5-.5V5.707l2.146 2.147a.5.5 0 0 0 .708-.708l-3-3a.5.5 0 0 0-.708 0l-3 3a.5.5 0 1 0 .708.708L7.5 5.707V11.5a.5.5 0 0 0 .5.5z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="file-preview-content">
                                <pre id="file-preview-text"></pre>
                            </div>
                            <div class="file-preview-footer">
                                <span id="preview-status" data-i18n="file_converter.preview_status">Showing first 20 lines</span>
                                <div class="preview-actions">
                                    <button id="preview-search-btn" class="preview-action-btn" title="Search in file">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="info-container">
                    <div class="info-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                        </svg>
                    </div>
                    <div class="info-text" data-i18n="file_converter.info_text">
                        Supported file formats: .txt and .docx (Word).<br>
                        Our Word converter preserves all formatting including colors, styles, font sizes, tables, list markers and footnotes.<br>
                        <em><i class="bi bi-shield-lock-fill text-success"></i> Your privacy is protected! No data is transmitted or stored.</em>
                    </div>
                </div>
            </div>

            <!-- Website Converter Tab -->
            <div class="tab-content" id="web-converter">
                <div class="converter-container">
                    <div class="language-headers">
                        <div class="language-header latin-header">
                            <span data-i18n="text_converter.latin_header">Latin</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                            </svg>
                        </div>
                        <div class="swap-btn-container">
                            <button class="swap-btn" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="language-header tifinagh-header">
                            <span style="margin-left: auto;" data-i18n="text_converter.tifinagh_header">Tifinagh</span>
                        </div>
                    </div>

                    <!-- Website URL Input -->
                    <div class="web-converter-container">
                        <div class="web-converter-intro">
                            <p data-i18n="website_converter.intro_text">Enter a website URL below and click "Convert Website" to open it with Tifinagh text conversion in a new tab.</p>
                        </div>

                        <form id="url-form" class="url-form">
                            <div class="url-input-container">
                                <input type="url" id="website-url" placeholder="Enter website URL (e.g., https://example.com)" data-i18n="website_converter.url_placeholder" required>
                                <button type="submit" class="load-website-btn" data-i18n="website_converter.convert_button">
                                    Convert Website
                                </button>
                            </div>
                            <div class="url-examples">
                                <span data-i18n="website_converter.try_examples">Try: </span>
                                <a href="#" class="example-url" data-url="https://en.wikipedia.org/wiki/Tifinagh">Wikipedia</a>
                                <a href="#" class="example-url" data-url="https://www.bbc.com">BBC</a>
                                <a href="#" class="example-url" data-url="https://www.amazigh.info">Amazigh.info</a>
                            </div>

                        </form>


                    </div>
                </div>

                <div class="info-container">
                    <div class="info-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
                        </svg>
                    </div>
                    <div class="info-text" data-i18n="website_converter.info_text">
                        The website will open in a new tab with Tifinagh conversion applied. You can browse the website normally and all text will be automatically converted to Tifinagh script.
                    </div>
                </div>
            </div>
    </div>
</div>

<!-- Error display section with detailed solutions -->
<div class="error-modal-container" id="error-modal" style="display: none;">
    <div class="error-modal">
        <div class="error-modal-header">
            <h3 id="error-modal-title" data-i18n="common.error">Error</h3>
            <span class="close-error-modal" id="close-error-modal">&times;</span>
        </div>
        <div class="error-modal-body">
            <p id="error-modal-message"></p>
            <div id="error-modal-details" style="display: none;">
                <h4 data-i18n="error.details">Error Details</h4>
                <pre id="error-modal-stack"></pre>
            </div>
            <div id="error-modal-solution">
                <h4 data-i18n="error.suggested_solutions">Suggested Solutions</h4>
                <ul id="error-modal-solutions-list">
                </ul>
            </div>
        </div>
        <div class="error-modal-footer">
            <button id="error-modal-copy" class="btn btn-sm" data-i18n="error.copy_details">Copy Details</button>
            <button id="error-modal-close" class="btn btn-primary btn-sm" data-i18n="common.close">Close</button>
            <button id="error-modal-retry" class="btn btn-success btn-sm" data-i18n="error.retry">Retry</button>
        </div>
    </div>
</div>

<script>
    // Advanced error handler
    const errorHandler = {
        // Store the last error
        lastError: null,

        // Store the last failed operation
        lastOperation: null,

        // Known error types and their solutions
        knownErrors: {
            'NetworkError': {
                title: 'Network Connection Error',
                solutions: [
                    'Check your internet connection.',
                    'Verify that the server is running.',
                    'There might be a server issue, try again later.'
                ]
            },
            'FileError': {
                title: 'File Processing Error',
                solutions: [
                    'Make sure the file is in a supported format (.txt or .docx).',
                    'Check that the file is not corrupted or empty.',
                    'Ensure the file size is less than 10MB.',
                    'There might be an encoding issue, try saving the file with UTF-8 encoding.'
                ]
            },
            'ConversionError': {
                title: 'Conversion Process Error',
                solutions: [
                    'The text might contain unsupported characters, try simplifying it.',
                    'Try splitting the text into smaller parts and convert them separately.',
                    'If the text contains complex tables, try converting them separately.'
                ]
            },
            'ServerError': {
                title: 'Server Error',
                solutions: [
                    'The server might be busy, try again in a moment.',
                    'There might be a configuration issue, contact the system administrator.',
                    'If the problem persists, you might need to restart the application.'
                ]
            },
            'EncodingError': {
                title: 'Text Encoding Error',
                solutions: [
                    'Make sure the text is written in Latin characters only.',
                    'The text might contain unsupported special characters, try removing them.',
                    'Try saving the file with UTF-8 encoding and uploading it again.'
                ]
            }
        },

        // Show error modal
        showError: function(error, operation = null) {
            // Store error and operation
            this.lastError = error;
            this.lastOperation = operation;

            // Set error modal title
            const errorTitle = document.getElementById('error-modal-title');

            // Set error message
            const errorMessage = document.getElementById('error-modal-message');

            // Determine error type
            let errorType = 'UnknownError';
            let errorObj = { title: 'Unknown Error', solutions: ['Try again.'] };

            // Analyze error type from message
            if (error.message) {
                if (error.message.includes('network') || error.message.includes('connect') || error.message.includes('timeout')) {
                    errorType = 'NetworkError';
                } else if (error.message.includes('file') || error.message.includes('format') || error.message.includes('size')) {
                    errorType = 'FileError';
                } else if (error.message.includes('convert') || error.message.includes('translation')) {
                    errorType = 'ConversionError';
                } else if (error.message.includes('server') || error.message.includes('500')) {
                    errorType = 'ServerError';
                } else if (error.message.includes('encoding') || error.message.includes('unicode') || error.message.includes('charset')) {
                    errorType = 'EncodingError';
                }
            }

            // Get known error information
            if (this.knownErrors[errorType]) {
                errorObj = this.knownErrors[errorType];
            }

            // Set error title and message
            errorTitle.textContent = errorObj.title;
            errorMessage.textContent = error.message || 'An unexpected error occurred.';

            // Add suggested solutions
            const solutionsList = document.getElementById('error-modal-solutions-list');
            solutionsList.innerHTML = '';

            errorObj.solutions.forEach(solution => {
                const li = document.createElement('li');
                li.textContent = solution;
                solutionsList.appendChild(li);
            });

            // Show error details if available
            const errorDetails = document.getElementById('error-modal-details');
            const errorStack = document.getElementById('error-modal-stack');

            if (error.stack) {
                errorStack.textContent = error.stack;
                errorDetails.style.display = 'block';
            } else {
                errorDetails.style.display = 'none';
            }

            // Show error modal
            document.getElementById('error-modal').style.display = 'flex';

            // Set retry button behavior
            const retryButton = document.getElementById('error-modal-retry');

            if (operation) {
                retryButton.style.display = 'block';
                retryButton.onclick = () => {
                    this.hideError();
                    operation();
                };
            } else {
                retryButton.style.display = 'none';
            }

            // Log error
            console.error('Error:', error);
        },

        // Hide error modal
        hideError: function() {
            document.getElementById('error-modal').style.display = 'none';
        }
    };

    // Add event listeners for error modal buttons
    document.getElementById('close-error-modal').addEventListener('click', () => errorHandler.hideError());
    document.getElementById('error-modal-close').addEventListener('click', () => errorHandler.hideError());
    document.getElementById('error-modal-copy').addEventListener('click', function() {
        const errorMessage = document.getElementById('error-modal-message').textContent;
        const errorStack = document.getElementById('error-modal-stack').textContent;

        // Copy details to clipboard
        navigator.clipboard.writeText(`${errorMessage}\n\n${errorStack}`)
            .then(() => {
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = 'Copy Details';
                }, 2000);
            })
            .catch(err => {
                console.error('Failed to copy text:', err);
            });
    });

    // Replace default error handler
    window.addEventListener('error', function(event) {
        errorHandler.showError(event.error || new Error(event.message));
        return false; // Prevent automatic error reporting
    });

    // Modify text conversion function to use the new error handler
    window.originalConvertText = window.convertText;
    window.convertText = function() {
        try {
            // Check active editor
            const isAdvancedEditorActive = document.getElementById('advanced-editor-container').classList.contains('active');
            let latinText = '';

            if (isAdvancedEditorActive && quillEditor) {
                latinText = quillEditor.getText();
            } else {
                latinText = document.getElementById('latin-text').value;
            }

            if (!latinText) {
                document.getElementById('tifinagh-text').textContent = '';
                return;
            }

            // Show loading state
            document.getElementById('tifinagh-text').textContent = 'Converting...';

            // Call API
            const conversionOperation = () => {
                fetch('/api/convert', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: latinText,
                        hasTable: false
                    }),
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Response error: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // عرض النتيجة في منطقة الإخراج
                    document.getElementById('tifinagh-text').textContent = data.result;

                    // تحديث عداد الأحرف
                    updateCharCounter();
                })
                .catch(error => {
                    console.error('Error converting text:', error);
                    document.getElementById('tifinagh-text').textContent = '';
                    errorHandler.showError(error, conversionOperation);
                });
            };

            // بدء العملية
            conversionOperation();
        } catch (error) {
            console.error('Error in convertText:', error);
            document.getElementById('tifinagh-text').textContent = '';
            errorHandler.showError(error);
        }
    };
</script>

<style>
    /* نمط نافذة الخطأ */
    .error-modal-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .error-modal {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        width: 500px;
        max-width: 90%;
        max-height: 90%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .error-modal-header {
        background-color: #f44336;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .error-modal-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .close-error-modal {
        font-size: 24px;
        cursor: pointer;
        user-select: none;
    }

    .error-modal-body {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
    }

    #error-modal-message {
        font-size: 16px;
        margin-bottom: 15px;
    }

    #error-modal-details {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 15px;
    }

    #error-modal-stack {
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
        overflow-x: auto;
    }

    #error-modal-solution h4 {
        margin-bottom: 10px;
    }

    #error-modal-solutions-list {
        padding-left: 20px;
    }

    #error-modal-solutions-list li {
        margin-bottom: 5px;
    }

    .error-modal-footer {
        padding: 15px 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        border-top: 1px solid #e0e0e0;
    }
</style>

{% endblock %}

{% block extra_css %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/unified-tabs.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/text-formatting.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/website-converter.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/rich-editor.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/file-converter.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/toast-alerts.css') }}">
<!-- CKEditor 5 CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/ckeditor-custom.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/document-editor.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/quill-fonts.css') }}">
{% endblock %}

{% block extra_js %}
<!-- CKEditor 5 JavaScript -->
<script src="https://cdn.ckeditor.com/ckeditor5/36.0.1/decoupled-document/ckeditor.js"></script>
<script src="{{ url_for('static', filename='js/toast-alerts.js') }}"></script>
<!-- Use only unified-tabs.js for tab functionality -->
<script src="{{ url_for('static', filename='js/unified-tabs.js') }}"></script>
<script src="{{ url_for('static', filename='js/translator.js') }}"></script>
<script src="{{ url_for('static', filename='js/file-converter.js') }}"></script>
<script src="{{ url_for('static', filename='js/web-converter.js') }}"></script>
<script src="{{ url_for('static', filename='js/unified-formatting.js') }}"></script>
<script src="{{ url_for('static', filename='js/ckeditor-setup.js') }}"></script>
<script src="{{ url_for('static', filename='js/document-editor.js') }}"></script>
<script src="{{ url_for('static', filename='js/editor-height-control.js') }}"></script>

<!-- Initialize active tab from server-side parameter if available -->
{% if active_tab %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Force the tab system to use the active_tab parameter from the server
        console.log('Server provided active tab:', '{{ active_tab }}');

        // Wait a short time to ensure tab system is initialized
        setTimeout(function() {
            if (window.tabSystem && document.getElementById('{{ active_tab }}')) {
                window.tabSystem.switchTab('{{ active_tab }}');
                console.log('Tab switched to:', '{{ active_tab }}');
            }
        }, 100);
    });
</script>
{% endif %}
{% endblock %}
