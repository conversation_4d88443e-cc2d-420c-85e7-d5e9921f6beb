<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Loading Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .debug-box { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .console { background: #000; color: #0f0; padding: 10px; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🔧 JavaScript Loading Debug Tool</h1>
    <p>This tool will help diagnose why JavaScript objects are not being created.</p>

    <div class="debug-box">
        <h2>1. Script Loading Status</h2>
        <div id="script-status" class="result info">Checking script loading...</div>
        <button onclick="checkScriptLoading()">Check Script Loading</button>
    </div>

    <div class="debug-box">
        <h2>2. Global Objects Check</h2>
        <div id="global-objects" class="result info">Checking global objects...</div>
        <button onclick="checkGlobalObjects()">Check Global Objects</button>
    </div>

    <div class="debug-box">
        <h2>3. Console Errors Monitor</h2>
        <div id="console-errors" class="console"></div>
        <button onclick="clearErrors()">Clear Errors</button>
    </div>

    <div class="debug-box">
        <h2>4. Manual Script Loading Test</h2>
        <button onclick="loadI18nManually()">Load i18n.js Manually</button>
        <button onclick="loadLanguageSwitcherManually()">Load language-switcher.js Manually</button>
        <div id="manual-loading" class="result info">Click buttons to manually load scripts</div>
    </div>

    <div class="debug-box">
        <h2>5. Network Requests Check</h2>
        <button onclick="checkNetworkRequests()">Check Network Requests</button>
        <div id="network-check" class="result info">Click to check if scripts are being requested</div>
    </div>

    <script>
        let errorLog = [];
        let originalConsoleError = console.error;
        let originalConsoleLog = console.log;
        
        // Override console to capture errors
        console.error = function(...args) {
            const timestamp = new Date().toLocaleTimeString();
            const errorMsg = `[${timestamp}] ERROR: ${args.join(' ')}`;
            errorLog.push(errorMsg);
            updateErrorConsole();
            originalConsoleError.apply(console, args);
        };
        
        console.log = function(...args) {
            const timestamp = new Date().toLocaleTimeString();
            const logMsg = `[${timestamp}] LOG: ${args.join(' ')}`;
            errorLog.push(logMsg);
            updateErrorConsole();
            originalConsoleLog.apply(console, args);
        };
        
        function updateErrorConsole() {
            const console = document.getElementById('console-errors');
            console.innerHTML = errorLog.slice(-20).map(entry => `<div>${entry}</div>`).join('');
            console.scrollTop = console.scrollHeight;
        }
        
        function checkScriptLoading() {
            const result = document.getElementById('script-status');
            let messages = [];
            
            // Check if scripts are in DOM
            const scripts = document.querySelectorAll('script[src]');
            messages.push(`Total scripts found: ${scripts.length}`);
            
            const i18nScript = Array.from(scripts).find(s => s.src.includes('i18n.js'));
            const languageSwitcherScript = Array.from(scripts).find(s => s.src.includes('language-switcher.js'));
            
            if (i18nScript) {
                messages.push(`✅ i18n.js script tag found: ${i18nScript.src}`);
                messages.push(`   - Loaded: ${i18nScript.readyState || 'unknown'}`);
            } else {
                messages.push('❌ i18n.js script tag not found');
            }
            
            if (languageSwitcherScript) {
                messages.push(`✅ language-switcher.js script tag found: ${languageSwitcherScript.src}`);
                messages.push(`   - Loaded: ${languageSwitcherScript.readyState || 'unknown'}`);
            } else {
                messages.push('❌ language-switcher.js script tag not found');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (i18nScript && languageSwitcherScript ? 'success' : 'error');
        }
        
        function checkGlobalObjects() {
            const result = document.getElementById('global-objects');
            let messages = [];
            
            // Check window.i18n
            if (typeof window.i18n !== 'undefined') {
                messages.push('✅ window.i18n exists');
                messages.push(`   - Type: ${typeof window.i18n}`);
                messages.push(`   - Constructor: ${window.i18n.constructor.name}`);
                messages.push(`   - Is loaded: ${window.i18n.isLoaded}`);
                messages.push(`   - Current lang: ${window.i18n.currentLang}`);
            } else {
                messages.push('❌ window.i18n does not exist');
            }
            
            // Check window.languageSwitcher
            if (typeof window.languageSwitcher !== 'undefined') {
                messages.push('✅ window.languageSwitcher exists');
                messages.push(`   - Type: ${typeof window.languageSwitcher}`);
                messages.push(`   - Constructor: ${window.languageSwitcher.constructor.name}`);
                messages.push(`   - Is initialized: ${window.languageSwitcher.isInitialized}`);
            } else {
                messages.push('❌ window.languageSwitcher does not exist');
            }
            
            // Check global functions
            const globalFunctions = ['switchToEnglish', 'switchToAmazigh', 'getCurrentLang', '__'];
            globalFunctions.forEach(func => {
                if (typeof window[func] === 'function') {
                    messages.push(`✅ window.${func} function exists`);
                } else {
                    messages.push(`❌ window.${func} function missing`);
                }
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (typeof window.i18n !== 'undefined' && typeof window.languageSwitcher !== 'undefined' ? 'success' : 'error');
        }
        
        function loadI18nManually() {
            const result = document.getElementById('manual-loading');
            result.innerHTML = 'Loading i18n.js manually...';
            
            const script = document.createElement('script');
            script.src = '/static/js/i18n.js?v=3.0.0&manual=1';
            script.onload = function() {
                result.innerHTML += '<br>✅ i18n.js loaded successfully';
                setTimeout(checkGlobalObjects, 100);
            };
            script.onerror = function() {
                result.innerHTML += '<br>❌ Failed to load i18n.js';
            };
            document.head.appendChild(script);
        }
        
        function loadLanguageSwitcherManually() {
            const result = document.getElementById('manual-loading');
            result.innerHTML = 'Loading language-switcher.js manually...';
            
            const script = document.createElement('script');
            script.src = '/static/js/language-switcher.js?v=3.0.0&manual=1';
            script.onload = function() {
                result.innerHTML += '<br>✅ language-switcher.js loaded successfully';
                setTimeout(checkGlobalObjects, 100);
            };
            script.onerror = function() {
                result.innerHTML += '<br>❌ Failed to load language-switcher.js';
            };
            document.head.appendChild(script);
        }
        
        function checkNetworkRequests() {
            const result = document.getElementById('network-check');
            result.innerHTML = 'Checking network requests...';
            
            // Test fetch requests to scripts
            Promise.all([
                fetch('/static/js/i18n.js?v=3.0.0').then(r => ({file: 'i18n.js', status: r.status, ok: r.ok})),
                fetch('/static/js/language-switcher.js?v=3.0.0').then(r => ({file: 'language-switcher.js', status: r.status, ok: r.ok}))
            ]).then(results => {
                let messages = [];
                results.forEach(result => {
                    if (result.ok) {
                        messages.push(`✅ ${result.file}: HTTP ${result.status} (OK)`);
                    } else {
                        messages.push(`❌ ${result.file}: HTTP ${result.status} (Error)`);
                    }
                });
                document.getElementById('network-check').innerHTML = messages.join('<br>');
            }).catch(error => {
                result.innerHTML = `❌ Network check failed: ${error.message}`;
            });
        }
        
        function clearErrors() {
            errorLog = [];
            updateErrorConsole();
        }
        
        // Auto-run checks on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Debug tool loaded');
            setTimeout(() => {
                checkScriptLoading();
                checkGlobalObjects();
                checkNetworkRequests();
            }, 1000);
        });
        
        // Listen for script load events
        document.addEventListener('DOMContentLoaded', function() {
            const scripts = document.querySelectorAll('script[src]');
            scripts.forEach(script => {
                script.addEventListener('load', function() {
                    console.log(`Script loaded: ${this.src}`);
                });
                script.addEventListener('error', function() {
                    console.error(`Script failed to load: ${this.src}`);
                });
            });
        });
        
        console.log('JavaScript Debug Tool initialized');
    </script>
</body>
</html>
