import io
import logging

# محاولة استيراد مكتبات docx مع معالجة الاستثناءات
try:
    from docx import Document
    from docx.oxml import OxmlElement
    from docx.oxml.ns import qn
    DOCX_AVAILABLE = True
except ImportError:
    # استيراد البدائل من ملف docx_fallback
    from .docx_fallback import OxmlElement, qn
    DOCX_AVAILABLE = False
    logging.warning("مكتبة python-docx غير متوفرة. سيتم استخدام المحول البسيط لملفات Word.")

from .converter import latin_to_tifinagh

# إعداد التسجيل
logger = logging.getLogger(__name__)

def convert_docx_to_tifinagh(input_file, output_file):
    """
    تحويل ملف Word إلى تيفيناغ مع الحفاظ على التنسيق

    Args:
        input_file (str): مسار ملف Word المدخل
        output_file (str): مسار ملف Word المخرج

    Returns:
        str: مسار ملف Word المحول
    """
    # إذا لم تكن مكتبة python-docx متوفرة، استخدم المحول البسيط
    if not DOCX_AVAILABLE:
        logger.info("مكتبة python-docx غير متوفرة، استخدام المحول البسيط")
        return use_simple_converter(input_file, output_file)

    try:
        # فتح المستند الأصلي
        original_doc = Document(input_file)

        # إنشاء مستند جديد
        result_doc = Document()

        # نسخ خصائص المستند
        copy_document_properties(original_doc, result_doc)

        # معالجة الأقسام
        for section in original_doc.sections:
            # نسخ خصائص القسم
            result_section = result_doc.sections[-1]
            copy_section_properties(section, result_section)

        # معالجة الرؤوس والتذييلات
        process_headers_and_footers(original_doc, result_doc)

        # معالجة محتوى المستند
        process_document_content(original_doc, result_doc)

        # حفظ المستند الجديد
        result_doc.save(output_file)

        logger.info(f"تم تحويل المستند بنجاح: {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"خطأ في تحويل ملف Word: {str(e)}")
        # محاولة استخدام المحول البسيط في حالة فشل المحول الرئيسي
        logger.info("محاولة استخدام المحول البسيط")
        return use_simple_converter(input_file, output_file)

def use_simple_converter(input_file, output_file):
    """
    استخدام المحول البسيط لملفات Word

    Args:
        input_file (str): مسار ملف Word المدخل
        output_file (str): مسار ملف Word المخرج

    Returns:
        str: مسار ملف Word المحول
    """
    try:
        if not DOCX_AVAILABLE:
            from .docx_fallback import simple_docx_to_tifinagh

        # استخدام المحول البسيط
        output_path = simple_docx_to_tifinagh(input_file, output_file)
        logger.info(f"تم استخدام المحول البسيط. الملف المحول: {output_path}")

        return output_path
    except Exception as e:
        logger.error(f"فشل في تحويل ملف Word باستخدام المحول البسيط: {str(e)}")
        raise ValueError(f"فشل في تحويل المستند: {str(e)}")

def copy_document_properties(original_doc, result_doc):
    """
    نسخ خصائص المستند
    """
    # نسخ الأنماط
    for style in original_doc.styles:
        if style.name not in result_doc.styles:
            try:
                result_doc.styles.add_style(style.name, style.type)
            except Exception:
                pass

def copy_section_properties(original_section, result_section):
    """
    نسخ خصائص القسم
    """
    # نسخ أبعاد الصفحة
    result_section.page_width = original_section.page_width
    result_section.page_height = original_section.page_height

    # نسخ الهوامش
    result_section.left_margin = original_section.left_margin
    result_section.right_margin = original_section.right_margin
    result_section.top_margin = original_section.top_margin
    result_section.bottom_margin = original_section.bottom_margin

    # نسخ اتجاه الصفحة
    result_section.orientation = original_section.orientation

    # نسخ حجم الورق
    result_section.page_width = original_section.page_width
    result_section.page_height = original_section.page_height

def process_headers_and_footers(original_doc, result_doc):
    """
    معالجة الرؤوس والتذييلات
    """
    # معالجة الرؤوس
    for i, section in enumerate(original_doc.sections):
        result_section = result_doc.sections[i] if i < len(result_doc.sections) else result_doc.sections[-1]

        # معالجة الرأس الأول
        if section.header.is_linked_to_previous == False:
            header = result_section.header
            for paragraph in section.header.paragraphs:
                header_para = header.paragraphs[0] if header.paragraphs else header.add_paragraph()
                process_paragraph(paragraph, header_para)

        # معالجة التذييل الأول
        if section.footer.is_linked_to_previous == False:
            footer = result_section.footer
            for paragraph in section.footer.paragraphs:
                footer_para = footer.paragraphs[0] if footer.paragraphs else footer.add_paragraph()
                process_paragraph(paragraph, footer_para)

def process_document_content(original_doc, result_doc):
    """
    معالجة محتوى المستند
    """
    # معالجة الفقرات والجداول
    for element in original_doc.element.body:
        tag = element.tag.split('}')[-1]

        if tag == 'p':
            # معالجة الفقرة
            para_obj = original_doc.paragraphs[original_doc.element.body.index(element)]
            result_para = result_doc.add_paragraph()
            process_paragraph(para_obj, result_para)

        elif tag == 'tbl':
            try:
                # معالجة الجدول بطريقة أكثر أمانًا
                # استخدام طريقة بديلة للعثور على الجدول المناسب
                table_index = -1
                for i, tbl_elem in enumerate(original_doc.element.body.xpath('//w:tbl')):
                    if tbl_elem is element:
                        table_index = i
                        break

                if table_index >= 0 and table_index < len(original_doc.tables):
                    table_obj = original_doc.tables[table_index]
                    process_table(table_obj, result_doc)
                else:
                    # إذا لم نتمكن من العثور على الجدول، نقوم بإنشاء جدول فارغ
                    logger.warning(f"لم يتم العثور على الجدول المناسب في الفهرس {table_index}")
                    # إنشاء جدول فارغ بصف واحد وعمود واحد
                    result_doc.add_table(rows=1, cols=1)
            except Exception as e:
                logger.error(f"خطأ في معالجة الجدول: {str(e)}")
                # إنشاء جدول فارغ بصف واحد وعمود واحد
                result_doc.add_table(rows=1, cols=1)

    # معالجة الحواشي السفلية
    process_footnotes(original_doc, result_doc)

def process_paragraph(original_para, result_para):
    """
    معالجة الفقرة
    """
    # نسخ نمط الفقرة
    if original_para.style:
        result_para.style = original_para.style

    # نسخ محاذاة الفقرة
    result_para.alignment = original_para.alignment

    # نسخ تنسيق الفقرة
    if hasattr(original_para, 'paragraph_format') and original_para.paragraph_format:
        result_para.paragraph_format.line_spacing = original_para.paragraph_format.line_spacing
        result_para.paragraph_format.space_before = original_para.paragraph_format.space_before
        result_para.paragraph_format.space_after = original_para.paragraph_format.space_after
        result_para.paragraph_format.first_line_indent = original_para.paragraph_format.first_line_indent
        result_para.paragraph_format.left_indent = original_para.paragraph_format.left_indent
        result_para.paragraph_format.right_indent = original_para.paragraph_format.right_indent

    # معالجة القوائم النقطية والمرقمة
    process_list_formatting(original_para, result_para)

    # معالجة النص
    for run in original_para.runs:
        # تحويل النص إلى تيفيناغ
        converted_text = latin_to_tifinagh(run.text) if run.text else ""

        # إضافة النص المحول
        new_run = result_para.add_run(converted_text)

        # نسخ تنسيق النص
        copy_run_formatting(run, new_run)

        # معالجة الصور
        process_images_in_run(run, result_para)

def process_list_formatting(original_para, result_para):
    """
    معالجة تنسيق القوائم النقطية والمرقمة
    """
    # التحقق من وجود قوائم نقطية أو مرقمة
    if hasattr(original_para, '_p') and original_para._p is not None:
        try:
            # البحث عن علامات القوائم النقطية
            num_pr = original_para._p.xpath('./w:pPr/w:numPr')
            if num_pr:
                # الحصول على معرف القائمة ومستوى القائمة
                num_id = None
                ilvl = None

                for num_id_elem in num_pr[0].xpath('./w:numId'):
                    num_id = num_id_elem.get(qn('w:val'))

                for ilvl_elem in num_pr[0].xpath('./w:ilvl'):
                    ilvl = ilvl_elem.get(qn('w:val'))

                if num_id and ilvl:
                    # إضافة تنسيق القائمة إلى الفقرة الجديدة
                    add_list_formatting(result_para, num_id, ilvl)
        except Exception as e:
            logger.warning(f"خطأ في معالجة تنسيق القائمة: {str(e)}")

def add_list_formatting(paragraph, num_id, ilvl):
    """
    إضافة تنسيق القائمة إلى الفقرة
    """
    # إنشاء عنصر numPr
    num_pr = OxmlElement('w:numPr')

    # إضافة عنصر ilvl (مستوى القائمة)
    ilvl_elem = OxmlElement('w:ilvl')
    ilvl_elem.set(qn('w:val'), ilvl)
    num_pr.append(ilvl_elem)

    # إضافة عنصر numId (معرف القائمة)
    num_id_elem = OxmlElement('w:numId')
    num_id_elem.set(qn('w:val'), num_id)
    num_pr.append(num_id_elem)

    # إضافة numPr إلى pPr
    p_pr = paragraph._p.get_or_add_pPr()
    p_pr.append(num_pr)

def copy_run_formatting(original_run, new_run):
    """
    نسخ تنسيق النص
    """
    # نسخ تنسيق الخط
    new_run.bold = original_run.bold
    new_run.italic = original_run.italic
    new_run.underline = original_run.underline

    # نسخ خصائص الخط الإضافية
    if hasattr(original_run, 'font') and original_run.font:
        if hasattr(original_run.font, 'strike'):
            new_run.font.strike = original_run.font.strike
        if hasattr(original_run.font, 'subscript'):
            new_run.font.subscript = original_run.font.subscript
        if hasattr(original_run.font, 'superscript'):
            new_run.font.superscript = original_run.font.superscript
        if hasattr(original_run.font, 'highlight_color'):
            new_run.font.highlight_color = original_run.font.highlight_color
        if hasattr(original_run.font, 'color') and original_run.font.color and hasattr(original_run.font.color, 'rgb'):
            new_run.font.color.rgb = original_run.font.color.rgb
        if hasattr(original_run.font, 'size'):
            new_run.font.size = original_run.font.size
        if hasattr(original_run.font, 'name'):
            new_run.font.name = original_run.font.name

def process_images_in_run(original_run, result_para):
    """
    معالجة الصور في النص
    """
    if hasattr(original_run, '_r') and original_run._r is not None:
        try:
            # البحث عن الصور في العنصر
            for drawing in original_run._r.xpath('.//w:drawing'):
                # استخراج معلومات الصورة
                try:
                    # الحصول على معرف الصورة
                    blip = drawing.xpath('.//a:blip', namespaces={'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'})
                    if blip and hasattr(blip[0], 'get'):
                        embed_id = blip[0].get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                        if embed_id:
                            # الحصول على بيانات الصورة من المستند الأصلي
                            if hasattr(original_run, 'part') and original_run.part and hasattr(original_run.part, 'related_parts'):
                                image_part = original_run.part.related_parts.get(embed_id)
                                if image_part and hasattr(image_part, '_blob'):
                                    # استخراج بيانات الصورة
                                    image_blob = image_part._blob

                                    # إضافة الصورة إلى الفقرة الجديدة
                                    result_para.add_run().add_picture(io.BytesIO(image_blob))
                                    logger.info(f"تمت إضافة صورة إلى الفقرة")
                except Exception as e:
                    logger.warning(f"خطأ في معالجة الصورة: {str(e)}")
        except Exception as e:
            logger.warning(f"خطأ في البحث عن الصور: {str(e)}")

def process_table(original_table, result_doc):
    """
    معالجة الجدول
    """
    # إنشاء جدول جديد بنفس عدد الصفوف والأعمدة
    result_table = result_doc.add_table(rows=len(original_table.rows), cols=len(original_table.columns))

    # نسخ نمط الجدول
    if original_table.style:
        result_table.style = original_table.style

    # معالجة خلايا الجدول
    for i, row in enumerate(original_table.rows):
        for j, cell in enumerate(row.cells):
            # الحصول على الخلية المقابلة في الجدول الجديد
            result_cell = result_table.rows[i].cells[j]

            # معالجة محتوى الخلية
            process_cell(cell, result_cell)

            # نسخ تنسيق الخلية
            copy_cell_formatting(cell, result_cell)

def process_cell(original_cell, result_cell):
    """
    معالجة خلية الجدول
    """
    # حذف الفقرة الافتراضية في الخلية الجديدة
    if result_cell.paragraphs:
        p = result_cell.paragraphs[0]
        p._p.getparent().remove(p._p)
        result_cell._paragraphs = []

    # معالجة فقرات الخلية
    for paragraph in original_cell.paragraphs:
        # إضافة فقرة جديدة للخلية
        result_para = result_cell.add_paragraph()

        # معالجة الفقرة
        process_paragraph(paragraph, result_para)

def copy_cell_formatting(original_cell, result_cell):
    """
    نسخ تنسيق خلية الجدول
    """
    # نسخ محاذاة الخلية
    if hasattr(original_cell, 'vertical_alignment') and original_cell.vertical_alignment:
        result_cell.vertical_alignment = original_cell.vertical_alignment

    # نسخ عرض الخلية
    if hasattr(original_cell, 'width') and original_cell.width:
        result_cell.width = original_cell.width

    # نسخ خلفية الخلية
    set_cell_background(original_cell, result_cell)

    # نسخ حدود الخلية
    copy_cell_borders(original_cell, result_cell)

def set_cell_background(original_cell, result_cell):
    """
    تعيين خلفية الخلية
    """
    if hasattr(original_cell, '_tc') and original_cell._tc is not None:
        try:
            # البحث عن عنصر التظليل
            for shading in original_cell._tc.xpath('./w:tcPr/w:shd'):
                fill = shading.get(qn('w:fill'))
                color = shading.get(qn('w:color'))
                val = shading.get(qn('w:val'))

                if fill or color:
                    # إنشاء عنصر التظليل
                    shd_elem = OxmlElement('w:shd')

                    # تعيين خصائص التظليل
                    if fill:
                        shd_elem.set(qn('w:fill'), fill)
                    if color:
                        shd_elem.set(qn('w:color'), color)
                    shd_elem.set(qn('w:val'), val if val else 'clear')

                    # إضافة عنصر التظليل إلى خصائص الخلية
                    tc_pr = result_cell._tc.get_or_add_tcPr()
                    tc_pr.append(shd_elem)
        except Exception as e:
            logger.warning(f"خطأ في تعيين خلفية الخلية: {str(e)}")

def copy_cell_borders(original_cell, result_cell):
    """
    نسخ حدود الخلية
    """
    if hasattr(original_cell, '_tc') and original_cell._tc is not None:
        try:
            # البحث عن عنصر الحدود
            for borders in original_cell._tc.xpath('./w:tcPr/w:tcBorders'):
                # الحصول على خصائص الخلية
                tc_pr = result_cell._tc.get_or_add_tcPr()

                # إنشاء عنصر الحدود
                tc_borders = OxmlElement('w:tcBorders')

                # نسخ حدود الخلية
                for border in borders:
                    border_type = border.tag.split('}')[-1]
                    val = border.get(qn('w:val'))
                    color = border.get(qn('w:color'))
                    sz = border.get(qn('w:sz'))

                    # إنشاء عنصر الحد
                    border_elem = OxmlElement(f'w:{border_type}')

                    # تعيين خصائص الحد
                    if val:
                        border_elem.set(qn('w:val'), val)
                    if color:
                        border_elem.set(qn('w:color'), color)
                    if sz:
                        border_elem.set(qn('w:sz'), sz)

                    # إضافة عنصر الحد إلى عنصر الحدود
                    tc_borders.append(border_elem)

                # إضافة عنصر الحدود إلى خصائص الخلية
                tc_pr.append(tc_borders)
        except Exception as e:
            logger.warning(f"خطأ في نسخ حدود الخلية: {str(e)}")

def process_footnotes(original_doc, result_doc):
    """
    معالجة الحواشي السفلية
    """
    # التحقق من وجود حواشي سفلية
    if not hasattr(original_doc, 'footnotes') or not original_doc.footnotes:
        return

    try:
        # الحصول على عنصر الحواشي السفلية
        footnotes_part = original_doc.part.part_related_by(
            "http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes"
        )

        # الحصول على عنصر الحواشي السفلية في المستند الجديد
        result_footnotes_part = result_doc.part.part_related_by(
            "http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes"
        )

        # معالجة الحواشي السفلية
        for footnote in footnotes_part.footnote_list:
            # تجاهل الحواشي الخاصة (0 و 1)
            if footnote.id in (0, 1):
                continue

            # إنشاء حاشية سفلية جديدة
            new_footnote = result_footnotes_part.add_footnote()

            # معالجة فقرات الحاشية السفلية
            for paragraph in footnote.paragraphs:
                # إضافة فقرة جديدة للحاشية السفلية
                new_para = new_footnote.add_paragraph()

                # معالجة الفقرة
                process_paragraph(paragraph, new_para)

            # إضافة مرجع الحاشية السفلية إلى المستند
            add_footnote_reference(result_doc, footnote.id, new_footnote.id)
    except Exception as e:
        logger.warning(f"خطأ في معالجة الحواشي السفلية: {str(e)}")

def add_footnote_reference(doc, original_id, new_id):
    """
    إضافة مرجع الحاشية السفلية إلى المستند
    """
    # البحث عن مراجع الحواشي السفلية في المستند الأصلي
    for paragraph in doc.paragraphs:
        if hasattr(paragraph, '_p') and paragraph._p is not None:
            for footnote_ref in paragraph._p.xpath('.//w:footnoteReference'):
                ref_id = footnote_ref.get(qn('w:id'))
                if ref_id == str(original_id):
                    # إنشاء مرجع الحاشية السفلية
                    run = paragraph.add_run()
                    footnote_ref = OxmlElement('w:footnoteReference')
                    footnote_ref.set(qn('w:id'), str(new_id))
                    run._r.append(footnote_ref)
                    break
