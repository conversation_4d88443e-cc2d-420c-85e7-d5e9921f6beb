/**
 * إصلاح مشكلة HTML entities في النصوص المترجمة
 * HTML Entities Fix for Translation System
 */

(function() {
    'use strict';
    
    console.log('🔧 HTML Entities Fix v1.0.0 loaded');
    
    /**
     * فك تشفير HTML entities
     */
    function decodeHtmlEntities(text) {
        if (!text || typeof text !== 'string') return text;
        
        // قائمة بأشهر HTML entities
        const entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&apos;': "'",
            '&nbsp;': ' ',
            '&copy;': '©',
            '&reg;': '®',
            '&trade;': '™'
        };
        
        // استبدال HTML entities
        let decodedText = text;
        for (const [entity, replacement] of Object.entries(entities)) {
            decodedText = decodedText.replace(new RegExp(entity, 'g'), replacement);
        }
        
        // استخدام DOM لفك باقي الـ entities
        if (decodedText.includes('&') && decodedText.includes(';')) {
            try {
                const tempElement = document.createElement('div');
                tempElement.innerHTML = decodedText;
                decodedText = tempElement.textContent || tempElement.innerText || decodedText;
            } catch (error) {
                console.warn('Failed to decode HTML entities using DOM:', error);
            }
        }
        
        return decodedText;
    }
    
    /**
     * إصلاح النصوص الموجودة في الصفحة
     */
    function fixExistingTexts() {
        const elementsToFix = document.querySelectorAll('[data-i18n]');
        let fixedCount = 0;
        
        elementsToFix.forEach(element => {
            const currentText = element.textContent || element.innerText;
            
            if (currentText && currentText.includes('&')) {
                const fixedText = decodeHtmlEntities(currentText);
                
                if (fixedText !== currentText) {
                    // تحديث النص إذا كان مختلفاً
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        if (element.hasAttribute('placeholder')) {
                            element.placeholder = fixedText;
                        } else {
                            element.value = fixedText;
                        }
                    } else {
                        // التحقق من وجود HTML في النص الأصلي
                        const originalKey = element.getAttribute('data-i18n');
                        if (originalKey && originalKey.includes('copyright') && fixedText.includes('©')) {
                            // للنصوص التي تحتوي على HTML مثل حقوق النشر
                            element.innerHTML = fixedText;
                            
                            // ملء السنة إذا وجدت
                            const yearSpan = element.querySelector('#current-year');
                            if (yearSpan) {
                                yearSpan.textContent = new Date().getFullYear();
                            }
                        } else {
                            element.textContent = fixedText;
                        }
                    }
                    
                    fixedCount++;
                    console.log(`🔧 Fixed HTML entity in element [${originalKey}]: "${currentText}" → "${fixedText}"`);
                }
            }
        });
        
        if (fixedCount > 0) {
            console.log(`🔧 Fixed ${fixedCount} HTML entities in existing texts`);
        }
        
        return fixedCount;
    }
    
    /**
     * مراقب للتغييرات في DOM
     */
    function setupDOMObserver() {
        const observer = new MutationObserver(function(mutations) {
            let shouldFix = false;
            
            mutations.forEach(function(mutation) {
                // التحقق من إضافة عقد جديدة
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // التحقق من وجود عناصر data-i18n في العقد المضافة
                            if (node.hasAttribute && node.hasAttribute('data-i18n') || 
                                node.querySelector && node.querySelector('[data-i18n]')) {
                                shouldFix = true;
                            }
                        }
                    });
                }
                
                // التحقق من تغيير المحتوى
                if (mutation.type === 'characterData' && 
                    mutation.target.parentElement && 
                    mutation.target.parentElement.hasAttribute('data-i18n')) {
                    shouldFix = true;
                }
            });
            
            if (shouldFix) {
                setTimeout(fixExistingTexts, 10);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        console.log('🔧 DOM observer setup for HTML entities fix');
        return observer;
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // إصلاح عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DOMContentLoaded - fixing HTML entities');
            fixExistingTexts();
        });
        
        // إصلاح عند تحميل نظام الترجمة
        document.addEventListener('languageSystemLoaded', function() {
            console.log('🔧 Language system loaded - fixing HTML entities');
            setTimeout(fixExistingTexts, 100);
        });
        
        // إصلاح عند تغيير اللغة
        document.addEventListener('language:changed', function() {
            console.log('🔧 Language changed - fixing HTML entities');
            setTimeout(fixExistingTexts, 100);
        });
        
        // إصلاح عند تغيير اللغة (النظام القديم)
        document.addEventListener('languageChanged', function() {
            console.log('🔧 Language changed (legacy) - fixing HTML entities');
            setTimeout(fixExistingTexts, 100);
        });
        
        // إصلاح عند جاهزية النظام الجديد
        document.addEventListener('system:ready', function() {
            console.log('🔧 New system ready - fixing HTML entities');
            setTimeout(fixExistingTexts, 100);
        });
        
        console.log('🔧 Event listeners setup complete for HTML entities fix');
    }
    
    /**
     * تهيئة النظام
     */
    function init() {
        console.log('🔧 Initializing HTML entities fix...');
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        // إعداد مراقب DOM
        setupDOMObserver();
        
        // إصلاح فوري إذا كانت الصفحة محملة بالفعل
        if (document.readyState === 'loading') {
            console.log('🔧 Document still loading, waiting for DOMContentLoaded');
        } else {
            console.log('🔧 Document already loaded, fixing immediately');
            fixExistingTexts();
        }
        
        // إصلاح دوري كإجراء احتياطي
        setInterval(function() {
            const fixed = fixExistingTexts();
            if (fixed > 0) {
                console.log('🔧 Periodic check found and fixed HTML entities');
            }
        }, 10000); // كل 10 ثوان
        
        console.log('🔧 HTML entities fix initialized successfully');
    }
    
    // تصدير للاستخدام العام
    window.HtmlEntitiesFix = {
        decodeHtmlEntities,
        fixExistingTexts,
        init
    };
    
    // تهيئة تلقائية
    init();
    
})();
