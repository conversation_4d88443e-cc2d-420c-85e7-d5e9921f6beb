"""
ملف بديل لمعالجة ملفات Word عند عدم توفر مكتبة python-docx
"""
import os
import tempfile
import logging
from .converter import latin_to_tifinagh

logger = logging.getLogger(__name__)

class OxmlElement:
    """فئة بديلة لـ OxmlElement"""
    def __init__(self, tag):
        self.tag = tag
        self.attributes = {}
        self.children = []
    
    def set(self, key, value):
        self.attributes[key] = value
    
    def append(self, child):
        self.children.append(child)

def qn(name):
    """دالة بديلة لـ qn"""
    return name

def simple_docx_to_tifinagh(input_file, output_file):
    """
    محول بسيط لملفات Word إلى تيفيناغ
    """
    try:
        # محاولة استخدام docx2python إذا كانت متوفرة
        try:
            import docx2python
            doc = docx2python.docx2python(input_file)
            text_content = doc.text
        except ImportError:
            # إذا لم تكن متوفرة، قراءة الملف كنص عادي
            logger.warning("مكتبة docx2python غير متوفرة. سيتم محاولة قراءة الملف كنص عادي.")
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                text_content = f.read()
        
        # تحويل النص إلى تيفيناغ
        tifinagh_content = latin_to_tifinagh(text_content)
        
        # حفظ النتيجة
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(tifinagh_content)
        
        logger.info(f"تم تحويل الملف بنجاح: {output_file}")
        return output_file
        
    except Exception as e:
        logger.error(f"خطأ في تحويل ملف Word: {str(e)}")
        raise
