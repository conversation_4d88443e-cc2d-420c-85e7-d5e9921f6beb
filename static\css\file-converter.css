/**
 * File Converter Tab Styles
 */

/* File Upload Area */
.file-upload-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
}

.file-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    background-color: var(--bg-light);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.file-upload-area.highlight, .file-upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.05);
    transform: scale(1.01);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.file-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.file-icon {
    color: var(--primary-color);
    margin-bottom: 15px;
    opacity: 0.8;
    transition: transform 0.3s ease;
}

.file-upload-area:hover .file-icon {
    transform: translateY(-5px);
}

.file-upload-area h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.upload-divider {
    position: relative;
    font-size: 0.9rem;
    margin: 10px 0;
}

.select-file-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px 24px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.3);
}

.select-file-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.4);
}

.select-file-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(var(--primary-rgb), 0.3);
}

.file-format-info {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-top: 10px;
}

/* File Status */
.file-status {
    background-color: var(--bg-light);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.file-status-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.file-info-icon {
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.file-name-display {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-color);
}

.file-size-display {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.file-status-message {
    margin-top: 5px;
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.remove-file-btn {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-file-btn:hover {
    background-color: rgba(var(--danger-rgb), 0.1);
    transform: rotate(90deg);
}

/* File Preview Section */
.file-preview-container {
    background-color: var(--bg-light);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 300px;
}

.file-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--bg-medium);
    border-bottom: 1px solid var(--border-color);
}

.file-preview-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 500;
}

.preview-controls {
    display: flex;
    gap: 5px;
}

.preview-control-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.preview-control-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.file-preview-content {
    padding: 15px;
    overflow-y: auto;
    flex-grow: 1;
}

#file-preview-text {
    margin: 0;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
}

.file-preview-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--bg-medium);
    border-top: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-muted);
}

.preview-actions {
    display: flex;
    gap: 5px;
}

.preview-action-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.preview-action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Convert Button */
.convert-btn-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.file-convert-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-convert-btn:hover:not(:disabled) {
    background-color: var(--primary-dark);
}

.file-convert-btn:disabled {
    background-color: var(--disabled-color);
    cursor: not-allowed;
}

.file-convert-btn.success {
    background-color: var(--success-color);
}

/* Loading Animation */
.spin {
    animation: spin 1.5s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search Box */
.preview-search-container {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.preview-search-input {
    border: none;
    outline: none;
    padding: 5px;
    font-size: 0.9rem;
}

.preview-search-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
}

.preview-search-close {
    background: none;
    border: none;
    color: var(--danger-color);
    cursor: pointer;
    padding: 5px;
}

/* Highlight search results */
.search-highlight {
    background-color: rgba(var(--warning-rgb), 0.3);
    border-radius: 2px;
}

/* Progress Bar */
.progress-container {
    width: 100%;
    background-color: rgba(var(--primary-rgb), 0.1);
    border-radius: 20px;
    margin-top: 15px;
    overflow: hidden;
    height: 12px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.progress-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
    z-index: 1;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg,
                var(--primary-color) 0%,
                var(--primary-dark) 50%,
                var(--primary-color) 100%);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 20px;
    background-size: 30px 30px;
    background-image: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.25) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.25) 50%,
        rgba(255, 255, 255, 0.25) 75%,
        transparent 75%,
        transparent
    );
    animation: progress-animation 1s linear infinite, pulse-animation 2s infinite;
    box-shadow: 0 0 10px rgba(var(--primary-rgb), 0.5);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%);
    animation: shine-animation 2s infinite;
}

@keyframes progress-animation {
    0% { background-position: 0 0; }
    100% { background-position: 30px 0; }
}

@keyframes pulse-animation {
    0% { box-shadow: 0 0 5px rgba(var(--primary-rgb), 0.5); }
    50% { box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.7); }
    100% { box-shadow: 0 0 5px rgba(var(--primary-rgb), 0.5); }
}

@keyframes shine-animation {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Info link style */
.info-link {
    display: inline-block;
    margin-top: 8px;
    color: var(--primary-color);
    text-decoration: underline;
    font-size: 0.9rem;
}

.info-link:hover {
    color: var(--primary-dark);
}

/* Word Tips Container */
.word-tips-container {
    background-color: rgba(var(--info-rgb), 0.1);
    border-left: 4px solid var(--info-color);
    margin-top: 15px;
}

.word-tips-list {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.word-tips-list li {
    margin-bottom: 8px;
    line-height: 1.4;
}



/* Responsive adjustments */
@media (max-width: 768px) {
    .file-upload-area {
        padding: 20px;
    }

    .file-preview-container {
        max-height: 200px;
    }
}
