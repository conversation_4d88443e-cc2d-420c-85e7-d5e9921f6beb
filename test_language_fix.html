<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Fix Test - Tifinagh Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .clear-btn { background: #dc3545; }
        .clear-btn:hover { background: #c82333; }
        #console {
            background: #1e1e1e;
            color: #ffffff;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Language Auto-Switch Fix Test</h1>
    
    <div class="test-container">
        <h2>📋 Test Status</h2>
        <div id="testStatus">
            <div class="info">Initializing tests...</div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🎮 Manual Tests</h2>
        <button onclick="testCurrentLanguage()">Check Current Language</button>
        <button onclick="testLocalStorage()">Check localStorage</button>
        <button onclick="testLanguageSwitch('en')">Switch to English</button>
        <button onclick="testLanguageSwitch('am')">Switch to Amazigh</button>
        <button onclick="clearLocalStorage()" class="clear-btn">Clear localStorage</button>
        <button onclick="reloadPage()">Reload Page</button>
    </div>
    
    <div class="test-container">
        <h2>📊 Test Results</h2>
        <div id="testResults"></div>
    </div>
    
    <div class="test-container">
        <h2>🖥️ Console Output</h2>
        <div id="console"></div>
        <button onclick="clearConsole()" class="clear-btn">Clear Console</button>
    </div>

    <!-- Load the fixed i18n system -->
    <script src="static/js/i18n.js"></script>
    
    <script>
        // Test utilities
        let testResults = [];
        let consoleOutput = [];
        
        // Override console methods to capture output
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        function logToConsole(type, message, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const fullMessage = `[${timestamp}] ${type.toUpperCase()}: ${message} ${args.join(' ')}`;
            consoleOutput.push(fullMessage);
            updateConsoleDisplay();
            originalConsole[type](message, ...args);
        }
        
        console.log = (...args) => logToConsole('log', ...args);
        console.warn = (...args) => logToConsole('warn', ...args);
        console.error = (...args) => logToConsole('error', ...args);
        
        function updateConsoleDisplay() {
            document.getElementById('console').textContent = consoleOutput.join('\n');
            document.getElementById('console').scrollTop = document.getElementById('console').scrollHeight;
        }
        
        function addTestResult(test, result, details = '') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${result ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>${result ? '✅' : '❌'} ${test}</strong>
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            document.getElementById('testResults').appendChild(resultDiv);
            
            testResults.push({ test, result, details, timestamp: new Date() });
        }
        
        function updateTestStatus(message, type = 'info') {
            document.getElementById('testStatus').innerHTML = `
                <div class="${type}">${message}</div>
            `;
        }
        
        // Test functions
        function testCurrentLanguage() {
            console.log('🧪 Testing current language...');
            
            if (window.i18n) {
                const currentLang = window.i18n.currentLang;
                console.log(`Current language: ${currentLang}`);
                
                if (currentLang === 'en') {
                    addTestResult('Default Language Check', true, 'Language is correctly set to English');
                } else {
                    addTestResult('Default Language Check', false, `Language is ${currentLang}, expected 'en'`);
                }
            } else {
                addTestResult('i18n System Check', false, 'i18n system not found');
            }
        }
        
        function testLocalStorage() {
            console.log('🧪 Testing localStorage...');
            
            const preferredLang = localStorage.getItem('preferred_language');
            const selectedLang = localStorage.getItem('selectedLanguage');
            
            console.log(`localStorage preferred_language: ${preferredLang}`);
            console.log(`localStorage selectedLanguage: ${selectedLang}`);
            
            const details = `preferred: ${preferredLang || 'null'}, selected: ${selectedLang || 'null'}`;
            
            if (!preferredLang && !selectedLang) {
                addTestResult('localStorage Clean', true, 'No language preferences stored');
            } else if ((preferredLang && ['en', 'am'].includes(preferredLang)) || 
                      (selectedLang && ['en', 'am'].includes(selectedLang))) {
                addTestResult('localStorage Valid', true, details);
            } else {
                addTestResult('localStorage Invalid', false, details);
            }
        }
        
        function testLanguageSwitch(targetLang) {
            console.log(`🧪 Testing language switch to: ${targetLang}`);
            
            if (window.i18n && typeof window.i18n.setLanguage === 'function') {
                const oldLang = window.i18n.currentLang;
                window.i18n.setLanguage(targetLang);
                const newLang = window.i18n.currentLang;
                
                if (newLang === targetLang) {
                    addTestResult(`Language Switch to ${targetLang}`, true, `Changed from ${oldLang} to ${newLang}`);
                } else {
                    addTestResult(`Language Switch to ${targetLang}`, false, `Failed to change from ${oldLang}, still ${newLang}`);
                }
            } else {
                addTestResult('Language Switch Function', false, 'setLanguage function not available');
            }
        }
        
        function clearLocalStorage() {
            console.log('🧪 Clearing localStorage...');
            
            localStorage.removeItem('preferred_language');
            localStorage.removeItem('selectedLanguage');
            
            addTestResult('localStorage Clear', true, 'Language preferences cleared');
            
            // Test if language resets to default
            setTimeout(() => {
                if (window.i18n) {
                    const currentLang = window.i18n.currentLang;
                    if (currentLang === 'en') {
                        addTestResult('Default Language After Clear', true, 'Language correctly reset to English');
                    } else {
                        addTestResult('Default Language After Clear', false, `Language is ${currentLang}, expected 'en'`);
                    }
                }
            }, 100);
        }
        
        function reloadPage() {
            console.log('🔄 Reloading page...');
            location.reload();
        }
        
        function clearConsole() {
            consoleOutput = [];
            updateConsoleDisplay();
        }
        
        // Automated tests
        function runAutomatedTests() {
            console.log('🚀 Running automated tests...');
            
            // Test 1: Check if i18n system exists
            setTimeout(() => {
                if (window.i18n) {
                    addTestResult('i18n System Loaded', true, 'window.i18n is available');
                } else {
                    addTestResult('i18n System Loaded', false, 'window.i18n is not available');
                }
            }, 100);
            
            // Test 2: Check if only one instance was created
            setTimeout(() => {
                const creationLogs = consoleOutput.filter(log => 
                    log.includes('i18n system created successfully')
                );
                
                if (creationLogs.length === 1) {
                    addTestResult('Single Instance Creation', true, 'Only one i18n instance created');
                } else {
                    addTestResult('Single Instance Creation', false, `${creationLogs.length} instances created`);
                }
            }, 200);
            
            // Test 3: Check default language
            setTimeout(() => {
                testCurrentLanguage();
            }, 300);
            
            // Test 4: Check localStorage
            setTimeout(() => {
                testLocalStorage();
            }, 400);
            
            updateTestStatus('Automated tests completed', 'success');
        }
        
        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            updateTestStatus('Page loaded, waiting for i18n system...', 'info');
            
            // Wait for i18n system to initialize
            setTimeout(() => {
                runAutomatedTests();
            }, 1000);
        });
        
        // Listen for language system events
        document.addEventListener('languageSystemLoaded', function(e) {
            console.log('🎉 Language system loaded event received');
            updateTestStatus('Language system loaded successfully', 'success');
        });
        
        document.addEventListener('languageChanged', function(e) {
            console.log(`🔄 Language changed to: ${e.detail.language}`);
        });
        
        console.log('🧪 Language Fix Test Page Loaded');
    </script>
</body>
</html>
