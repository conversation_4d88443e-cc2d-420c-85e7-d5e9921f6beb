{% extends 'admin/base.html' %}

{% block title %}User Management - Admin Panel{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">User Management</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12 d-flex justify-content-between align-items-center">
        <h1 class="h3">User Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="bi bi-person-plus me-1"></i> Add User
        </button>
    </div>
</div>

{% if action == 'change_password' %}
<!-- Change Password Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Change Password</h5>
    </div>
    <div class="card-body">
        <form id="changePasswordForm" action="{{ url_for('admin.admin_change_password') }}" method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password" required>
                        <div class="form-text">Password must be at least 8 characters long and include letters and numbers.</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-1"></i> Change Password
                        </button>
                        <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">
                            <i class="bi bi-x-lg me-1"></i> Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% else %}
<!-- Users Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Users</h5>
        <div class="input-group" style="max-width: 300px;">
            <input type="text" class="form-control" placeholder="Search users..." id="searchUsers">
            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                <i class="bi bi-x"></i>
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="usersTable">
                <thead class="table-light">
                    <tr>
                        <th>Username</th>
                        <th>Role</th>
                        <th>Last Login</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for username, user_data in users.items() %}
                    <tr>
                        <td>{{ username }}</td>
                        <td>
                            <span class="badge {% if user_data.role == 'admin' %}bg-danger{% else %}bg-primary{% endif %}">
                                {{ user_data.role|capitalize }}
                            </span>
                        </td>
                        <td>{{ user_data.last_login }}</td>
                        <td>
                            <span class="badge bg-success">Active</span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary edit-user" data-username="{{ username }}" data-role="{{ user_data.role }}">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                {% if username != 'admin' %}
                                <button type="button" class="btn btn-outline-danger delete-user" data-username="{{ username }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                                <button type="button" class="btn btn-outline-secondary reset-password" data-username="{{ username }}">
                                    <i class="bi bi-key"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Add/Edit User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Add User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="editMode" value="add">
                    <input type="hidden" id="originalUsername" value="">

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" required>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password">
                        <small class="form-text text-muted" id="passwordHelpText">Password must be at least 8 characters long.</small>
                    </div>

                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role">
                            <option value="editor">Editor</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUser">Save User</button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>You are about to reset the password for user: <strong id="resetUsername"></strong></p>
                <form id="resetPasswordForm">
                    <div class="mb-3">
                        <label for="newResetPassword" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newResetPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmResetPassword" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirmResetPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmReset">Reset Password</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal for Delete -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Warning!</strong> This action cannot be undone.
                </div>
                <p>Are you sure you want to delete this user?</p>
                <p><strong>Username:</strong> <span id="deleteUsername"></span></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const userModal = new bootstrap.Modal(document.getElementById('addUserModal'));
        const resetModal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        let currentUsername = '';

        // Edit user
        document.querySelectorAll('.edit-user').forEach(btn => {
            btn.addEventListener('click', function() {
                const username = this.dataset.username;
                const role = this.dataset.role;

                document.getElementById('editMode').value = 'edit';
                document.getElementById('originalUsername').value = username;
                document.getElementById('username').value = username;
                document.getElementById('role').value = role;
                document.getElementById('password').value = '';
                document.getElementById('password').required = false;
                document.getElementById('passwordHelpText').textContent = 'Leave blank to keep current password.';

                document.getElementById('username').readOnly = true;
                document.getElementById('addUserModalLabel').textContent = 'Edit User';
                userModal.show();
            });
        });

        // Add new user
        document.querySelector('[data-bs-target="#addUserModal"]').addEventListener('click', function() {
            document.getElementById('editMode').value = 'add';
            document.getElementById('originalUsername').value = '';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('role').value = 'editor';

            document.getElementById('username').readOnly = false;
            document.getElementById('password').required = true;
            document.getElementById('passwordHelpText').textContent = 'Password must be at least 8 characters long.';
            document.getElementById('addUserModalLabel').textContent = 'Add User';
        });

        // Save user
        document.getElementById('saveUser').addEventListener('click', function() {
            const editMode = document.getElementById('editMode').value;
            const originalUsername = document.getElementById('originalUsername').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const role = document.getElementById('role').value;

            if (!username) {
                alert('Username is required');
                return;
            }

            if (editMode === 'add' && !password) {
                alert('Password is required for new users');
                return;
            }

            const data = {
                mode: editMode,
                original_username: originalUsername,
                username: username,
                password: password,
                role: role
            };

            // Send to server
            fetch('{{ url_for("admin.admin_save_user") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User saved successfully');
                    location.reload(); // Reload to see changes
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });

            userModal.hide();
        });

        // Reset password
        document.querySelectorAll('.reset-password').forEach(btn => {
            btn.addEventListener('click', function() {
                currentUsername = this.dataset.username;
                document.getElementById('resetUsername').textContent = currentUsername;
                resetModal.show();
            });
        });

        // Confirm reset password
        document.getElementById('confirmReset').addEventListener('click', function() {
            const newPassword = document.getElementById('newResetPassword').value;
            const confirmPassword = document.getElementById('confirmResetPassword').value;

            if (!newPassword) {
                alert('New password is required');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('Passwords do not match');
                return;
            }

            fetch('{{ url_for("admin.admin_reset_password") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: currentUsername,
                    password: newPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Password reset successfully');
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });

            resetModal.hide();
        });

        // Delete user
        document.querySelectorAll('.delete-user').forEach(btn => {
            btn.addEventListener('click', function() {
                currentUsername = this.dataset.username;
                document.getElementById('deleteUsername').textContent = currentUsername;
                deleteModal.show();
            });
        });

        // Confirm delete
        document.getElementById('confirmDelete').addEventListener('click', function() {
            fetch('{{ url_for("admin.admin_delete_user") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username: currentUsername })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('User deleted successfully');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });

            deleteModal.hide();
        });

        // Search users
        document.getElementById('searchUsers').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#usersTable tbody tr');

            rows.forEach(row => {
                const username = row.querySelector('td:first-child').textContent.toLowerCase();
                const role = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

                if (username.includes(searchTerm) || role.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Clear search
        document.getElementById('clearSearch').addEventListener('click', function() {
            document.getElementById('searchUsers').value = '';
            document.querySelectorAll('#usersTable tbody tr').forEach(row => {
                row.style.display = '';
            });
        });

        // Change password form validation
        const changePasswordForm = document.getElementById('changePasswordForm');
        if (changePasswordForm) {
            changePasswordForm.addEventListener('submit', function(event) {
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (newPassword !== confirmPassword) {
                    event.preventDefault();
                    alert('New passwords do not match');
                }

                if (newPassword.length < 8) {
                    event.preventDefault();
                    alert('Password must be at least 8 characters long');
                }
            });
        }
    });
</script>
{% endblock %}