"""
محول ملفات Word متكامل يحافظ على جميع التنسيقات
"""
import os
import logging
import tempfile
import shutil
import zipfile
from lxml import etree
from .converter import latin_to_tifinagh

# إعداد التسجيل
logger = logging.getLogger(__name__)

# تعريف XML namespaces المستخدمة في ملفات DOCX
NAMESPACES = {
    'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
    'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
    'p': 'http://schemas.openxmlformats.org/presentationml/2006/main',
    'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
    'pic': 'http://schemas.openxmlformats.org/drawingml/2006/picture',
    'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
    'mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',
    'wps': 'http://schemas.microsoft.com/office/word/2010/wordprocessingShape',
    'wpg': 'http://schemas.microsoft.com/office/word/2010/wordprocessingGroup',
    'w14': 'http://schemas.microsoft.com/office/word/2010/wordml',
    'w15': 'http://schemas.microsoft.com/office/word/2012/wordml',
    'cp': 'http://schemas.openxmlformats.org/package/2006/metadata/core-properties',
    'dc': 'http://purl.org/dc/elements/1.1/',
    'dcterms': 'http://purl.org/dc/terms/',
    'dcmitype': 'http://purl.org/dc/dcmitype/',
    'xsi': 'http://www.w3.org/2001/XMLSchema-instance',
    'c': 'http://schemas.openxmlformats.org/drawingml/2006/chart',
    'm': 'http://schemas.openxmlformats.org/officeDocument/2006/math',
    'wne': 'http://schemas.microsoft.com/office/word/2006/wordml',
    'xml': 'http://www.w3.org/XML/1998/namespace',
}

def convert_docx_complete(input_path, output_path=None):
    """
    تحويل ملف Word إلى تيفيناغ مع الحفاظ على جميع التنسيقات

    Args:
        input_path (str): مسار ملف Word المدخل
        output_path (str): مسار ملف Word المخرج (اختياري)

    Returns:
        str: مسار الملف المحول
    """
    try:
        # التحقق من وجود الملف المدخل
        if not os.path.exists(input_path):
            logger.error(f"الملف المدخل غير موجود: {input_path}")
            raise FileNotFoundError(f"الملف المدخل غير موجود: {input_path}")

        # تحديد مسار الملف المخرج إذا لم يتم تحديده
        if output_path is None:
            file_name, file_ext = os.path.splitext(os.path.basename(input_path))
            output_path = os.path.join('uploads', f"{file_name}_tifinagh{file_ext}")

        # التأكد من وجود مجلد الحفظ
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"تم إنشاء مجلد الحفظ: {output_dir}")

        # إنشاء مجلد مؤقت للعمل
        temp_dir = tempfile.mkdtemp()
        try:
            # نسخ الملف الأصلي إلى المجلد المؤقت
            temp_input = os.path.join(temp_dir, "original.docx")
            shutil.copy2(input_path, temp_input)

            # استخراج محتويات الملف
            extract_dir = os.path.join(temp_dir, "extracted")
            os.makedirs(extract_dir, exist_ok=True)

            with zipfile.ZipFile(temp_input, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)

            # تحويل النصوص في ملف document.xml
            document_path = os.path.join(extract_dir, "word", "document.xml")
            if os.path.exists(document_path):
                convert_document_xml(document_path)

            # تحويل النصوص في ملفات أخرى
            # الحواشي السفلية
            footnotes_path = os.path.join(extract_dir, "word", "footnotes.xml")
            if os.path.exists(footnotes_path):
                convert_document_xml(footnotes_path)

            # الحواشي النهائية
            endnotes_path = os.path.join(extract_dir, "word", "endnotes.xml")
            if os.path.exists(endnotes_path):
                convert_document_xml(endnotes_path)

            # الرؤوس والتذييلات
            header_files = [f for f in os.listdir(os.path.join(extract_dir, "word")) if f.startswith("header") and f.endswith(".xml")]
            for header_file in header_files:
                header_path = os.path.join(extract_dir, "word", header_file)
                convert_document_xml(header_path)

            footer_files = [f for f in os.listdir(os.path.join(extract_dir, "word")) if f.startswith("footer") and f.endswith(".xml")]
            for footer_file in footer_files:
                footer_path = os.path.join(extract_dir, "word", footer_file)
                convert_document_xml(footer_path)

            # إعادة إنشاء ملف DOCX
            temp_output = os.path.join(temp_dir, "converted.docx")
            create_docx_from_directory(extract_dir, temp_output)

            # نسخ الملف المحول إلى المسار المطلوب
            shutil.copy2(temp_output, output_path)
            logger.info(f"تم تحويل ملف Word بنجاح: {output_path}")

            return output_path

        finally:
            # تنظيف المجلد المؤقت
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"لم يتم حذف المجلد المؤقت: {str(e)}")

    except Exception as e:
        logger.error(f"خطأ في تحويل ملف Word: {str(e)}")
        raise

def convert_document_xml(xml_path):
    """
    تحويل النصوص في ملف XML

    Args:
        xml_path (str): مسار ملف XML
    """
    try:
        # قراءة ملف XML
        tree = etree.parse(xml_path)
        root = tree.getroot()

        # تجميع النصوص المتصلة معًا قبل التحويل
        paragraphs = root.xpath("//w:p", namespaces=NAMESPACES)

        for paragraph in paragraphs:
            # تجميع النصوص في الفقرة
            runs = paragraph.xpath(".//w:r", namespaces=NAMESPACES)

            # تجميع النص الكامل للفقرة أولاً
            full_paragraph_text = ""
            text_elements_map = []  # لتخزين عناصر النص وأطوالها

            for run in runs:
                text_elements = run.xpath(".//w:t", namespaces=NAMESPACES)
                for text_element in text_elements:
                    text = text_element.text or ""
                    full_paragraph_text += text
                    text_elements_map.append((text_element, len(text)))

            # إذا كانت الفقرة فارغة، تخطيها
            if not full_paragraph_text.strip():
                continue

            # تحويل النص الكامل للفقرة
            tifinagh_paragraph = latin_to_tifinagh(full_paragraph_text)

            # إعادة توزيع النص المحول على العناصر الأصلية
            current_pos = 0
            for text_element, length in text_elements_map:
                if length > 0:
                    # حساب النسبة المئوية للنص الأصلي
                    ratio = length / len(full_paragraph_text)
                    # حساب طول النص المحول المقابل
                    tifinagh_length = max(1, int(len(tifinagh_paragraph) * ratio))
                    # الحصول على جزء النص المحول
                    if current_pos < len(tifinagh_paragraph):
                        end_pos = min(current_pos + tifinagh_length, len(tifinagh_paragraph))
                        text_element.text = tifinagh_paragraph[current_pos:end_pos]
                        current_pos = end_pos
                    else:
                        # إذا تجاوزنا طول النص المحول، نضع نصًا فارغًا
                        text_element.text = ""

        # معالجة العناصر النصية خارج الفقرات (مثل العناوين والتذييلات)
        # نستخدم نهجًا مختلفًا للعناصر خارج الفقرات
        standalone_text_elements = root.xpath("//w:t[not(ancestor::w:p)]", namespaces=NAMESPACES)

        for text_element in standalone_text_elements:
            original_text = text_element.text or ""
            if original_text.strip():
                # تحويل النص إلى تيفيناغ
                tifinagh_text = latin_to_tifinagh(original_text)
                text_element.text = tifinagh_text

        # معالجة خاصة للجداول
        tables = root.xpath("//w:tbl", namespaces=NAMESPACES)
        for table in tables:
            # معالجة كل خلية في الجدول
            cells = table.xpath(".//w:tc", namespaces=NAMESPACES)
            for cell in cells:
                # تجميع النص الكامل للخلية
                cell_text = ""
                cell_text_elements = []

                # جمع جميع عناصر النص في الخلية
                text_elements = cell.xpath(".//w:t", namespaces=NAMESPACES)
                for text_element in text_elements:
                    text = text_element.text or ""
                    cell_text += text
                    cell_text_elements.append((text_element, text))

                # إذا كانت الخلية فارغة، تخطيها
                if not cell_text.strip():
                    continue

                # تحويل النص الكامل للخلية
                tifinagh_cell_text = latin_to_tifinagh(cell_text)

                # إذا كان هناك عنصر نصي واحد فقط، نضع النص المحول كاملاً فيه
                if len(cell_text_elements) == 1:
                    cell_text_elements[0][0].text = tifinagh_cell_text
                else:
                    # توزيع النص المحول على العناصر النصية بنسب متساوية
                    total_original_length = len(cell_text)
                    current_pos = 0

                    for text_element, original_text in cell_text_elements:
                        if not original_text:
                            continue

                        # حساب النسبة المئوية للنص الأصلي
                        ratio = len(original_text) / total_original_length
                        # حساب طول النص المحول المقابل
                        tifinagh_length = max(1, int(len(tifinagh_cell_text) * ratio))
                        # الحصول على جزء النص المحول
                        if current_pos < len(tifinagh_cell_text):
                            end_pos = min(current_pos + tifinagh_length, len(tifinagh_cell_text))
                            text_element.text = tifinagh_cell_text[current_pos:end_pos]
                            current_pos = end_pos
                        else:
                            # إذا تجاوزنا طول النص المحول، نضع نصًا فارغًا
                            text_element.text = ""

        # حفظ التغييرات
        tree.write(xml_path, encoding="UTF-8", xml_declaration=True)
        logger.info(f"تم تحويل النصوص في ملف: {xml_path}")

    except Exception as e:
        logger.error(f"خطأ في تحويل ملف XML: {str(e)}")
        raise

def create_docx_from_directory(directory, output_path):
    """
    إنشاء ملف DOCX من مجلد يحتوي على محتويات مستخرجة

    Args:
        directory (str): مسار المجلد الذي يحتوي على محتويات DOCX
        output_path (str): مسار ملف DOCX المخرج
    """
    try:
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for root, _, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, directory)
                    zip_file.write(file_path, arcname)

        logger.info(f"تم إنشاء ملف DOCX: {output_path}")

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف DOCX: {str(e)}")
        raise
