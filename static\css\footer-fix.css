/**
 * Footer CSS
 * يحل مشكلة عدم ظهور الفوتر بشكل كامل ويضيف تحسينات مظهرية
 * (تم دمج footer-enhancements.css مع footer-fix.css)
 */

/* إعادة تعيين أساسية للصفحة */
html {
    height: 100% !important;
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow-y: scroll !important; /* إظهار شريط التمرير دائماً لمنع انحياز العناصر */
}

body {
    height: 100% !important;
    min-height: 100vh !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* تأكيد أن الجسم يستخدم flexbox بشكل صحيح */
body {
    display: flex !important;
    flex-direction: column !important;
    min-height: 100vh !important;
}

/* تأكيد أن المحتوى الرئيسي يتمدد بشكل صحيح */
main.main-content {
    flex: 1 0 auto !important; /* لا ينكمش، ولكن يتمدد */
    display: flex !important;
    flex-direction: column !important;
    overflow-x: hidden !important;
    position: relative !important;
    width: 100% !important;
}

/* تأكيد أن الفوتر يظهر في الأسفل ولا يختفي */
footer {
    flex-shrink: 0 !important; /* لا ينكمش */
    width: 100% !important;
    position: relative !important; /* ليس ثابتًا أو مطلقًا */
    z-index: 10 !important; /* للتأكد من أنه فوق المحتوى */
    margin-top: 0 !important; /* إزالة الهامش العلوي للفوتر */
}

/* === التحسينات المظهرية للفوتر (من ملف footer-enhancements.css) === */

/* تحسين مظهر الفوتر بشكل عام */
footer.bg-dark {
    background: linear-gradient(135deg, #1e293b, #0f172a) !important;
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    padding: 1rem 0 0.5rem !important; /* تقليل المساحة العمودية بشكل كبير */
    margin-top: 0 !important; /* إزالة الهامش العلوي */
}

/* إضافة تأثير خط متدرج في أعلى الفوتر */
footer.bg-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #3b82f6, #8b5cf6, var(--primary-color));
    background-size: 300% 100%;
    animation: gradientAnimation 6s ease infinite;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تحسين مظهر العناوين في الفوتر */
footer .fw-bold {
    position: relative;
    display: inline-block;
}

/* تحسين مظهر الخط الفاصل */
footer hr {
    height: 2px;
    background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.3), rgba(255,255,255,0.1));
    border: none;
}

/* تحسين مظهر النصوص في الفوتر */
footer div[style*="font-size"] {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* إضافة تأثير توهج للعنوان الرئيسي */
footer div[style*="font-size:1.6rem"] {
    text-shadow: 0 0 10px rgba(79, 70, 229, 0.3);
}

/* تحسين مظهر أيقونات التواصل الاجتماعي */
.social-icons {
    display: flex;
    justify-content: center; /* تغيير من flex-end إلى center لجعل الأيقونات في المنتصف */
    gap: 15px;
    flex-wrap: wrap; /* السماح بالتفاف الأيقونات عند وجود عدد كبير منها */
    margin-bottom: 10px; /* إضافة هامش سفلي للتأكد من عدم التداخل مع العناصر الأخرى */
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.05);
    color: #e2e8f0 !important;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    text-decoration: none !important;
    position: relative;
    overflow: hidden;
    margin: 2px; /* إضافة هامش صغير بين الأيقونات */
    cursor: pointer;
    z-index: 1;
}

/* تأثير الخلفية عند التحويم */
.social-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s ease;
    z-index: -1;
}

.social-icon:hover::before {
    transform: scale(1);
}

.social-icon:hover {
    color: white !important;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.social-icon:active {
    transform: translateY(0);
}

/* إزالة أي تأثيرات قد تتداخل مع الروابط */
.social-icon i {
    pointer-events: none;
}

/* تأثير نبض للأيقونات */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.social-icon:hover i {
    animation: pulse 1s infinite;
}

/* ألوان مخصصة لأيقونات التواصل الاجتماعي */
.social-icon .bi-facebook:hover {
    color: #1877f2 !important;
}

/* أيقونة X (تويتر سابقًا) */
.social-icon .bi-twitter-x:hover {
    color: #ffffff !important;
}

/* للتوافق مع الإصدارات القديمة */
.social-icon .bi-twitter:hover,
.social-icon .bi-x-twitter:hover,
.social-icon .bi-x:hover {
    color: #ffffff !important;
}

.social-icon .bi-instagram:hover {
    color: #e4405f !important;
}

.social-icon .bi-youtube:hover {
    color: #ff0000 !important;
}

.social-icon .bi-linkedin:hover {
    color: #0077b5 !important;
}

/* تعريف خاص لأيقونة البريد الإلكتروني */
.social-icon .bi-envelope,
.social-icon .email-icon {
    color: #ffffff !important; /* تعيين اللون الأبيض بشكل إجباري */
}

/* تأكيد إضافي لأيقونة البريد الإلكتروني */
.bi-envelope.text-white,
.bi-envelope.email-icon,
footer .social-icons a[href^="mailto:"] i {
    color: #ffffff !important; /* تعيين اللون الأبيض بشكل إجباري */
}

/* تعريف خاص لأيقونة البريد الإلكتروني عند التحويم */
.social-icon:hover .bi-envelope,
.social-icon:hover .email-icon {
    color: #ffffff !important; /* تعيين اللون الأبيض بشكل إجباري عند التحويم */
}

.social-icon .bi-envelope:hover {
    color: #ffffff !important;
}

/* === استمرار تنسيقات footer-fix الأصلية === */

/* تحسين تنظيم الصفحة */
.main-container {
    padding-bottom: 0 !important; /* إزالة المساحة السفلية */
    margin-bottom: 0 !important; /* إزالة الهامش السفلي */
}

/* تقليل المسافات بين العناصر */
.converter-container {
    margin: 0 auto 0.5rem !important; /* تقليل الهامش السفلي بشكل كبير */
}

/* تأكيد أن حاويات المحتوى تتمدد بشكل صحيح */
.tab-content.active,
.converter-container,
.text-areas-container {
    height: auto !important;
    min-height: auto !important;
    margin-bottom: 0 !important;
}

/* تأكيد أن الفوتر يظهر بشكل كامل */
.tab-content-container {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}

/* الحفاظ على تنسيق حاوية المعلومات */
.info-container {
    margin: 1rem auto 1rem !important; /* زيادة الهامش العلوي إلى 16 بكسل (1rem) */
    padding: 0.75rem !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 0.75rem !important;
    background-color: #e0f2fe !important;
    border-radius: 0.375rem !important;
    color: #0369a1 !important;
    width: 100% !important;
    box-sizing: border-box !important;
}
