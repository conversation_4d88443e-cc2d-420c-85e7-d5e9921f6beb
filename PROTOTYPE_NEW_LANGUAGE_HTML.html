<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Language System Prototype - Tifinagh Converter</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        /* تصميم مبسط ونظيف للنظام الجديد */
        .language-switcher {
            position: relative;
        }
        
        .language-switcher .dropdown-toggle {
            min-width: 140px;
            border-radius: 25px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .language-switcher .dropdown-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .language-switcher .dropdown-toggle.loading {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .language-switcher .dropdown-menu {
            border-radius: 15px;
            border: none;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            padding: 0.5rem 0;
            min-width: 200px;
        }
        
        .language-switcher .dropdown-item {
            padding: 0.75rem 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.2s ease;
            border-radius: 0;
        }
        
        .language-switcher .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
        
        .language-switcher .dropdown-item.active {
            background-color: #0d6efd;
            color: white;
        }
        
        .language-switcher .dropdown-item.active:hover {
            background-color: #0b5ed7;
        }
        
        .language-flag {
            font-size: 1.2em;
            width: 24px;
            text-align: center;
        }
        
        .language-name {
            font-weight: 500;
        }
        
        .language-check {
            margin-left: auto;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .dropdown-item.active .language-check {
            opacity: 1;
        }
        
        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .language-switcher .dropdown-menu {
                min-width: 250px;
                left: 50% !important;
                transform: translateX(-50%) !important;
            }
            
            .language-switcher .dropdown-toggle {
                min-width: 120px;
            }
        }
        
        /* تأثيرات التحميل */
        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* تحسينات الوصولية */
        .language-switcher .dropdown-toggle:focus {
            outline: 2px solid #0d6efd;
            outline-offset: 2px;
        }
        
        .language-switcher .dropdown-item:focus {
            outline: 2px solid #0d6efd;
            outline-offset: -2px;
        }
        
        /* تصميم الصفحة التجريبية */
        .demo-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            background: linear-gradient(135deg, #0d6efd, #6610f2);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .feature-list li::before {
            content: "✅";
            font-size: 1.2em;
        }
    </style>
</head>
<body class="bg-light">
    
    <!-- Header مع زر تبديل اللغة الجديد -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand fw-bold" href="#" data-i18n="common.app_name">
                Tifinagh Converter
            </a>
            
            <!-- زر تبديل اللغة الجديد - مبسط ونظيف -->
            <div class="language-switcher ms-auto" data-language-system>
                <button class="btn btn-outline-primary dropdown-toggle" 
                        type="button" 
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                        aria-label="تبديل اللغة">
                    <i class="bi bi-globe2"></i>
                    <span data-current-language>English</span>
                    <div class="loading-spinner d-none"></div>
                </button>
                
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="#" data-language="en">
                            <span class="language-flag">🇺🇸</span>
                            <span class="language-name">English</span>
                            <i class="bi bi-check2 language-check"></i>
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#" data-language="am">
                            <span class="language-flag">ⵣ</span>
                            <span class="language-name">ⵜⴰⵎⴰⵣⵉⵖⵜ</span>
                            <i class="bi bi-check2 language-check"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- محتوى الصفحة التجريبية -->
    <div class="demo-container">
        
        <!-- Header -->
        <div class="demo-header">
            <h1 data-i18n="demo.title">🌐 New Language System Prototype</h1>
            <p class="mb-0" data-i18n="demo.subtitle">Clean, Simple, and Efficient</p>
        </div>
        
        <!-- مقارنة النظام القديم والجديد -->
        <div class="demo-section">
            <h2 data-i18n="demo.comparison_title">📊 مقارنة النظام القديم والجديد</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h4 class="text-danger" data-i18n="demo.old_system">❌ النظام القديم</h4>
                    <ul class="feature-list">
                        <li data-i18n="demo.old_complex">857 سطر كود معقد</li>
                        <li data-i18n="demo.old_files">3 ملفات JavaScript منفصلة</li>
                        <li data-i18n="demo.old_bugs">مشاكل متكررة وأخطاء</li>
                        <li data-i18n="demo.old_maintenance">صعوبة في الصيانة</li>
                        <li data-i18n="demo.old_performance">أداء بطيء</li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h4 class="text-success" data-i18n="demo.new_system">✅ النظام الجديد</h4>
                    <ul class="feature-list">
                        <li data-i18n="demo.new_simple">300 سطر كود مبسط</li>
                        <li data-i18n="demo.new_files">ملف واحد منظم</li>
                        <li data-i18n="demo.new_stable">مستقر وموثوق</li>
                        <li data-i18n="demo.new_maintenance">سهل الصيانة والتطوير</li>
                        <li data-i18n="demo.new_performance">أداء سريع ومحسن</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- اختبار الوظائف -->
        <div class="demo-section">
            <h2 data-i18n="demo.test_title">🧪 اختبار الوظائف</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 data-i18n="demo.manual_test">اختبار يدوي:</h5>
                    <button class="btn btn-primary me-2" onclick="testEnglish()" data-i18n="demo.test_english">
                        Test English
                    </button>
                    <button class="btn btn-secondary" onclick="testAmazigh()" data-i18n="demo.test_amazigh">
                        Test Amazigh
                    </button>
                </div>
                
                <div class="col-md-6">
                    <h5 data-i18n="demo.current_lang">اللغة الحالية:</h5>
                    <div class="alert alert-info">
                        <strong id="current-lang-display">English</strong>
                        <br>
                        <small id="current-lang-code">en</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- نصوص تجريبية للترجمة -->
        <div class="demo-section">
            <h2 data-i18n="demo.sample_text">📝 نصوص تجريبية</h2>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title" data-i18n="common.welcome">Welcome</h5>
                            <p class="card-text" data-i18n="demo.welcome_text">
                                Welcome to our improved language system
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title" data-i18n="demo.features">Features</h5>
                            <p class="card-text" data-i18n="demo.features_text">
                                Fast, reliable, and easy to use
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title" data-i18n="demo.support">Support</h5>
                            <p class="card-text" data-i18n="demo.support_text">
                                Full support for all devices
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Mock translations for demo -->
    <script>
        // ترجمات تجريبية للعرض
        window.mockTranslations = {
            "demo.title": {
                "en": "🌐 New Language System Prototype",
                "am": "🌐 ⴰⵎⴷⵢⴰ ⵏ ⵓⵏⴰⴳⵔⴰⵡ ⴰⵎⴰⵢⵏⵓ ⵏ ⵜⵓⵜⵍⴰⵢⵉⵏ"
            },
            "demo.subtitle": {
                "en": "Clean, Simple, and Efficient",
                "am": "ⴰⵣⴷⴷⴰⵢ, ⴰⴼⵙⵓⵙ, ⴷ ⵓⴼⵔⵉⵏ"
            },
            "common.welcome": {
                "en": "Welcome",
                "am": "ⴰⵏⵙⵓⴼ"
            },
            "demo.welcome_text": {
                "en": "Welcome to our improved language system",
                "am": "ⴰⵏⵙⵓⴼ ⵖⵔ ⵓⵏⴰⴳⵔⴰⵡ ⵏⵏⴰⵖ ⵏ ⵜⵓⵜⵍⴰⵢⵉⵏ ⵉⵜⵜⵓⵙⵏⴼⵍⵏ"
            },
            "demo.features": {
                "en": "Features",
                "am": "ⵜⵉⵎⵉⵣⴰⵔ"
            },
            "demo.features_text": {
                "en": "Fast, reliable, and easy to use",
                "am": "ⴰⵔⵣⵓ, ⴰⵎⵜⵜⴰⴽⵍ, ⴷ ⴼⵙⵓⵙ ⴳ ⵓⵙⵎⵔⵙ"
            },
            "demo.support": {
                "en": "Support",
                "am": "ⴰⵙⴰⵔⴰⴳ"
            },
            "demo.support_text": {
                "en": "Full support for all devices",
                "am": "ⴰⵙⴰⵔⴰⴳ ⴰⵎⵓⵔⵙ ⵉ ⵎⴰⵕⵕⴰ ⵉⵎⴰⵙⵙⵏ"
            }
        };
        
        // Mock API endpoints
        window.fetch = function(url, options) {
            if (url === '/get-i18n') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        translations: window.mockTranslations
                    })
                });
            }
            
            if (url === '/set-language') {
                return Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        message: 'Language changed successfully'
                    })
                });
            }
            
            return Promise.reject(new Error('Unknown endpoint'));
        };
        
        // دوال اختبار
        function testEnglish() {
            if (window.LanguageSystem) {
                window.LanguageSystem.setLanguage('en');
            }
        }
        
        function testAmazigh() {
            if (window.LanguageSystem) {
                window.LanguageSystem.setLanguage('am');
            }
        }
        
        // تحديث عرض اللغة الحالية
        document.addEventListener('language:changed', function(event) {
            const langDisplay = document.getElementById('current-lang-display');
            const codeDisplay = document.getElementById('current-lang-code');
            
            if (langDisplay && codeDisplay) {
                langDisplay.textContent = event.detail.languageData.nativeName;
                codeDisplay.textContent = event.detail.newLanguage;
            }
        });
    </script>
    
    <!-- النظام الجديد -->
    <script src="PROTOTYPE_NEW_LANGUAGE_SYSTEM.js"></script>
    
</body>
</html>
