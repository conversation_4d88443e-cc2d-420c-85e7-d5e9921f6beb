"""
وحدة التعامل مع مواقع التواصل الاجتماعي
تحتوي على الوظائف اللازمة للتعامل مع إعدادات مواقع التواصل الاجتماعي
"""

import os
import json
import logging

# إعداد التسجيل
logger = logging.getLogger(__name__)

# المسار الافتراضي لملف الإعدادات
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
SETTINGS_FILE = os.path.join(DATA_DIR, 'settings.json')

# قائمة بمواقع التواصل الاجتماعي المدعومة
SUPPORTED_SOCIAL_MEDIA = {
    'facebook': {
        'name': 'Facebook',
        'icon': 'bi-facebook',
        'color': 'text-primary',
        'url_pattern': 'https://facebook.com/'
    },
    'twitter': {
        'name': 'X',
        'icon': 'bi-twitter-x',
        'color': 'text-info',
        'url_pattern': 'https://x.com/'
    },
    'instagram': {
        'name': 'Instagram',
        'icon': 'bi-instagram',
        'color': 'text-danger',
        'url_pattern': 'https://instagram.com/'
    },
    'youtube': {
        'name': 'YouTube',
        'icon': 'bi-youtube',
        'color': 'text-danger',
        'url_pattern': 'https://youtube.com/'
    },
    'linkedin': {
        'name': 'LinkedIn',
        'icon': 'bi-linkedin',
        'color': 'text-primary',
        'url_pattern': 'https://linkedin.com/'
    },
    'github': {
        'name': 'GitHub',
        'icon': 'bi-github',
        'color': 'text-dark',
        'url_pattern': 'https://github.com/'
    },
    'email': {
        'name': 'Email',
        'icon': 'bi-envelope',
        'color': 'text-white',
        'url_pattern': 'mailto:'
    }
}

def load_settings():
    """
    تحميل إعدادات التطبيق من ملف JSON
    """
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"Settings file not found: {SETTINGS_FILE}")
            return {}
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        return {}

def save_settings(settings):
    """
    حفظ إعدادات التطبيق في ملف JSON
    """
    try:
        # التأكد من وجود مجلد البيانات
        os.makedirs(os.path.dirname(SETTINGS_FILE), exist_ok=True)

        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)

        return True
    except Exception as e:
        logger.error(f"Error saving settings: {str(e)}")
        return False

def get_social_media():
    """
    الحصول على إعدادات مواقع التواصل الاجتماعي
    """
    settings = load_settings()

    # إذا لم تكن إعدادات مواقع التواصل الاجتماعي موجودة، إنشاء إعدادات افتراضية
    if 'social_media' not in settings:
        settings['social_media'] = {}

        # إنشاء إعدادات افتراضية لكل موقع
        for site_id in SUPPORTED_SOCIAL_MEDIA:
            settings['social_media'][site_id] = {
                'enabled': False,
                'url': ''
            }

        # حفظ الإعدادات
        save_settings(settings)

    return settings.get('social_media', {})

def update_social_media(social_media_settings):
    """
    تحديث إعدادات مواقع التواصل الاجتماعي

    Args:
        social_media_settings (dict): إعدادات مواقع التواصل الاجتماعي الجديدة

    Returns:
        bool: True إذا تم التحديث بنجاح، False في حالة حدوث خطأ
    """
    try:
        settings = load_settings()

        # تحديث إعدادات مواقع التواصل الاجتماعي
        settings['social_media'] = social_media_settings

        # حفظ الإعدادات
        return save_settings(settings)
    except Exception as e:
        logger.error(f"Error updating social media settings: {str(e)}")
        return False

def get_active_social_media():
    """
    الحصول على مواقع التواصل الاجتماعي النشطة فقط

    Returns:
        dict: قاموس يحتوي على مواقع التواصل الاجتماعي النشطة فقط
    """
    social_media = get_social_media()
    active_social_media = {}

    for site_id, site_data in social_media.items():
        if site_data.get('enabled', False) and site_data.get('url', '').strip():
            active_social_media[site_id] = site_data

    return active_social_media
