/**
 * إصلاح التوافق بين النظام الجديد والقديم
 * Compatibility Fix for New Language System
 * Version: 1.1.0 - Fixed getter conflicts
 */

(function() {
    'use strict';

    console.log('🔧 Loading compatibility fix v1.1.0...');

    // التأكد من وجود النظام الجديد
    if (!window.LanguageSystem) {
        console.warn('⚠️ New LanguageSystem not found yet. Will retry when available.');
        // إعادة المحاولة بعد قليل
        setTimeout(arguments.callee, 100);
        return;
    }

    // حفظ مرجع للنظام الموجود إذا كان متوفراً
    const existingI18n = window.i18n;

    // إنشاء wrapper للتوافق مع النظام القديم
    function createCompatibilityWrapper() {
        // قائمة callbacks في انتظار التحميل
        const pendingCallbacks = [];
        let systemReady = false;

        // مراقبة جاهزية النظام
        function checkSystemReady() {
            if (window.LanguageSystem && window.LanguageSystem.isReady()) {
                systemReady = true;

                // تنفيذ جميع callbacks المنتظرة
                while (pendingCallbacks.length > 0) {
                    const callback = pendingCallbacks.shift();
                    try {
                        callback();
                    } catch (error) {
                        console.error('Callback execution error:', error);
                    }
                }

                console.log('✅ Compatibility wrapper: System ready, callbacks executed');
                return true;
            }
            return false;
        }
        
        // إنشاء كائن التوافق - دوال فقط، الخصائص ستُضاف لاحقاً
        const compatibilityWrapper = {
            
            // دوال أساسية
            translate: function(key, params) {
                if (window.LanguageSystem) {
                    return window.LanguageSystem.translate(key, params);
                }
                return key;
            },
            
            setLanguage: function(lang) {
                if (window.LanguageSystem) {
                    return window.LanguageSystem.setLanguage(lang);
                }
                console.warn('LanguageSystem not ready for setLanguage');
            },
            
            updateUI: function() {
                if (window.LanguageUI) {
                    return window.LanguageUI.updateUI();
                }
                console.warn('LanguageUI not ready for updateUI');
            },
            
            // دالة onLoad المحسنة للتوافق
            onLoad: function(callback) {
                if (typeof callback !== 'function') {
                    console.warn('onLoad: callback must be a function');
                    return;
                }
                
                if (systemReady || checkSystemReady()) {
                    // النظام جاهز، تنفيذ فوري
                    try {
                        callback();
                        console.log('✅ onLoad: Callback executed immediately');
                    } catch (error) {
                        console.error('onLoad callback error:', error);
                    }
                } else {
                    // إضافة للقائمة المنتظرة
                    pendingCallbacks.push(callback);
                    console.log('⏳ onLoad: Callback queued, waiting for system ready');
                }
            },
            
            // دالة init للتوافق
            init: function() {
                if (window.LanguageSystem) {
                    return window.LanguageSystem.init();
                }
                console.warn('LanguageSystem not available for init');
            }
        };
        
        return compatibilityWrapper;
    }
    
    // إنشاء wrapper التوافق
    const compatWrapper = createCompatibilityWrapper();
    
    // إنشاء window.i18n جديد بدلاً من محاولة الدمج
    // هذا يتجنب تعارضات getters/setters
    const newI18n = Object.create(null);

    // نسخ الدوال من compatWrapper
    Object.keys(compatWrapper).forEach(key => {
        if (typeof compatWrapper[key] === 'function') {
            newI18n[key] = compatWrapper[key];
        }
    });

    // إضافة الخصائص كـ getters آمنة
    Object.defineProperty(newI18n, 'currentLang', {
        get: function() {
            return window.LanguageSystem ? window.LanguageSystem.getCurrentLanguage() : 'en';
        },
        enumerable: true
    });

    Object.defineProperty(newI18n, 'isLoaded', {
        get: function() {
            return window.LanguageSystem ? window.LanguageSystem.isReady() : false;
        },
        enumerable: true
    });

    Object.defineProperty(newI18n, 'translations', {
        get: function() {
            return window.mockTranslations || {};
        },
        enumerable: true
    });

    // استبدال window.i18n
    window.i18n = newI18n;
    console.log('🔧 Created new window.i18n compatibility wrapper (v1.1.0)');
    
    // مراقبة أحداث النظام الجديد
    document.addEventListener('system:ready', function() {
        console.log('🎉 Compatibility fix: System ready event received');
        
        // إطلاق حدث languageSystemLoaded للتوافق مع الكود القديم
        const legacyEvent = new CustomEvent('languageSystemLoaded', {
            detail: {
                translations: window.i18n.translations
            }
        });
        document.dispatchEvent(legacyEvent);
        console.log('🔧 Legacy languageSystemLoaded event dispatched');
    });
    
    document.addEventListener('language:changed', function(event) {
        console.log('🔧 Compatibility fix: Language changed to', event.detail.newLanguage);
        
        // إطلاق حدث languageChanged للتوافق
        const legacyEvent = new CustomEvent('languageChanged', {
            detail: {
                language: event.detail.newLanguage
            }
        });
        document.dispatchEvent(legacyEvent);
    });
    
    // محاولة تهيئة فورية إذا كان النظام جاهز
    setTimeout(function() {
        if (window.LanguageSystem && window.LanguageSystem.isReady()) {
            const readyEvent = new CustomEvent('system:ready');
            document.dispatchEvent(readyEvent);
        }
    }, 100);
    
    console.log('✅ Compatibility fix loaded successfully');
    
})();

// إضافة دعم للدوال المساعدة العامة
if (!window.__) {
    window.__ = function(key, params) {
        return window.i18n ? window.i18n.translate(key, params) : key;
    };
}

if (!window.setLang) {
    window.setLang = function(lang) {
        return window.i18n ? window.i18n.setLanguage(lang) : null;
    };
}

if (!window.getCurrentLang) {
    window.getCurrentLang = function() {
        return window.i18n ? window.i18n.currentLang : 'en';
    };
}

console.log('🔧 Compatibility fix: Global helper functions ready');
