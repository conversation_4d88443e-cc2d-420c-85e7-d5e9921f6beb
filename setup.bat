@echo off
echo إعداد بيئة تطبيق محول تيفيناغ...
echo.

echo حذف البيئة الافتراضية القديمة (إن وجدت)...
if exist venv (
    rmdir /s /q venv
)
if exist .venv (
    rmdir /s /q .venv
)

echo إنشاء بيئة افتراضية جديدة...
python -m venv venv

echo تنشيط البيئة الافتراضية...
call venv\Scripts\activate

echo تحديث pip...
python -m pip install --upgrade pip

echo تثبيت المكتبات المطلوبة...
pip install flask werkzeug jinja2 python-dotenv python-docx bs4 docx2python

echo تم إعداد البيئة بنجاح!
echo.
echo لتشغيل التطبيق، استخدم الأمر: run.bat
echo.
pause
