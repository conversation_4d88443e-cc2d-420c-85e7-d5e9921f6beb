/**
 * Editors CSS
 * ملف مخصص لتنسيقات المحررين (البسيط والمتقدم)
 */

/*
 * تنسيقات المحرر البسيط
 */

/* حاوية مناطق النص */
.text-areas-container {
    display: flex;
    width: 100%;
    padding: 0;
    box-sizing: border-box;
}

/* منطقة النص */
.text-area {
    flex: 1;
    padding: 0;
    box-sizing: border-box;
}

/* مساحة التحرير والنص بالتيفيناغ */
.text-area textarea,
.text-area .tifinagh-text {
    width: 100%;
    min-height: 500px;
    padding: 1rem;
    border: none;
    resize: vertical;
    font-size: 1rem;
    outline: none;
    box-sizing: border-box;
    overflow-y: auto;
    max-height: 500px;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.5;
    transition: font-size 0.2s, text-align 0.2s;
}

/* تنسيقات خاصة بنص التيفيناغ */
.text-area .tifinagh-text {
    font-family: 'Noto Sans Tifinagh', sans-serif;
    font-size: 1.2rem;
    background-color: #f8f9fa;
}

/* تنسيقات للجداول داخل النص */
.text-area .tifinagh-text.contains-table {
    overflow-x: auto;
    padding: 1rem;
}

/* تنسيقات الجداول */
.text-area textarea table,
.text-area .tifinagh-text table,
.converted-table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    border-radius: 0.25rem;
}

.text-area textarea td,
.text-area textarea th,
.text-area .tifinagh-text td,
.text-area .tifinagh-text th,
.converted-table td,
.converted-table th {
    border: 1px solid var(--border-color);
    padding: 0.75rem;
    text-align: left;
    vertical-align: top;
}

.text-area textarea th,
.text-area .tifinagh-text th,
.converted-table th {
    background-color: var(--bg-light);
    font-weight: bold;
    border-bottom: 2px solid var(--border-color);
}

/* تخطيط متناوب للصفوف */
.text-area .tifinagh-text table tr:nth-child(even),
.converted-table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تأثير التحويم على الصفوف */
.text-area .tifinagh-text table tr:hover,
.converted-table tr:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
}

/* تذييل منطقة النص */
.text-area-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 0.875rem;
}

/* أزرار التحكم في النص */
.clear-btn,
.copy-btn,
.download-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    cursor: pointer;
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
}

.clear-btn:hover,
.copy-btn:hover,
.download-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.clear-btn:active,
.copy-btn:active,
.download-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.text-actions {
    display: flex;
    gap: 0.5rem;
}

/*
 * تنسيقات المحرر المتقدم
 */

/* حاوية المحرر المتقدم */
.advanced-editor-wrapper {
    margin-bottom: 0;
    padding-bottom: 0;
}

/* منطقة المحتوى */
.document-editor-content {
    min-height: 450px;
    max-height: 500px;
    overflow-y: auto;
    padding: 1.5rem;
    background-color: #fff;
    font-family: 'Roboto', 'Noto Sans Tifinagh', 'Noto Sans Arabic', Arial, sans-serif;
    font-size: 16px;
    line-height: 1.6;
}

/* منطقة المحتوى القابلة للتحرير */
.document-editor__editable {
    min-height: 450px;
    max-height: 500px;
    padding: 1.5rem 2.5rem;
    border: none;
    box-shadow: none;
    background-color: #fff;
    overflow-y: auto;
}

/* تنسيقات CKEditor */
.ck.ck-editor__main > .ck-editor__editable {
    max-height: 500px;
    min-height: 450px;
    overflow-y: auto;
}

/* تنسيقات شريط أدوات CKEditor */
#advanced-editor-container .ck-toolbar {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #f9f9f9;
    border-color: var(--border-color);
}

/* تنسيقات منطقة محتوى CKEditor */
#advanced-editor-container .ck-content {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-color: var(--border-color);
    min-height: 450px;
    max-height: 500px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
}

/* تم إزالة مؤشر المحتوى الإضافي لتجنب ظهور شريط في منتصف المحرر */

/* تنسيقات للأجهزة المحمولة */
@media (max-width: 768px) {
    .text-areas-container {
        flex-direction: column;
    }

    .text-area {
        width: 100%;
    }

    .latin-area {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    /* تعديل ارتفاع مساحة التحرير على الأجهزة المحمولة */
    .text-area textarea,
    .text-area .tifinagh-text {
        min-height: 400px;
        max-height: 500px;
    }

    /* تعديل الجداول على الأجهزة المحمولة */
    .text-area .tifinagh-text table,
    .converted-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}
