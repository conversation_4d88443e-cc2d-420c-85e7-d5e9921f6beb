/**
 * Dropdown Fix JavaScript
 * Ensures proper dropdown behavior and positioning
 * Version: 1.0.0
 */

(function() {
    'use strict';
    
    console.log('🔧 Dropdown Fix JavaScript loaded');
    
    function initDropdownFix() {
        console.log('🔧 Initializing dropdown fix...');
        
        // Find all language selector dropdowns
        const languageDropdowns = document.querySelectorAll('.language-selector .dropdown');
        
        languageDropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.dropdown-toggle');
            const menu = dropdown.querySelector('.dropdown-menu');
            
            if (!toggle || !menu) {
                console.log('⚠️ Dropdown elements not found');
                return;
            }
            
            console.log('🔧 Setting up dropdown:', dropdown);
            
            // Ensure proper Bootstrap dropdown initialization
            if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
                try {
                    // Initialize Bootstrap dropdown
                    const bsDropdown = new bootstrap.Dropdown(toggle);
                    console.log('✅ Bootstrap dropdown initialized');
                    
                    // Add event listeners for better control
                    toggle.addEventListener('show.bs.dropdown', function(e) {
                        console.log('🔧 Dropdown showing...');
                        
                        // Ensure menu is positioned correctly
                        setTimeout(() => {
                            positionDropdownMenu(menu, toggle);
                        }, 10);
                    });
                    
                    toggle.addEventListener('shown.bs.dropdown', function(e) {
                        console.log('✅ Dropdown shown');
                        
                        // Focus first item for accessibility
                        const firstItem = menu.querySelector('.dropdown-item:not(.disabled)');
                        if (firstItem) {
                            firstItem.focus();
                        }
                    });
                    
                    toggle.addEventListener('hide.bs.dropdown', function(e) {
                        console.log('🔧 Dropdown hiding...');
                    });
                    
                    toggle.addEventListener('hidden.bs.dropdown', function(e) {
                        console.log('✅ Dropdown hidden');
                    });
                    
                } catch (error) {
                    console.error('❌ Error initializing Bootstrap dropdown:', error);
                    // Fallback to manual dropdown handling
                    setupManualDropdown(toggle, menu);
                }
            } else {
                console.log('⚠️ Bootstrap not available, using manual dropdown');
                setupManualDropdown(toggle, menu);
            }
        });
    }
    
    function positionDropdownMenu(menu, toggle) {
        console.log('🔧 Positioning dropdown menu...');
        
        // Get toggle button position
        const toggleRect = toggle.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // Reset any previous positioning
        menu.style.position = 'absolute';
        menu.style.top = '100%';
        menu.style.left = 'auto';
        menu.style.right = '0';
        menu.style.transform = 'none';
        menu.style.zIndex = '10000';
        
        // Check if dropdown would go off-screen
        const menuRect = menu.getBoundingClientRect();
        
        // Adjust horizontal position if needed
        if (toggleRect.right < menuRect.width) {
            menu.style.left = '0';
            menu.style.right = 'auto';
        }
        
        // Adjust vertical position if needed
        if (toggleRect.bottom + menuRect.height > viewportHeight) {
            menu.style.top = 'auto';
            menu.style.bottom = '100%';
        }
        
        console.log('✅ Dropdown menu positioned');
    }
    
    function setupManualDropdown(toggle, menu) {
        console.log('🔧 Setting up manual dropdown...');
        
        let isOpen = false;
        
        // Toggle dropdown on click
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            isOpen = !isOpen;
            
            if (isOpen) {
                showDropdown(menu, toggle);
            } else {
                hideDropdown(menu);
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (isOpen && !toggle.contains(e.target) && !menu.contains(e.target)) {
                isOpen = false;
                hideDropdown(menu);
            }
        });
        
        // Close dropdown on escape key
        document.addEventListener('keydown', function(e) {
            if (isOpen && e.key === 'Escape') {
                isOpen = false;
                hideDropdown(menu);
                toggle.focus();
            }
        });
        
        // Handle arrow keys for navigation
        menu.addEventListener('keydown', function(e) {
            if (!isOpen) return;
            
            const items = menu.querySelectorAll('.dropdown-item:not(.disabled)');
            const currentIndex = Array.from(items).indexOf(document.activeElement);
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
                    items[nextIndex].focus();
                    break;
                    
                case 'ArrowUp':
                    e.preventDefault();
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
                    items[prevIndex].focus();
                    break;
                    
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    if (document.activeElement && document.activeElement.classList.contains('dropdown-item')) {
                        document.activeElement.click();
                    }
                    break;
            }
        });
        
        console.log('✅ Manual dropdown setup complete');
    }
    
    function showDropdown(menu, toggle) {
        console.log('🔧 Showing dropdown manually...');
        
        menu.classList.add('show');
        toggle.setAttribute('aria-expanded', 'true');
        
        // Position the menu
        positionDropdownMenu(menu, toggle);
        
        // Focus first item
        setTimeout(() => {
            const firstItem = menu.querySelector('.dropdown-item:not(.disabled)');
            if (firstItem) {
                firstItem.focus();
            }
        }, 100);
        
        console.log('✅ Dropdown shown manually');
    }
    
    function hideDropdown(menu) {
        console.log('🔧 Hiding dropdown manually...');
        
        menu.classList.remove('show');
        const toggle = menu.closest('.dropdown').querySelector('.dropdown-toggle');
        if (toggle) {
            toggle.setAttribute('aria-expanded', 'false');
        }
        
        console.log('✅ Dropdown hidden manually');
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDropdownFix);
    } else {
        initDropdownFix();
    }
    
    // Re-initialize on window resize to adjust positioning
    window.addEventListener('resize', function() {
        console.log('🔧 Window resized, checking dropdown positions...');
        
        const openDropdowns = document.querySelectorAll('.language-selector .dropdown-menu.show');
        openDropdowns.forEach(menu => {
            const toggle = menu.closest('.dropdown').querySelector('.dropdown-toggle');
            if (toggle) {
                positionDropdownMenu(menu, toggle);
            }
        });
    });
    
    // Handle orientation change on mobile
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            console.log('🔧 Orientation changed, checking dropdown positions...');
            
            const openDropdowns = document.querySelectorAll('.language-selector .dropdown-menu.show');
            openDropdowns.forEach(menu => {
                const toggle = menu.closest('.dropdown').querySelector('.dropdown-toggle');
                if (toggle) {
                    positionDropdownMenu(menu, toggle);
                }
            });
        }, 100);
    });
    
    console.log('✅ Dropdown Fix JavaScript initialized');
    
})();
