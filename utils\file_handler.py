"""
File handling utilities for Latin to Tifinagh conversion.
"""
import os
import tempfile
import logging
from .converter import latin_to_tifinagh
from .docx_converter import convert_docx_to_tifinagh

# استيراد المحول المتكامل
try:
    from .docx_converter_complete import convert_docx_complete
    COMPLETE_CONVERTER_AVAILABLE = True
    logging.info("تم تحميل المحول المتكامل لملفات Word بنجاح")
except ImportError:
    COMPLETE_CONVERTER_AVAILABLE = False
    logging.warning("المحول المتكامل لملفات Word غير متوفر. سيتم استخدام المحول البديل.")

# التحقق من توفر مكتبة docx2python
DOCX2PYTHON_AVAILABLE = False
try:
    import docx2python  # type: ignore # noqa
    docx2python_version = getattr(docx2python, "__version__", "unknown")
    DOCX2PYTHON_AVAILABLE = True
    logging.info(f"تم تحميل مكتبة docx2python بنجاح (الإصدار: {docx2python_version})")
except ImportError:
    logging.warning("مكتبة docx2python غير متوفرة. لن يتم دعم بعض ميزات ملفات Word.")

# إعداد التسجيل (logging)
logger = logging.getLogger(__name__)

def process_file(file_path, target_format='html'):
    """
    معالجة الملف وتحويله إلى تيفيناغ

    Args:
        file_path (str): مسار الملف المراد تحويله
        target_format (str): تنسيق الملف الناتج (html أو docx أو txt)

    Returns:
        str: مسار الملف الناتج

    Raises:
        ValueError: إذا كان تنسيق الملف غير مدعوم
        FileNotFoundError: إذا لم يتم العثور على الملف
        Exception: لأي أخطاء أخرى غير متوقعة
    """
    logger.info(f"بدء معالجة الملف: {file_path} بتنسيق مستهدف: {target_format}")

    # التحقق من وجود الملف
    if not os.path.exists(file_path):
        logger.error(f"الملف غير موجود: {file_path}")
        raise FileNotFoundError(f"الملف غير موجود: {file_path}")

    # الحصول على امتداد الملف
    _, file_extension = os.path.splitext(file_path)
    file_extension = file_extension.lower()

    # التحقق من صحة التنسيق المستهدف
    valid_formats = ['html', 'docx', 'txt']
    if target_format not in valid_formats:
        logger.warning(f"التنسيق المستهدف غير صالح: {target_format}. سيتم استخدام التنسيق الافتراضي: html")
        target_format = 'html'

    # معالجة الملف حسب نوعه
    if file_extension in ['.txt', '.md', '.text']:
        return process_text_file(file_path, target_format)
    elif file_extension in ['.docx', '.doc']:
        return process_word_file(file_path, target_format)
    elif file_extension in ['.html', '.htm']:
        return process_html_file(file_path, target_format)
    else:
        logger.error(f"تنسيق الملف غير مدعوم: {file_extension}")
        raise ValueError(f"تنسيق الملف غير مدعوم: {file_extension}")

def process_text_file(file_path, target_format='html'):
    """
    معالجة ملف نصي وتحويله إلى تيفيناغ

    Args:
        file_path (str): مسار الملف النصي
        target_format (str): تنسيق الملف الناتج

    Returns:
        str: مسار الملف الناتج
    """
    logger.info(f"معالجة ملف نصي: {file_path}")

    try:
        # قراءة محتوى الملف
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # تحويل المحتوى إلى تيفيناغ
        tifinagh_content = latin_to_tifinagh(content)

        # إنشاء ملف مؤقت لتخزين النتيجة
        fd, temp_path = tempfile.mkstemp(suffix=f'.{target_format}')
        os.close(fd)

        # كتابة المحتوى المحول في الملف المؤقت حسب التنسيق المطلوب
        if target_format == 'html':
            with open(temp_path, 'w', encoding='utf-8') as file:
                html_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Tifinagh Conversion</title>
    <style>
        body { font-family: 'Noto Sans Tifinagh', sans-serif; font-size: 18px; line-height: 1.6; }
    </style>
</head>
<body>
    <div dir="rtl">
        CONTENT_PLACEHOLDER
    </div>
</body>
</html>"""
                html_content = html_template.replace('CONTENT_PLACEHOLDER', tifinagh_content.replace('\n', '<br>'))
                file.write(html_content)
        elif target_format == 'docx':
            # إنشاء ملف docx جديد
            try:
                from docx import Document
                doc = Document()

                # إضافة النص المحول
                for paragraph_text in tifinagh_content.split('\n'):
                    if paragraph_text.strip():
                        doc.add_paragraph(paragraph_text)

                # حفظ المستند
                doc.save(temp_path)
                logger.info(f"تم إنشاء ملف Word بنجاح: {temp_path}")
            except ImportError:
                logger.warning("مكتبة python-docx غير متوفرة. سيتم حفظ النص كملف نصي.")
                # تغيير امتداد الملف إلى txt
                temp_path = os.path.splitext(temp_path)[0] + '.txt'
                with open(temp_path, 'w', encoding='utf-8') as file:
                    file.write(tifinagh_content)
        else:
            # حفظ كملف نصي عادي
            with open(temp_path, 'w', encoding='utf-8') as file:
                file.write(tifinagh_content)

        logger.info(f"تم إنشاء الملف الناتج: {temp_path}")
        return temp_path

    except Exception as e:
        logger.error(f"خطأ أثناء معالجة الملف النصي: {str(e)}")
        raise

def process_word_file(file_path, target_format='docx'):
    """
    معالجة ملف Word وتحويله إلى تيفيناغ

    Args:
        file_path (str): مسار ملف Word
        target_format (str): تنسيق الملف الناتج

    Returns:
        str: مسار الملف الناتج

    Raises:
        Exception: لأي أخطاء غير متوقعة
    """
    logger.info(f"بدء معالجة ملف Word: {file_path} بتنسيق مستهدف: {target_format}")

    # إنشاء ملف مؤقت لتخزين النتيجة
    fd, temp_path = tempfile.mkstemp(suffix=f'.{target_format}')
    os.close(fd)  # إغلاق وصف الملف

    try:
        # محاولة تحويل ملف Word باستخدام المحول المتكامل (الطريقة المفضلة)
        if COMPLETE_CONVERTER_AVAILABLE:
            try:
                logger.info("استخدام المحول المتكامل لتحويل ملف Word")
                # استخدام المحول المتكامل الذي تم استيراده مسبق
                result_path = convert_docx_complete(file_path, temp_path)
                logger.info(f"تم إنشاء ملف بنجاح باستخدام المحول المتكامل: {result_path}")
                return result_path
            except Exception as e:
                logger.warning(f"فشل التحويل باستخدام المحول المتكامل: {str(e)}")
                # إذا فشل المحول المتكامل، جرب الطرق البديلة

        # محاولة تحويل ملف Word باستخدام الدالة الرئيسية
        try:
            logger.info("استخدام المحول الرئيسي لتحويل ملف Word")
            # تحويل ملف Word إلى تيفيناغ
            result_path = convert_docx_to_tifinagh(file_path, temp_path)
            logger.info(f"تم إنشاء ملف بنجاح باستخدام المحول الرئيسي: {result_path}")
            return result_path
        except Exception as e:
            logger.warning(f"فشل التحويل باستخدام المحول الرئيسي: {str(e)}")

            # محاولة استخدام الطريقة البديلة
            if DOCX2PYTHON_AVAILABLE:
                try:
                    logger.info("محاولة استخدام docx2python كبديل")
                    from .docx_fallback import simple_docx_to_tifinagh
                    result_path = simple_docx_to_tifinagh(file_path, temp_path)
                    logger.info(f"تم إنشاء ملف بنجاح باستخدام الطريقة البديلة: {result_path}")
                    return result_path
                except Exception as e2:
                    logger.error(f"فشل باستخدام docx2python: {str(e2)}")
                    # إذا فشلت جميع المحاولات، حاول تحويل الملف إلى نص عادي
                    logger.info("محاولة تحويل الملف إلى نص عادي")

                    # استخراج النص من ملف Word
                    with open(file_path, 'rb') as f:
                        from docx import Document  # type: ignore # noqa
                        doc = Document(f)
                        text_content = "\n".join([para.text for para in doc.paragraphs if para.text])

                    # تحويل النص إلى تيفيناغ
                    tifinagh_content = latin_to_tifinagh(text_content)

                    # حفظ النص في ملف نصي
                    txt_path = os.path.splitext(temp_path)[0] + '.txt'
                    with open(txt_path, 'w', encoding='utf-8') as f:
                        f.write(tifinagh_content)

                    logger.info(f"تم تحويل الملف إلى نص عادي: {txt_path}")
                    return txt_path
            else:
                # إذا لم تكن مكتبة docx2python متوفرة، أرجع خطأ
                raise ValueError("فشل في تحويل ملف Word. المكتبات المطلوبة غير متوفرة.")
    except Exception as e:
        logger.error(f"خطأ أثناء معالجة ملف Word: {str(e)}")
        raise ValueError(f"فشل في تحويل ملف Word: {str(e)}")

def process_html_file(file_path, target_format='html'):
    """
    معالجة ملف HTML وتحويله إلى تيفيناغ

    Args:
        file_path (str): مسار ملف HTML
        target_format (str): تنسيق الملف الناتج

    Returns:
        str: مسار الملف الناتج
    """
    logger.info(f"معالجة ملف HTML: {file_path}")

    try:
        # قراءة محتوى الملف
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()

        # استخراج النص من HTML
        from bs4 import BeautifulSoup  # type: ignore # noqa
        soup = BeautifulSoup(content, 'html.parser')
        text_content = soup.get_text()

        # تحويل النص إلى تيفيناغ
        tifinagh_content = latin_to_tifinagh(text_content)

        # إنشاء ملف مؤقت لتخزين النتيجة
        fd, temp_path = tempfile.mkstemp(suffix=f'.{target_format}')
        os.close(fd)

        # كتابة المحتوى المحول في الملف المؤقت
        if target_format == 'html':
            with open(temp_path, 'w', encoding='utf-8') as file:
                html_template = """<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Tifinagh Conversion</title>
    <style>
        body { font-family: 'Noto Sans Tifinagh', sans-serif; font-size: 18px; line-height: 1.6; }
    </style>
</head>
<body>
    <div dir="rtl">
        CONTENT_PLACEHOLDER
    </div>
</body>
</html>"""
                html_content = html_template.replace('CONTENT_PLACEHOLDER', tifinagh_content.replace('\n', '<br>'))
                file.write(html_content)
        else:
            with open(temp_path, 'w', encoding='utf-8') as file:
                file.write(tifinagh_content)

        logger.info(f"تم إنشاء الملف الناتج: {temp_path}")
        return temp_path

    except Exception as e:
        logger.error(f"خطأ أثناء معالجة ملف HTML: {str(e)}")
        raise

