<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switcher - Bootstrap 5.3.3</title>
    
    <!-- Bootstrap CSS 5.3.3 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    
    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Noto Sans Tifinagh Font for Amazigh text -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Tifinagh&display=swap" rel="stylesheet">
    
    <style>
        /* تطبيق خط Inter على كامل الصفحة */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        /* خط تيفيناغ للنص الأمازيغي */
        .tifinagh-text {
            font-family: 'Noto Sans Tifinagh', sans-serif;
        }
        
        /* تحسينات إضافية للزر */
        .language-switcher-btn {
            min-width: 140px;
            transition: all 0.2s ease-in-out;
        }
        
        /* القائمة المنسدلة مع z-index عالي */
        .language-dropdown {
            z-index: 1051 !important;
            min-width: 192px;
        }
        
        /* تحسين عرض أيقونة التحديد */
        .dropdown-item .check-icon {
            width: 16px;
            margin-right: 8px;
        }
        
        .dropdown-item .check-icon.invisible {
            visibility: hidden;
        }
        
        /* تحسين التباعد */
        .dropdown-item {
            padding: 0.5rem 1rem;
            display: flex;
            align-items: center;
        }
        
        /* هيدر تجريبي */
        .demo-header {
            background-color: #f8f9fa;
            padding: 1rem 0;
            border-bottom: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <!-- هيدر تجريبي -->
    <header class="demo-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">موقع تجريبي</h4>
                
                <!-- زر تغيير اللغة -->
                <div class="dropdown">
                    <button class="btn btn-outline-primary rounded-pill shadow-sm language-switcher-btn d-flex align-items-center" 
                            type="button" 
                            id="languageDropdown" 
                            data-bs-toggle="dropdown" 
                            aria-expanded="false">
                        <i class="bi bi-globe2 me-2"></i>
                        <span id="currentLanguageText">English</span>
                    </button>
                    
                    <ul class="dropdown-menu dropdown-menu-end shadow-lg rounded language-dropdown" 
                        aria-labelledby="languageDropdown">
                        <li>
                            <button class="dropdown-item" type="button" data-lang="en">
                                <i class="bi bi-check2 check-icon text-primary"></i>
                                <span>English</span>
                            </button>
                        </li>
                        <li>
                            <button class="dropdown-item" type="button" data-lang="am">
                                <i class="bi bi-check2 check-icon invisible text-primary"></i>
                                <span class="tifinagh-text">ⵜⴰⵎⴰⵣⵉⵖⵜ</span>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </header>
    
    <!-- محتوى تجريبي -->
    <main class="container my-5">
        <div class="row">
            <div class="col-12">
                <h1 id="pageTitle">Welcome to the Demo Site</h1>
                <p id="pageDescription">This is a demonstration of the language switcher component built with Bootstrap 5.3.3.</p>
                
                <div class="alert alert-info mt-4">
                    <h5>المواصفات المطبقة:</h5>
                    <ul class="mb-0">
                        <li>✅ Bootstrap 5.3.3 CSS و JavaScript</li>
                        <li>✅ زر بخلفية بيضاء وحد أزرق فاتح (btn-outline-primary)</li>
                        <li>✅ شكل مستدير بالكامل (rounded-pill)</li>
                        <li>✅ ظل خفيف (shadow-sm)</li>
                        <li>✅ أيقونة كرة أرضية (bi-globe2)</li>
                        <li>✅ قائمة منسدلة بظل كبير (shadow-lg)</li>
                        <li>✅ z-index عالي (1051)</li>
                        <li>✅ أيقونة علامة صح للغة النشطة</li>
                        <li>✅ خط Inter للنص العام</li>
                        <li>✅ خط Noto Sans Tifinagh للأمازيغية</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Bootstrap JavaScript 5.3.3 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    
    <script>
        // نصوص الترجمة
        const translations = {
            en: {
                title: "Welcome to the Demo Site",
                description: "This is a demonstration of the language switcher component built with Bootstrap 5.3.3.",
                languageName: "English"
            },
            am: {
                title: "ⴰⵏⵙⵓⴼ ⵖⵔ ⵓⵙⵉⵜ ⴰⵎⵙⴰⵡⴰⵍ",
                description: "ⵓⵢⴰ ⴷ ⴰⵙⴽⴰⵏ ⵏ ⵓⵎⵙⵏⴼⴰⵍ ⵏ ⵜⵓⵜⵍⴰⵢⵜ ⵉⵜⵜⵓⵙⴽⴰⵔⵏ ⵙ Bootstrap 5.3.3.",
                languageName: "ⵜⴰⵎⴰⵣⵉⵖⵜ"
            }
        };
        
        let currentLanguage = 'en';
        
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Language Switcher initialized');
            
            // إعداد مستمعي الأحداث لأزرار اللغة
            const languageButtons = document.querySelectorAll('[data-lang]');
            languageButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const selectedLang = this.getAttribute('data-lang');
                    switchLanguage(selectedLang);
                });
            });
            
            // تحديث الحالة الأولية
            updateLanguageDisplay();
        });
        
        // دالة تغيير اللغة
        function switchLanguage(lang) {
            if (lang === currentLanguage) return;
            
            console.log(`🌐 Switching language from ${currentLanguage} to ${lang}`);
            currentLanguage = lang;
            
            // تحديث النصوص
            updateContent();
            
            // تحديث عرض اللغة
            updateLanguageDisplay();
            
            // إغلاق القائمة المنسدلة
            const dropdown = bootstrap.Dropdown.getInstance(document.getElementById('languageDropdown'));
            if (dropdown) {
                dropdown.hide();
            }
        }
        
        // تحديث محتوى الصفحة
        function updateContent() {
            const translation = translations[currentLanguage];
            
            document.getElementById('pageTitle').textContent = translation.title;
            document.getElementById('pageDescription').textContent = translation.description;
            document.getElementById('currentLanguageText').textContent = translation.languageName;
        }
        
        // تحديث عرض اللغة النشطة
        function updateLanguageDisplay() {
            const languageButtons = document.querySelectorAll('[data-lang]');
            
            languageButtons.forEach(button => {
                const lang = button.getAttribute('data-lang');
                const checkIcon = button.querySelector('.check-icon');
                
                if (lang === currentLanguage) {
                    checkIcon.classList.remove('invisible');
                } else {
                    checkIcon.classList.add('invisible');
                }
            });
        }
    </script>
</body>
</html>
