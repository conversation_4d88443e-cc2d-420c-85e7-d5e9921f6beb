{% extends 'admin/base.html' %}

{% block title %}إدارة الترجمات - لوحة التحكم{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">إدارة الترجمات</li>
{% endblock %}

{% block extra_css %}
<style>
    /* أنماط جدول الترجمات */
    #translationsTable {
        border-collapse: separate;
        border-spacing: 0;
    }

    #translationsTable th {
        position: sticky;
        top: 0;
        background-color: #f8f9fa;
        z-index: 10;
        font-weight: 600;
    }

    #translationsTable tbody tr {
        transition: background-color 0.2s;
    }

    #translationsTable tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    /* أنماط حقول الإدخال */
    .tifinagh-input {
        font-family: 'Noto Sans Tifinagh', sans-serif;
        font-size: 1.1rem;
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        transition: border-color 0.2s;
        width: 100%;
    }

    .tifinagh-input:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    /* أنماط شريط التقدم */
    .progress {
        height: 25px;
        margin-bottom: 1rem;
    }

    /* أنماط الأزرار */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .action-buttons .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    /* أنماط البحث والتصفية */
    .filter-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .search-container {
        position: relative;
        max-width: 300px;
    }

    .search-container .form-control {
        padding-right: 2.5rem;
    }

    .search-container .clear-search {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
    }

    /* أنماط المؤشرات */
    .stats-container {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .stat-card {
        flex: 1;
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8f9fa;
        text-align: center;
    }

    .stat-card h3 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .stat-card p {
        margin: 0;
        color: #6c757d;
    }

    /* أنماط الرسائل */
    .alert-container {
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12 d-flex justify-content-between align-items-center">
        <h1 class="h3">إدارة الترجمات</h1>
        <div>
            <button type="button" class="btn btn-primary me-2" id="extractTextsBtn">
                <i class="bi bi-arrow-clockwise me-1"></i> استخراج النصوص
            </button>
            <button type="button" class="btn btn-outline-secondary" id="exportTranslations">
                <i class="bi bi-download me-1"></i> تصدير الترجمات
            </button>
        </div>
    </div>
</div>

<!-- مؤشرات الترجمة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title mb-3">تقدم الترجمة</h5>
                <div class="progress">
                    <div id="translationProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" aria-valuenow="{{ stats.percentage }}" aria-valuemin="0" aria-valuemax="100"
                         style="width: {{ stats.percentage }}%">
                        {{ stats.percentage }}%
                    </div>
                </div>
                <div class="stats-container mt-3">
                    <div class="stat-card">
                        <h3>{{ stats.total }}</h3>
                        <p>إجمالي النصوص</p>
                    </div>
                    <div class="stat-card">
                        <h3>{{ stats.translated }}</h3>
                        <p>النصوص المترجمة</p>
                    </div>
                    <div class="stat-card">
                        <h3>{{ stats.untranslated }}</h3>
                        <p>النصوص غير المترجمة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- رسائل التنبيه -->
<div class="alert-container">
    <div id="loadingIndicator" class="alert alert-info d-none" role="alert">
        <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2" role="status"></div>
            <span>جاري التحميل...</span>
        </div>
    </div>
    <div id="errorAlert" class="alert alert-danger d-none" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <span id="errorMessage"></span>
    </div>
    <div id="successAlert" class="alert alert-success d-none" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span id="successMessage"></span>
    </div>
</div>

<!-- بطاقة جدول الترجمات -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h5 class="card-title mb-0">الترجمات</h5>
            <div class="d-flex align-items-center">
                <span class="me-2 text-muted" id="filteredCount">عرض <span id="visibleCount">0</span> من <span id="totalCount">{{ stats.total }}</span></span>
                <div class="search-container">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="بحث في الترجمات..." id="searchTranslations">
                        <button type="button" class="btn btn-outline-secondary d-none" id="clearSearch">
                            <i class="bi bi-x"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="searchButton">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="filter-container">
            <div class="d-flex flex-wrap gap-2">
                <div class="btn-group" role="group" aria-label="تصفية الترجمات">
                    <button type="button" class="btn btn-outline-secondary active" id="filterAll">الكل</button>
                    <button type="button" class="btn btn-outline-secondary" id="filterTranslated">المترجمة</button>
                    <button type="button" class="btn btn-outline-secondary" id="filterUntranslated">غير المترجمة</button>
                </div>

                <div class="btn-group" role="group" aria-label="تصفية حسب القسم">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        تصفية حسب القسم
                    </button>
                    <ul class="dropdown-menu" id="sectionFilters">
                        <li><a class="dropdown-item active" href="#" data-section="all">جميع الأقسام</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <!-- سيتم ملء هذه القائمة ديناميكيًا -->
                    </ul>
                </div>

                <div class="btn-group" role="group" aria-label="ترتيب الترجمات">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                        ترتيب حسب
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item active" href="#" data-sort="key-asc">المفتاح (تصاعدي)</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="key-desc">المفتاح (تنازلي)</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" data-sort="status-asc">الحالة (غير مترجم أولاً)</a></li>
                        <li><a class="dropdown-item" href="#" data-sort="status-desc">الحالة (مترجم أولاً)</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <!-- جدول الترجمات -->
        <div class="table-responsive">
            <table class="table table-hover" id="translationsTable">
                <thead class="table-light">
                    <tr>
                        <th width="25%">المفتاح</th>
                        <th width="35%">النص الإنجليزي</th>
                        <th width="40%">الترجمة الأمازيغية</th>
                    </tr>
                </thead>
                <tbody id="translationsTableBody">
                    <!-- سيتم ملء صفوف الجدول ديناميكيًا -->
                    {% for key, translation in translations.items() %}
                    <tr data-key="{{ key }}">
                        <td>{{ key }}</td>
                        <td>{{ translation.en }}</td>
                        <td>
                            <input type="text" class="tifinagh-input" value="{{ translation.am }}" data-key="{{ key }}" data-original="{{ translation.am }}">
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- أزرار الإجراءات وعناصر التصفح -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
                <button type="button" class="btn btn-success me-2" id="saveAllTranslationsBtn">
                    <i class="bi bi-save me-1"></i> حفظ جميع التعديلات
                </button>
                <button type="button" class="btn btn-outline-primary" id="addNewTranslationBtn">
                    <i class="bi bi-plus-circle me-1"></i> إضافة ترجمة جديدة
                </button>
            </div>

            <!-- عناصر التصفح بين الصفحات -->
            <nav aria-label="تصفح الترجمات">
                <ul class="pagination mb-0" id="translationPagination">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" id="prevPage" aria-label="السابق">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#" id="nextPage" aria-label="التالي">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- عدد العناصر في الصفحة -->
        <div class="d-flex justify-content-end mt-2">
            <div class="form-inline">
                <label class="me-2">عناصر في الصفحة:</label>
                <select class="form-select form-select-sm" id="itemsPerPage" style="width: auto;">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة ترجمة جديدة -->
<div class="modal fade" id="addTranslationModal" tabindex="-1" aria-labelledby="addTranslationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTranslationModalLabel">إضافة ترجمة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <form id="addTranslationForm">
                    <div class="mb-3">
                        <label for="newKey" class="form-label">المفتاح</label>
                        <input type="text" class="form-control" id="newKey" required>
                        <div class="form-text">مفتاح مختصر ودال (مثل: homepage.title, common.button_save)</div>
                    </div>
                    <div class="mb-3">
                        <label for="newEnglishText" class="form-label">النص الإنجليزي</label>
                        <input type="text" class="form-control" id="newEnglishText" required>
                    </div>
                    <div class="mb-3">
                        <label for="newAmazighText" class="form-label">الترجمة الأمازيغية</label>
                        <input type="text" class="form-control tifinagh-input" id="newAmazighText">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveNewTranslation">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف هذه الترجمة؟</p>
                <p>المفتاح: <strong id="deleteKeyDisplay"></strong></p>
                <input type="hidden" id="deleteKey">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // العناصر
        const loadingIndicator = document.getElementById('loadingIndicator');
        const errorAlert = document.getElementById('errorAlert');
        const errorMessage = document.getElementById('errorMessage');
        const successAlert = document.getElementById('successAlert');
        const successMessage = document.getElementById('successMessage');
        const translationsTableBody = document.getElementById('translationsTableBody');
        const searchInput = document.getElementById('searchTranslations');
        const clearSearchBtn = document.getElementById('clearSearch');
        const filterAllBtn = document.getElementById('filterAll');
        const filterTranslatedBtn = document.getElementById('filterTranslated');
        const filterUntranslatedBtn = document.getElementById('filterUntranslated');
        const extractTextsBtn = document.getElementById('extractTextsBtn');
        const exportTranslationsBtn = document.getElementById('exportTranslations');
        const addNewTranslationBtn = document.getElementById('addNewTranslationBtn');
        const saveNewTranslationBtn = document.getElementById('saveNewTranslation');
        const confirmDeleteBtn = document.getElementById('confirmDelete');

        // النوافذ المنبثقة
        const addTranslationModal = new bootstrap.Modal(document.getElementById('addTranslationModal'));
        const deleteConfirmModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));

        // حفظ الترجمة
        document.querySelectorAll('.save-translation').forEach(button => {
            button.addEventListener('click', function() {
                const key = this.dataset.key;
                const input = document.querySelector(`.tifinagh-input[data-key="${key}"]`);
                const amText = input.value;

                saveTranslation(key, amText);
            });
        });

        // حذف الترجمة
        document.querySelectorAll('.delete-translation').forEach(button => {
            button.addEventListener('click', function() {
                const key = this.dataset.key;
                document.getElementById('deleteKey').value = key;
                document.getElementById('deleteKeyDisplay').textContent = key;
                deleteConfirmModal.show();
            });
        });

        // تأكيد الحذف
        confirmDeleteBtn.addEventListener('click', function() {
            const key = document.getElementById('deleteKey').value;
            deleteTranslation(key);
            deleteConfirmModal.hide();
        });

        // إضافة ترجمة جديدة
        addNewTranslationBtn.addEventListener('click', function() {
            document.getElementById('addTranslationForm').reset();
            addTranslationModal.show();
        });

        // حفظ الترجمة الجديدة
        saveNewTranslationBtn.addEventListener('click', function() {
            const key = document.getElementById('newKey').value;
            const enText = document.getElementById('newEnglishText').value;
            const amText = document.getElementById('newAmazighText').value;

            if (!key || !enText) {
                showError('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            addNewTranslation(key, enText, amText);
            addTranslationModal.hide();
        });

        // استخراج النصوص
        extractTextsBtn.addEventListener('click', function() {
            extractTexts();
        });

        // تصدير الترجمات
        exportTranslationsBtn.addEventListener('click', function() {
            exportTranslations();
        });

        // البحث في الترجمات
        searchInput.addEventListener('input', function() {
            const searchText = this.value.toLowerCase();
            if (searchText) {
                clearSearchBtn.classList.remove('d-none');
            } else {
                clearSearchBtn.classList.add('d-none');
            }
            filterTranslations();
        });

        // مسح البحث
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            clearSearchBtn.classList.add('d-none');
            filterTranslations();
        });

        // أزرار التصفية
        filterAllBtn.addEventListener('click', function() {
            setActiveFilter(this);
            filterTranslations();
        });

        filterTranslatedBtn.addEventListener('click', function() {
            setActiveFilter(this);
            filterTranslations();
        });

        filterUntranslatedBtn.addEventListener('click', function() {
            setActiveFilter(this);
            filterTranslations();
        });

        // تعيين زر التصفية النشط
        function setActiveFilter(button) {
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
        }

        // تصفية الترجمات
        function filterTranslations() {
            const searchText = searchInput.value.toLowerCase();
            const showTranslated = filterTranslatedBtn.classList.contains('active');
            const showUntranslated = filterUntranslatedBtn.classList.contains('active');
            const showAll = filterAllBtn.classList.contains('active');

            document.querySelectorAll('#translationsTableBody tr').forEach(row => {
                const key = row.dataset.key.toLowerCase();
                const enText = row.cells[1].textContent.toLowerCase();
                const amInput = row.cells[2].querySelector('input');
                const amText = amInput.value.toLowerCase();
                const isTranslated = amText.trim() !== '';

                let show = true;

                // تصفية حسب النص
                if (searchText && !key.includes(searchText) && !enText.includes(searchText) && !amText.includes(searchText)) {
                    show = false;
                }

                // تصفية حسب حالة الترجمة
                if (showTranslated && !isTranslated) {
                    show = false;
                } else if (showUntranslated && isTranslated) {
                    show = false;
                }

                row.style.display = show ? '' : 'none';
            });
        }

        // حفظ الترجمة
        function saveTranslation(key, amText) {
            showLoading();

            fetch('/admin/api/i18n/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ key, am_text: amText }),
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showSuccess(data.message);
                    updateProgressBar();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showError('حدث خطأ أثناء حفظ الترجمة');
                console.error('Error:', error);
            });
        }

        // إضافة ترجمة جديدة
        function addNewTranslation(key, enText, amText) {
            showLoading();

            fetch('/admin/api/i18n/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ key, en_text: enText, am_text: amText }),
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showSuccess(data.message);
                    // إضافة الصف الجديد إلى الجدول
                    addRowToTable(key, enText, amText);
                    updateProgressBar();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showError('حدث خطأ أثناء إضافة الترجمة');
                console.error('Error:', error);
            });
        }

        // حذف الترجمة
        function deleteTranslation(key) {
            showLoading();

            fetch('/admin/api/i18n/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ key }),
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showSuccess(data.message);
                    // حذف الصف من الجدول
                    const row = document.querySelector(`tr[data-key="${key}"]`);
                    if (row) {
                        row.remove();
                    }
                    updateProgressBar();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showError('حدث خطأ أثناء حذف الترجمة');
                console.error('Error:', error);
            });
        }

        // استخراج النصوص
        function extractTexts() {
            showLoading();

            fetch('/admin/api/i18n/extract', {
                method: 'POST',
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showSuccess(`تم استخراج ${data.extracted} نص وإضافة ${data.added} نص جديد`);
                    // إعادة تحميل الصفحة لعرض النصوص الجديدة
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                hideLoading();
                showError('حدث خطأ أثناء استخراج النصوص');
                console.error('Error:', error);
            });
        }

        // تصدير الترجمات
        function exportTranslations() {
            window.location.href = '/admin/api/i18n/export';
        }

        // إضافة صف جديد إلى الجدول
        function addRowToTable(key, enText, amText) {
            const row = document.createElement('tr');
            row.dataset.key = key;

            row.innerHTML = `
                <td>${key}</td>
                <td>${enText}</td>
                <td>
                    <input type="text" class="tifinagh-input" value="${amText}" data-key="${key}" dir="rtl">
                </td>
                <td>
                    <div class="action-buttons">
                        <button type="button" class="btn btn-primary btn-sm save-translation" data-key="${key}">
                            <i class="bi bi-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-danger btn-sm delete-translation" data-key="${key}">
                            <i class="bi bi-trash"></i> حذف
                        </button>
                    </div>
                </td>
            `;

            translationsTableBody.appendChild(row);

            // إضافة مستمعي الأحداث للأزرار الجديدة
            row.querySelector('.save-translation').addEventListener('click', function() {
                const key = this.dataset.key;
                const input = document.querySelector(`.tifinagh-input[data-key="${key}"]`);
                const amText = input.value;
                saveTranslation(key, amText);
            });

            row.querySelector('.delete-translation').addEventListener('click', function() {
                const key = this.dataset.key;
                document.getElementById('deleteKey').value = key;
                document.getElementById('deleteKeyDisplay').textContent = key;
                deleteConfirmModal.show();
            });
        }

        // تحديث شريط التقدم
        function updateProgressBar() {
            fetch('/admin/api/i18n/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const stats = data.stats;
                        const progressBar = document.getElementById('translationProgressBar');
                        progressBar.style.width = `${stats.percentage}%`;
                        progressBar.textContent = `${stats.percentage}%`;
                        progressBar.setAttribute('aria-valuenow', stats.percentage);

                        // تحديث الإحصائيات
                        document.querySelector('.stat-card:nth-child(1) h3').textContent = stats.total;
                        document.querySelector('.stat-card:nth-child(2) h3').textContent = stats.translated;
                        document.querySelector('.stat-card:nth-child(3) h3').textContent = stats.untranslated;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        // إظهار رسالة التحميل
        function showLoading() {
            loadingIndicator.classList.remove('d-none');
            errorAlert.classList.add('d-none');
            successAlert.classList.add('d-none');
        }

        // إخفاء رسالة التحميل
        function hideLoading() {
            loadingIndicator.classList.add('d-none');
        }

        // إظهار رسالة الخطأ
        function showError(message) {
            errorMessage.textContent = message;
            errorAlert.classList.remove('d-none');
            successAlert.classList.add('d-none');

            // إخفاء رسالة الخطأ بعد 5 ثوانٍ
            setTimeout(() => {
                errorAlert.classList.add('d-none');
            }, 5000);
        }

        // إظهار رسالة النجاح
        function showSuccess(message) {
            successMessage.textContent = message;
            successAlert.classList.remove('d-none');
            errorAlert.classList.add('d-none');

            // إخفاء رسالة النجاح بعد 3 ثوانٍ
            setTimeout(() => {
                successAlert.classList.add('d-none');
            }, 3000);
        }
    });
</script>
{% endblock %}
