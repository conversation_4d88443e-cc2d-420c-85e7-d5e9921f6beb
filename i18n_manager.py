"""
مدير الترجمة (i18n) للتطبيق
"""
import os
import json
import logging
import re
from datetime import datetime
from bs4 import BeautifulSoup

# إعداد التسجيل
logger = logging.getLogger(__name__)

# مسار ملف الترجمات
I18N_FILE = 'data/i18n.json'

def load_translations():
    """
    تحميل الترجمات من ملف JSON.

    Returns:
        dict: قاموس يحتوي على الترجمات
    """
    try:
        if os.path.exists(I18N_FILE):
            with open(I18N_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"ملف الترجمات {I18N_FILE} غير موجود. إنشاء ملف فارغ.")
            save_translations({})
            return {}
    except Exception as e:
        logger.error(f"خطأ في تحميل الترجمات: {str(e)}")
        return {}

def save_translations(translations):
    """
    حفظ الترجمات في ملف JSON.

    Args:
        translations (dict): قاموس يحتوي على الترجمات

    Returns:
        bool: True إذا تم الحفظ بنجاح، False في حالة حدوث خطأ
    """
    try:
        # إنشاء مجلد البيانات إذا لم يكن موجودًا
        os.makedirs(os.path.dirname(I18N_FILE), exist_ok=True)

        # إنشاء نسخة احتياطية من الملف الحالي قبل الحفظ
        if os.path.exists(I18N_FILE):
            backup_file = f"{I18N_FILE}.bak.{datetime.now().strftime('%Y%m%d%H%M%S')}"
            with open(I18N_FILE, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            logger.info(f"تم إنشاء نسخة احتياطية من ملف الترجمات: {backup_file}")

        # حفظ الترجمات في الملف
        with open(I18N_FILE, 'w', encoding='utf-8') as f:
            json.dump(translations, f, ensure_ascii=False, indent=2)

        logger.info(f"تم حفظ الترجمات بنجاح في {I18N_FILE}")
        return True
    except Exception as e:
        logger.error(f"خطأ في حفظ الترجمات: {str(e)}")
        return False

def translate(key, lang='en'):
    """
    ترجمة نص بناءً على المفتاح واللغة.

    Args:
        key (str): مفتاح النص المراد ترجمته
        lang (str): رمز اللغة (الافتراضي: 'en')

    Returns:
        str: النص المترجم أو المفتاح نفسه إذا لم يتم العثور على ترجمة
    """
    translations = load_translations()

    # إذا كان المفتاح موجودًا واللغة متاحة
    if key in translations and lang in translations[key]:
        return translations[key][lang]

    # إذا كان المفتاح موجودًا ولكن اللغة غير متاحة، استخدم الإنجليزية
    if key in translations and 'en' in translations[key]:
        return translations[key]['en']

    # إرجاع المفتاح نفسه إذا لم يتم العثور على ترجمة
    return key

def get_all_keys():
    """
    الحصول على جميع مفاتيح الترجمة.

    Returns:
        list: قائمة بجميع مفاتيح الترجمة
    """
    translations = load_translations()
    return list(translations.keys())

def add_translation(key, en_text, am_text=''):
    """
    إضافة ترجمة جديدة أو تحديث ترجمة موجودة.

    Args:
        key (str): مفتاح الترجمة
        en_text (str): النص الإنجليزي
        am_text (str): النص الأمازيغي (اختياري)

    Returns:
        tuple: (bool, str) - (حالة النجاح، الرسالة)
    """
    translations = load_translations()

    # التحقق من وجود المفتاح
    is_update = key in translations

    # إضافة أو تحديث الترجمة
    translations[key] = {
        'en': en_text,
        'am': am_text
    }

    success = save_translations(translations)

    if success:
        if is_update:
            return True, f"تم تحديث الترجمة لـ '{key}' بنجاح"
        else:
            return True, f"تمت إضافة ترجمة جديدة لـ '{key}' بنجاح"
    else:
        return False, "فشل في حفظ الترجمة"

def update_translation(key, am_text):
    """
    تحديث ترجمة موجودة (النص الأمازيغي فقط).

    Args:
        key (str): مفتاح الترجمة
        am_text (str): النص الأمازيغي الجديد

    Returns:
        tuple: (bool, str) - (حالة النجاح، الرسالة)
    """
    translations = load_translations()

    # التحقق من وجود المفتاح
    if key not in translations:
        return False, f"المفتاح '{key}' غير موجود"

    # الاحتفاظ بالنص الإنجليزي وتحديث النص الأمازيغي فقط
    en_text = translations[key].get('en', '')
    translations[key] = {
        'en': en_text,
        'am': am_text
    }

    success = save_translations(translations)

    if success:
        return True, f"تم تحديث الترجمة لـ '{key}' بنجاح"
    else:
        return False, "فشل في حفظ الترجمة"

def delete_translation(key):
    """
    حذف ترجمة.

    Args:
        key (str): مفتاح الترجمة المراد حذفها

    Returns:
        tuple: (bool, str) - (حالة النجاح، الرسالة)
    """
    translations = load_translations()

    if key in translations:
        del translations[key]
        success = save_translations(translations)
        if success:
            return True, f"تم حذف الترجمة لـ '{key}' بنجاح"
        else:
            return False, "فشل في حفظ التغييرات بعد الحذف"
    else:
        return False, f"مفتاح الترجمة '{key}' غير موجود"

def get_translation_statistics():
    """
    الحصول على إحصائيات الترجمة.

    Returns:
        dict: قاموس يحتوي على إحصائيات الترجمة
    """
    try:
        # تحميل الترجمات الحالية
        translations = load_translations()

        # عدد الترجمات الإجمالي
        total = len(translations)

        # عدد النصوص المترجمة (ترجمات أمازيغية غير فارغة)
        translated = sum(1 for _, value in translations.items() if value.get("am") and value.get("am").strip())

        # حساب النسبة المئوية
        percentage = round((translated / total) * 100) if total > 0 else 0

        return {
            "total": total,
            "translated": translated,
            "untranslated": total - translated,
            "percentage": percentage
        }
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات الترجمة: {str(e)}")
        return {
            "total": 0,
            "translated": 0,
            "untranslated": 0,
            "percentage": 0
        }

def extract_i18n_keys_from_html(html_file):
    """
    استخراج مفاتيح data-i18n من ملف HTML.

    Args:
        html_file (str): مسار ملف HTML

    Returns:
        dict: قاموس يحتوي على مفاتيح data-i18n والنصوص المقابلة لها
    """
    i18n_keys = {}

    try:
        # تجاهل ملفات الإدارة
        if '/admin/' in html_file or '\\admin\\' in html_file or 'admin_' in html_file or 'admin.' in html_file:
            logger.info(f"تجاهل ملف الإدارة: {html_file}")
            return i18n_keys

        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        soup = BeautifulSoup(content, 'html.parser')

        # استخراج النصوص من سمة data-i18n
        for element in soup.find_all(attrs={'data-i18n': True}):
            key = element['data-i18n']

            # تجاهل المفاتيح الفارغة
            if not key or key.strip() == '':
                continue

            # الحصول على النص داخل العنصر مع الحفاظ على علامات HTML
            text = element.decode_contents().strip()
            if text:
                # تجاهل متغيرات القوالب
                if '{{' in text or '{%' in text:
                    continue

                # إضافة المفتاح والنص إلى القاموس
                i18n_keys[key] = text
                logger.info(f"تم استخراج مفتاح data-i18n: {key} = {text}")

    except Exception as e:
        logger.error(f"خطأ في استخراج مفاتيح data-i18n من ملف HTML: {str(e)}")

    return i18n_keys

def extract_i18n_keys_from_js(js_file):
    """
    استخراج مفاتيح data-i18n من ملف JavaScript.

    Args:
        js_file (str): مسار ملف JavaScript

    Returns:
        dict: قاموس يحتوي على مفاتيح data-i18n والنصوص المقابلة لها
    """
    i18n_keys = {}

    try:
        # تجاهل ملفات الإدارة
        if '/admin/' in js_file or '\\admin\\' in js_file or 'admin_' in js_file or 'admin.' in js_file:
            logger.info(f"تجاهل ملف الإدارة: {js_file}")
            return i18n_keys

        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # استخراج استدعاءات دالة الترجمة
        # البحث عن استدعاءات window.__("key") أو __("key")
        translation_calls = re.findall(r'(?:window\.)?__\([\'"]([^\'"]+)[\'"]\)', content)
        for key in translation_calls:
            if key and '.' in key:  # التأكد من أن المفتاح يحتوي على نقطة (section.key)
                i18n_keys[key] = key  # استخدام المفتاح نفسه كنص افتراضي
                logger.info(f"تم استخراج مفتاح ترجمة من JavaScript: {key}")

        # البحث عن سمات data-i18n في HTML المضمن في JavaScript
        data_i18n_attrs = re.findall(r'data-i18n=[\'"]([^\'"]+)[\'"]', content)
        for key in data_i18n_attrs:
            if key and '.' in key:  # التأكد من أن المفتاح يحتوي على نقطة (section.key)
                i18n_keys[key] = key  # استخدام المفتاح نفسه كنص افتراضي
                logger.info(f"تم استخراج مفتاح data-i18n من HTML مضمن في JavaScript: {key}")

    except Exception as e:
        logger.error(f"خطأ في استخراج مفاتيح data-i18n من ملف JavaScript: {str(e)}")

    return i18n_keys

def extract_and_add_i18n_keys(directory):
    """
    استخراج مفاتيح data-i18n من الملفات وإضافتها إلى ملف الترجمة.

    Args:
        directory (str): المجلد الذي يحتوي على الملفات

    Returns:
        tuple: (int, int) - (عدد المفاتيح المستخرجة، عدد المفاتيح المضافة)
    """
    i18n_keys = {}
    added_count = 0
    processed_files = 0

    logger.info(f"بدء استخراج مفاتيح data-i18n من المجلد: {directory}")

    # استخراج مفاتيح data-i18n من ملفات HTML و JavaScript
    for root, _, files in os.walk(directory):
        # تجاهل مجلدات الإدارة
        if '/admin/' in root or '\\admin\\' in root:
            logger.info(f"تجاهل مجلد الإدارة: {root}")
            continue

        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                logger.info(f"استخراج مفاتيح data-i18n من ملف HTML: {file_path}")
                file_keys = extract_i18n_keys_from_html(file_path)
                i18n_keys.update(file_keys)
                logger.info(f"تم استخراج {len(file_keys)} مفتاح من {file_path}")
                processed_files += 1
            elif file.endswith('.js'):
                file_path = os.path.join(root, file)
                logger.info(f"استخراج مفاتيح data-i18n من ملف JavaScript: {file_path}")
                file_keys = extract_i18n_keys_from_js(file_path)
                i18n_keys.update(file_keys)
                logger.info(f"تم استخراج {len(file_keys)} مفتاح من {file_path}")
                processed_files += 1

    logger.info(f"تم معالجة {processed_files} ملف")
    logger.info(f"تم استخراج {len(i18n_keys)} مفتاح data-i18n")

    # تحميل الترجمات الحالية
    translations = load_translations()
    logger.info(f"تم تحميل {len(translations)} ترجمة حالية")

    # إضافة مفاتيح data-i18n إلى ملف الترجمة
    for key, text in i18n_keys.items():
        if key not in translations:
            translations[key] = {
                'en': text,
                'am': ''  # ترجمة أمازيغية فارغة
            }
            added_count += 1
            logger.info(f"تمت إضافة مفتاح data-i18n: {key} = {text}")
        else:
            # تحديث النص الإنجليزي إذا كان مختلفًا
            if translations[key].get('en') != text and text != key:
                translations[key]['en'] = text
                logger.info(f"تم تحديث النص الإنجليزي لمفتاح data-i18n: {key} = {text}")

    # حفظ الترجمات
    save_translations(translations)
    logger.info(f"تم حفظ {len(translations)} ترجمة")

    return len(i18n_keys), added_count
