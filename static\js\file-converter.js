// File conversion process management and progress bar display

// Function to display toast messages
function showToast(message, type = 'info', duration = 5000) {
    // التحقق من وجود كائن toast العام
    if (window.toast && typeof window.toast.show === 'function') {
        // استخدام نظام التنبيهات المتقدم إذا كان متاحًا
        window.toast.show({
            type: type,
            message: message,
            duration: duration
        });
    } else {
        // إنشاء عنصر تنبيه بسيط إذا لم يكن نظام التنبيهات المتقدم متاحًا
        const toastElement = document.createElement('div');
        toastElement.className = `simple-toast toast-${type}`;
        toastElement.textContent = message;

        // إضافة أنماط CSS للتنبيه
        toastElement.style.position = 'fixed';
        toastElement.style.bottom = '20px';
        toastElement.style.right = '20px';
        toastElement.style.padding = '10px 15px';
        toastElement.style.borderRadius = '4px';
        toastElement.style.color = '#fff';
        toastElement.style.zIndex = '9999';
        toastElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';

        // تعيين لون الخلفية حسب النوع
        switch (type) {
            case 'success':
                toastElement.style.backgroundColor = '#4CAF50';
                break;
            case 'error':
                toastElement.style.backgroundColor = '#f44336';
                break;
            case 'warning':
                toastElement.style.backgroundColor = '#ff9800';
                break;
            case 'info':
            default:
                toastElement.style.backgroundColor = '#2196F3';
                break;
        }

        // إضافة التنبيه إلى الصفحة
        document.body.appendChild(toastElement);

        // إزالة التنبيه بعد المدة المحددة
        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.parentNode.removeChild(toastElement);
            }
        }, duration);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const dropArea = document.getElementById('drop-area');
    const fileUpload = document.getElementById('file-upload');
    const fileInfoCard = document.getElementById('file-info-card');
    const fileUploadArea = document.querySelector('.file-upload-area');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const removeFileBtn = document.getElementById('remove-file');
    const filePreviewContainer = document.querySelector('.file-preview-container');
    const filePreviewText = document.getElementById('file-preview-text');
    const showMorePreviewBtn = document.getElementById('show-more-preview');
    const showLessPreviewBtn = document.getElementById('show-less-preview');
    const previewStatus = document.getElementById('preview-status');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('file-progress-bar');
    const progressPercentage = document.getElementById('progress-percentage');
    const convertBtnContainer = document.getElementById('convert-btn-container');
    const convertBtn = document.getElementById('convert-btn');
    const resultContainer = document.getElementById('result-container');
    const downloadBtn = document.getElementById('download-btn');

    // State variables
    let selectedFile = null;
    let previewLines = 20;
    let maxPreviewLines = 100;
    const maxFileSize = 10 * 1024 * 1024; // 10 MB
    let fileContent = null;

    // Events - Add checks to ensure elements exist before adding event listeners
    if (dropArea) {
        dropArea.addEventListener('dragover', handleDragOver);
        dropArea.addEventListener('dragleave', handleDragLeave);
        dropArea.addEventListener('drop', handleDrop);
    }

    if (fileUpload) {
        fileUpload.addEventListener('change', handleFileSelect);
    }

    if (removeFileBtn) {
        removeFileBtn.addEventListener('click', resetFileSelection);
    }

    const selectFileBtn = document.getElementById('select-file-btn');
    if (selectFileBtn) {
        selectFileBtn.addEventListener('click', () => fileUpload.click());
    }

    if (showMorePreviewBtn) {
        showMorePreviewBtn.addEventListener('click', showMorePreview);
    }

    if (showLessPreviewBtn) {
        showLessPreviewBtn.addEventListener('click', showLessPreview);
    }

    if (convertBtn) {
        convertBtn.addEventListener('click', translateFile);
        convertBtn.disabled = true;
    }

    // Handle file drag over drop area
    function handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        dropArea.classList.add('dragover');
    }

    // Handle drag leave from drop area
    function handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        dropArea.classList.remove('dragover');
    }

    // Handle file drop
    function handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        dropArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    }

    // Handle file selection
    function handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    }

    // Handle the selected file
    function handleFile(file) {
        // Check if file format is supported
        const fileExt = file.name.split('.').pop().toLowerCase();
        if (fileExt !== 'txt' && fileExt !== 'docx' && fileExt !== 'doc') {
            showToast('Error: Unsupported file format. Please select a .txt, .docx, or .doc file.', 'error', 5000);
            return;
        }

        // Check file size
        if (file.size > maxFileSize) {
            showToast('Error: File is too large. Maximum size is 10MB.', 'error', 5000);
            return;
        }

        // Set the selected file
        selectedFile = file;

        // Display file information
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfoCard.classList.remove('d-none');
        fileUploadArea.classList.add('d-none');

        // Show convert button
        convertBtnContainer.classList.remove('d-none');
        convertBtn.disabled = false;

        // Read file content for preview (text files only)
        if (fileExt === 'txt') {
            const reader = new FileReader();
            reader.onload = function(e) {
                fileContent = e.target.result;
                showFilePreview(fileContent);
            };
            reader.onerror = function() {
                showToast('Error: Failed to read file. Please try again.', 'error', 5000);
            };
            reader.readAsText(file);
        } else {
            filePreviewContainer.classList.add('d-none');
        }
    }

    // Show file preview
    function showFilePreview(content) {
        if (!content) return;

        const lines = content.split('\n');
        const linesToShow = Math.min(previewLines, lines.length);
        filePreviewText.textContent = lines.slice(0, linesToShow).join('\n');

        filePreviewContainer.classList.remove('d-none');
        updatePreviewStatus(linesToShow, lines.length);

        // تحديث أزرار المعاينة
        showMorePreviewBtn.style.display = linesToShow < lines.length && linesToShow < maxPreviewLines ? 'block' : 'none';
        showLessPreviewBtn.style.display = linesToShow > 20 ? 'block' : 'none';
    }

    // Show more preview
    function showMorePreview() {
        if (!fileContent) return;

        previewLines = Math.min(previewLines + 20, maxPreviewLines);
        showFilePreview(fileContent);
    }

    // Show less preview
    function showLessPreview() {
        if (!fileContent) return;

        previewLines = Math.max(20, previewLines - 20);
        showFilePreview(fileContent);
    }

    // Update preview status
    function updatePreviewStatus(shown, total) {
        if (shown < total) {
            previewStatus.textContent = `Showing ${shown} of ${total} lines`;
        } else {
            previewStatus.textContent = `Showing all lines (${total})`;
        }
    }

    // Reset file selection
    function resetFileSelection() {
        selectedFile = null;
        fileContent = null;
        fileUpload.value = '';
        fileInfoCard.classList.add('d-none');
        fileUploadArea.classList.remove('d-none');
        filePreviewContainer.classList.add('d-none');
        progressContainer.classList.add('d-none');
        resultContainer.classList.add('d-none');
        convertBtnContainer.classList.add('d-none');
        progressBar.style.width = '0%';
        progressPercentage.textContent = '0%';
        convertBtn.disabled = true;
        convertBtn.style.display = ''; // Make convert button visible again

        // Reset "Converting..." text
        const progressLabel = document.querySelector('.form-label span:first-child');
        if (progressLabel) {
            progressLabel.textContent = 'Converting...';
        }
    }

    // Convert the file
    function translateFile() {
        if (!selectedFile) return;

        // Show progress bar
        progressContainer.classList.remove('d-none');
        resultContainer.classList.add('d-none');
        convertBtn.disabled = true;

        // Create FormData
        const formData = new FormData();
        formData.append('file', selectedFile);

        // Set output format (same as input format)
        const fileExt = selectedFile.name.split('.').pop().toLowerCase();
        formData.append('output_format', fileExt);

        // Progress tracker
        let lastProgress = 0;
        let progressInterval = setInterval(function() {
            // Simulate conversion progress (since server doesn't send real updates)
            if (lastProgress < 90) {
                lastProgress += Math.random() * 3; // Gradual progress increase
                updateProgress(lastProgress);
            }
        }, 200);

        // Conversion request
        fetch('/api/convert-file', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // Show 95% progress when response is received
            clearInterval(progressInterval);
            updateProgress(95);

            if (!response.ok) {
                return response.text().then(text => {
                    throw new Error(`Server error: ${text || response.status}`);
                });
            }

            // Return binary file data
            return response.blob();
        })
        .then(blob => {
            // Complete progress bar
            updateProgress(100);

            // Change "Converting..." to "Converted"
            const progressLabel = document.querySelector('.form-label span:first-child');
            if (progressLabel) {
                progressLabel.textContent = 'Converted';
            }

            // Create download link
            const fileExt = selectedFile.name.split('.').pop().toLowerCase();
            const outputFilename = selectedFile.name.replace(`.${fileExt}`, `-tifinagh.${fileExt}`);

            // Set download button
            const url = URL.createObjectURL(blob);
            downloadBtn.href = url;
            downloadBtn.download = outputFilename;

            // Show result container
            resultContainer.classList.remove('d-none');

            // Hide convert button
            convertBtn.style.display = 'none';

            // Trigger automatic download
            setTimeout(() => {
                downloadBtn.click();
            }, 1000);

            // No need to show additional toast messages since we already have the success message in the UI
        })
        .catch(error => {
            clearInterval(progressInterval);
            progressContainer.classList.add('d-none');
            convertBtn.disabled = false;
            convertBtn.style.display = ''; // Make sure convert button is visible in case of error
            console.error('Error converting file:', error);

            // Use advanced error handler
            if (window.errorHandler) {
                window.errorHandler.showError(error, translateFile);
            } else {
                showToast(`Failed to convert file: ${error.message}`, 'error', 5000);
            }
        });
    }

    // Update progress bar
    function updateProgress(percent) {
        // Make movement smooth
        const smoothPercent = Math.min(100, Math.round(percent * 10) / 10);
        progressBar.style.width = `${smoothPercent}%`;
        progressPercentage.textContent = `${Math.round(smoothPercent)}%`;

        // Change progress bar color based on percentage
        if (smoothPercent < 30) {
            progressBar.style.backgroundColor = '#f44336'; // Red
        } else if (smoothPercent < 70) {
            progressBar.style.backgroundColor = '#ff9800'; // Orange
        } else {
            progressBar.style.backgroundColor = '#4CAF50'; // Green
        }
    }

    // Download file
    function downloadFile(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
        else return (bytes / 1048576).toFixed(1) + ' MB';
    }
});
