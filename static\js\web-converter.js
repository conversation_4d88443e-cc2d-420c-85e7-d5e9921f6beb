/**
 * Direct Website Latin to Tifinagh conversion functionality
 * Opens websites directly in a new tab with Tifinagh conversion
 */

(function() {
    // DOM elements
    let urlForm;
    let websiteUrl;
    let enableTifinaghCheckbox;
    let webLatinText;
    let webTifinaghText;
    let webConvertBtn;
    let webClearBtn;
    let webCopyBtn;
    let webDownloadBtn;
    let exampleUrls;
    let showAlternativeBtn;
    let alternativeConversion;

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get DOM elements
        urlForm = document.getElementById('url-form');
        websiteUrl = document.getElementById('website-url');
        enableTifinaghCheckbox = document.getElementById('enable-tifinagh');
        webLatinText = document.getElementById('web-latin-text');
        webTifinaghText = document.getElementById('web-tifinagh-text');
        webConvertBtn = document.getElementById('web-convert-btn');
        webClearBtn = document.getElementById('web-clear-btn');
        webCopyBtn = document.getElementById('web-copy-btn');
        webDownloadBtn = document.getElementById('web-download-btn');
        exampleUrls = document.querySelectorAll('.example-url');
        showAlternativeBtn = document.getElementById('show-alternative');
        alternativeConversion = document.querySelector('.alternative-conversion');

        // Initialize event listeners
        initEventListeners();
    });

    // Initialize event listeners
    function initEventListeners() {
        // URL form submission
        if (urlForm) {
            urlForm.addEventListener('submit', function(e) {
                e.preventDefault();
                openWebsiteInNewTab();
            });
        }

        // Alternative text conversion
        if (webConvertBtn && webLatinText && webTifinaghText) {
            webConvertBtn.addEventListener('click', function() {
                convertCustomText();
            });
        }

        // Clear alternative text
        if (webClearBtn && webLatinText) {
            webClearBtn.addEventListener('click', function() {
                webLatinText.value = '';
                webTifinaghText.textContent = '';
            });
        }

        // Copy converted text
        if (webCopyBtn && webTifinaghText) {
            webCopyBtn.addEventListener('click', function() {
                copyToClipboard(webTifinaghText.textContent);
            });
        }

        // Download converted text
        if (webDownloadBtn && webTifinaghText) {
            webDownloadBtn.addEventListener('click', function() {
                downloadText(webTifinaghText.textContent, 'tifinagh-text.txt');
            });
        }

        // Example URLs
        if (exampleUrls && exampleUrls.length > 0) {
            exampleUrls.forEach(function(example) {
                example.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = this.getAttribute('data-url');
                    if (websiteUrl && url) {
                        websiteUrl.value = url;
                        // Automatically open the website when clicking an example
                        openWebsiteInNewTab();
                    }
                });
            });
        }

        // Show alternative conversion
        if (showAlternativeBtn && alternativeConversion) {
            showAlternativeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                alternativeConversion.style.display = 'block';
                showAlternativeBtn.style.display = 'none';
            });
        }
    }

    // Open website in new tab with Tifinagh conversion
    function openWebsiteInNewTab() {
        if (!websiteUrl || !websiteUrl.value) return;

        let url = websiteUrl.value.trim();

        // Validate URL
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
        }

        // Always enable Tifinagh conversion
        const convertTifinagh = true;

        // Create proxy URL - properly encode all parameters
        const proxyUrl = `/api/proxy-website?url=${encodeURIComponent(url)}&convert=true`;

        // Log the URL for debugging
        console.log("Opening URL:", proxyUrl);

        // Open in new tab
        window.open(proxyUrl, '_blank');
    }

    // Convert custom text
    function convertCustomText() {
        const text = webLatinText.value;
        if (!text) return;

        // Show loading state
        if (webTifinaghText) {
            webTifinaghText.textContent = 'Converting...';
        }

        // Check if text contains table markup
        const hasTable = text.includes('<table') ||
                        text.includes('<tr') ||
                        text.includes('<td') ||
                        text.includes('<th') ||
                        (text.includes('|') &&
                         (text.includes('--') ||
                          text.includes('-:') ||
                          text.includes(':-') ||
                          (text.match(/\|/g) || []).length >= 4));

        // Call the API to convert the text
        fetch('/api/convert', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: text,
                hasTable: hasTable
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.result) {
                // Check if the result contains HTML table tags or if hasTable flag is set
                if (data.hasTable ||
                    data.result.includes('<table') ||
                    data.result.includes('<tr') ||
                    data.result.includes('<td') ||
                    data.result.includes('<th')) {
                    // If it contains HTML tables, use innerHTML instead of textContent
                    webTifinaghText.innerHTML = data.result;
                } else {
                    // Otherwise use textContent for security
                    webTifinaghText.textContent = data.result;
                }
            } else if (data.error) {
                webTifinaghText.textContent = 'Error: ' + data.error;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            webTifinaghText.textContent = 'Error: ' + error.message;
        });
    }

    // Copy text to clipboard
    function copyToClipboard(text) {
        if (!text) return;

        // Create a temporary textarea element
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        document.body.appendChild(textarea);

        // Select and copy the text
        textarea.select();
        document.execCommand('copy');

        // Remove the temporary element
        document.body.removeChild(textarea);

        // Show feedback
        if (webCopyBtn) {
            const originalHTML = webCopyBtn.innerHTML;
            webCopyBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                    <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                </svg>`;
            webCopyBtn.style.color = '#28a745';

            setTimeout(function() {
                webCopyBtn.innerHTML = originalHTML;
                webCopyBtn.style.color = '';
            }, 2000);
        }
    }

    // Download text as file
    function downloadText(text, filename) {
        if (!text) return;

        const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;

        document.body.appendChild(a);
        a.click();

        // Clean up
        setTimeout(function() {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // Show feedback
            if (webDownloadBtn) {
                const originalHTML = webDownloadBtn.innerHTML;
                webDownloadBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                    </svg>`;
                webDownloadBtn.style.color = '#28a745';

                setTimeout(function() {
                    webDownloadBtn.innerHTML = originalHTML;
                    webDownloadBtn.style.color = '';
                }, 2000);
            }
        }, 100);
    }
})();
