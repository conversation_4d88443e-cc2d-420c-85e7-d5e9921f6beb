{% extends 'admin/base.html' %}

{% block title %}Tools & Diagnostics - Admin Panel{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">Tools & Diagnostics</li>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3">Tools & Diagnostics</h1>
        <p class="text-muted">Monitor system health and perform maintenance tasks.</p>
    </div>
</div>

<div class="row">
    <!-- System Information -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">System Information</h5>
            </div>
            <div class="card-body">
                <table class="table">
                    <tbody>
                        <tr>
                            <th scope="row">Operating System</th>
                            <td>{{ system_info.os }}</td>
                        </tr>
                        <tr>
                            <th scope="row">Python Version</th>
                            <td>{{ system_info.python_version }}</td>
                        </tr>
                        <tr>
                            <th scope="row">Flask Version</th>
                            <td>{{ system_info.flask_version }}</td>
                        </tr>
                        <tr>
                            <th scope="row">Server Hostname</th>
                            <td>{{ system_info.hostname }}</td>
                        </tr>
                        <tr>
                            <th scope="row">Server Time</th>
                            <td>{{ system_info.server_time }}</td>
                        </tr>
                        <tr>
                            <th scope="row">Uptime</th>
                            <td>{{ system_info.uptime }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-outline-secondary" id="refreshSystemInfo">
                    <i class="bi bi-arrow-repeat me-1"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Library Dependencies -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">Library Dependencies</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Library</th>
                                <th>Version</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for lib in dependencies %}
                            <tr>
                                <td>{{ lib.name }}</td>
                                <td>{{ lib.version }}</td>
                                <td>
                                    {% if lib.installed %}
                                    <span class="badge bg-success">Installed</span>
                                    {% else %}
                                    <span class="badge bg-danger">Missing</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-outline-primary" id="installMissing">
                    <i class="bi bi-download me-1"></i> Install Missing Dependencies
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Error Logs -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Error Logs</h5>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshLogs">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" id="clearLogs" data-bs-toggle="modal" data-bs-target="#clearLogsModal">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="log-container" style="max-height: 400px; overflow-y: auto;">
                    {% if error_logs %}
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Level</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in error_logs %}
                            <tr>
                                <td class="text-nowrap">{{ log.time }}</td>
                                <td>
                                    <span class="badge bg-{{ log.level_color }}">{{ log.level }}</span>
                                </td>
                                <td>{{ log.message }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <div class="text-center py-4 text-muted">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                        <p class="mt-2">No error logs found.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Maintenance Tools -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Maintenance Tools</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="cacheBtn">
                        <div>
                            <h6 class="mb-1">Clear Cache</h6>
                            <p class="mb-0 text-muted small">Clear all cached files to refresh application data</p>
                        </div>
                        <i class="bi bi-trash text-danger"></i>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="backupBtn">
                        <div>
                            <h6 class="mb-1">Create Backup</h6>
                            <p class="mb-0 text-muted small">Create a backup of all data files</p>
                        </div>
                        <i class="bi bi-download text-primary"></i>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="optimizeBtn">
                        <div>
                            <h6 class="mb-1">Optimize Database</h6>
                            <p class="mb-0 text-muted small">Optimize and cleanup data files</p>
                        </div>
                        <i class="bi bi-speedometer text-success"></i>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="testEmailBtn">
                        <div>
                            <h6 class="mb-1">Test Email Configuration</h6>
                            <p class="mb-0 text-muted small">Send a test email to verify email configuration</p>
                        </div>
                        <i class="bi bi-envelope text-info"></i>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center" id="restartBtn" data-bs-toggle="modal" data-bs-target="#restartModal">
                        <div>
                            <h6 class="mb-1">Restart Application</h6>
                            <p class="mb-0 text-muted small">Restart the application to apply changes</p>
                        </div>
                        <i class="bi bi-arrow-clockwise text-warning"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal for Clear Logs -->
<div class="modal fade" id="clearLogsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Clear Error Logs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Warning!</strong> This action will permanently delete all error logs.
                </div>
                <p>Are you sure you want to continue?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmClearLogs">Clear Logs</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal for Restart -->
<div class="modal fade" id="restartModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Restart Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Warning!</strong> Restarting the application will disconnect all users temporarily.
                </div>
                <p>Are you sure you want to restart the application?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmRestart">Restart</button>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5 id="progressTitle">Processing...</h5>
                <p class="text-muted" id="progressMessage">Please wait while the operation completes.</p>
                <div class="progress mt-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));

        // System Info refresh
        document.getElementById('refreshSystemInfo').addEventListener('click', function() {
            fetch('{{ url_for("admin.admin_refresh_system_info") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error refreshing system information: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while refreshing system information');
                });
        });

        // Install missing dependencies
        document.getElementById('installMissing').addEventListener('click', function() {
            document.getElementById('progressTitle').textContent = 'Installing Dependencies';
            document.getElementById('progressMessage').textContent = 'Please wait while missing dependencies are installed...';
            document.querySelector('#progressModal .progress-bar').style.width = '0%';
            progressModal.show();

            fetch('{{ url_for("admin.admin_install_dependencies") }}')
                .then(response => response.json())
                .then(data => {
                    progressModal.hide();
                    if (data.success) {
                        alert('Dependencies installed successfully');
                        location.reload();
                    } else {
                        alert('Error installing dependencies: ' + data.message);
                    }
                })
                .catch(error => {
                    progressModal.hide();
                    console.error('Error:', error);
                    alert('An error occurred while installing dependencies');
                });
        });

        // Refresh logs
        document.getElementById('refreshLogs').addEventListener('click', function() {
            fetch('{{ url_for("admin.admin_refresh_logs") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error refreshing logs: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while refreshing logs');
                });
        });

        // Clear logs
        document.getElementById('confirmClearLogs').addEventListener('click', function() {
            fetch('{{ url_for("admin.admin_clear_error_logs") }}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error clearing logs: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while clearing logs');
                });
        });

        // Clear cache
        document.getElementById('cacheBtn').addEventListener('click', function() {
            document.getElementById('progressTitle').textContent = 'Clearing Cache';
            document.getElementById('progressMessage').textContent = 'Please wait while the cache is being cleared...';
            document.querySelector('#progressModal .progress-bar').style.width = '0%';
            progressModal.show();

            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                document.querySelector('#progressModal .progress-bar').style.width = progress + '%';
                if (progress >= 100) clearInterval(interval);
            }, 200);

            fetch('{{ url_for("admin.admin_clear_app_cache") }}')
                .then(response => response.json())
                .then(data => {
                    clearInterval(interval);
                    document.querySelector('#progressModal .progress-bar').style.width = '100%';
                    setTimeout(() => {
                        progressModal.hide();
                        if (data.success) {
                            alert('Cache cleared successfully');
                        } else {
                            alert('Error clearing cache: ' + data.message);
                        }
                    }, 500);
                })
                .catch(error => {
                    clearInterval(interval);
                    progressModal.hide();
                    console.error('Error:', error);
                    alert('An error occurred while clearing cache');
                });
        });

        // Create backup
        document.getElementById('backupBtn').addEventListener('click', function() {
            document.getElementById('progressTitle').textContent = 'Creating Backup';
            document.getElementById('progressMessage').textContent = 'Please wait while a backup is being created...';
            document.querySelector('#progressModal .progress-bar').style.width = '0%';
            progressModal.show();

            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                document.querySelector('#progressModal .progress-bar').style.width = progress + '%';
                if (progress >= 100) clearInterval(interval);
            }, 300);

            fetch('{{ url_for("admin.admin_create_backup_file") }}')
                .then(response => response.json())
                .then(data => {
                    clearInterval(interval);
                    document.querySelector('#progressModal .progress-bar').style.width = '100%';
                    setTimeout(() => {
                        progressModal.hide();
                        if (data.success) {
                            window.location.href = data.download_url;
                            alert('Backup created successfully');
                        } else {
                            alert('Error creating backup: ' + data.message);
                        }
                    }, 500);
                })
                .catch(error => {
                    clearInterval(interval);
                    progressModal.hide();
                    console.error('Error:', error);
                    alert('An error occurred while creating backup');
                });
        });

        // Optimize database
        document.getElementById('optimizeBtn').addEventListener('click', function() {
            document.getElementById('progressTitle').textContent = 'Optimizing Database';
            document.getElementById('progressMessage').textContent = 'Please wait while the database is being optimized...';
            document.querySelector('#progressModal .progress-bar').style.width = '0%';
            progressModal.show();

            let progress = 0;
            const interval = setInterval(() => {
                progress += 7;
                document.querySelector('#progressModal .progress-bar').style.width = progress + '%';
                if (progress >= 100) clearInterval(interval);
            }, 250);

            fetch('{{ url_for("admin.admin_optimize_database_files") }}')
                .then(response => response.json())
                .then(data => {
                    clearInterval(interval);
                    document.querySelector('#progressModal .progress-bar').style.width = '100%';
                    setTimeout(() => {
                        progressModal.hide();
                        if (data.success) {
                            alert('Database optimized successfully');
                        } else {
                            alert('Error optimizing database: ' + data.message);
                        }
                    }, 500);
                })
                .catch(error => {
                    clearInterval(interval);
                    progressModal.hide();
                    console.error('Error:', error);
                    alert('An error occurred while optimizing database');
                });
        });

        // Test email
        document.getElementById('testEmailBtn').addEventListener('click', function() {
            const email = prompt('Enter email address to send test message to:');
            if (!email) return;

            document.getElementById('progressTitle').textContent = 'Sending Test Email';
            document.getElementById('progressMessage').textContent = 'Please wait while a test email is being sent...';
            document.querySelector('#progressModal .progress-bar').style.width = '50%';
            progressModal.show();

            fetch('{{ url_for("admin.admin_send_test_email") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                document.querySelector('#progressModal .progress-bar').style.width = '100%';
                setTimeout(() => {
                    progressModal.hide();
                    if (data.success) {
                        alert('Test email sent successfully to ' + email);
                    } else {
                        alert('Error sending test email: ' + data.message);
                    }
                }, 500);
            })
            .catch(error => {
                progressModal.hide();
                console.error('Error:', error);
                alert('An error occurred while sending test email');
            });
        });

        // Restart application
        document.getElementById('confirmRestart').addEventListener('click', function() {
            document.getElementById('progressTitle').textContent = 'Restarting Application';
            document.getElementById('progressMessage').textContent = 'Please wait while the application restarts...';
            document.querySelector('#progressModal .progress-bar').style.width = '0%';
            progressModal.show();

            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                document.querySelector('#progressModal .progress-bar').style.width = progress + '%';
                if (progress >= 100) clearInterval(interval);
            }, 500);

            fetch('{{ url_for("admin.admin_restart_application") }}')
                .then(response => response.json())
                .then(data => {
                    clearInterval(interval);
                    document.querySelector('#progressModal .progress-bar').style.width = '100%';
                    setTimeout(() => {
                        progressModal.hide();
                        if (data.success) {
                            alert('Application restarted successfully. The page will reload in a few seconds.');
                            setTimeout(() => location.reload(), 5000);
                        } else {
                            alert('Error restarting application: ' + data.message);
                        }
                    }, 500);
                })
                .catch(error => {
                    clearInterval(interval);
                    progressModal.hide();
                    console.error('Error:', error);
                    alert('An error occurred while restarting the application');
                });
        });
    });
</script>
{% endblock %}