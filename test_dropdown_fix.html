<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Z-Index Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .test-box { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .result { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .big-button { font-size: 18px; padding: 15px 30px; }
        .z-index-test { position: relative; background: #ff6b6b; color: white; padding: 20px; margin: 10px 0; z-index: 5000; }
        .overlay-test { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.3); z-index: 8000; display: none; }
        .high-z-element { position: relative; background: #4ecdc4; color: white; padding: 15px; margin: 10px 0; z-index: 7000; }
    </style>
</head>
<body>
    <h1>🔧 Dropdown Z-Index Fix Test</h1>
    <p>This page tests the language dropdown z-index fix to ensure it appears above all other elements.</p>

    <div class="test-box">
        <h2>1. Z-Index Test Elements</h2>
        <p>These elements have high z-index values to test if the dropdown appears above them:</p>
        
        <div class="z-index-test">
            <strong>High Z-Index Element (z-index: 5000)</strong><br>
            The language dropdown should appear above this red element.
        </div>
        
        <div class="high-z-element">
            <strong>Another High Z-Index Element (z-index: 7000)</strong><br>
            The language dropdown should appear above this teal element too.
        </div>
        
        <button onclick="showOverlay()" class="big-button">Show Overlay (z-index: 8000)</button>
        <button onclick="hideOverlay()">Hide Overlay</button>
        
        <div id="overlay" class="overlay-test">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; color: black; padding: 20px; border-radius: 8px;">
                <h3>Overlay Test (z-index: 8000)</h3>
                <p>The language dropdown should appear above this overlay!</p>
                <button onclick="hideOverlay()">Close Overlay</button>
            </div>
        </div>
    </div>

    <div class="test-box">
        <h2>2. Dropdown CSS Check</h2>
        <div id="css-check" class="result info">Checking CSS...</div>
        <button onclick="checkDropdownCSS()">Check Dropdown CSS</button>
    </div>

    <div class="test-box">
        <h2>3. Visual Test Instructions</h2>
        <div class="result info">
            <strong>Manual Test Steps:</strong><br>
            1. Click the language dropdown button in the top navigation<br>
            2. Verify the dropdown appears above all colored elements on this page<br>
            3. Try clicking "Show Overlay" and then open the language dropdown<br>
            4. The dropdown should appear above the dark overlay<br>
            5. Test on mobile by resizing the browser window<br>
            6. Check that the dropdown doesn't get cut off at screen edges
        </div>
    </div>

    <div class="test-box">
        <h2>4. Open Main Application</h2>
        <button onclick="openMainApp()" class="big-button">Open Main App</button>
        <p>Test the language dropdown in the actual application environment.</p>
    </div>

    <div class="test-box">
        <h2>5. Responsive Test</h2>
        <button onclick="testResponsive()">Test Responsive Behavior</button>
        <div id="responsive-test" class="result info">Click to test responsive behavior</div>
    </div>

    <script>
        function showOverlay() {
            document.getElementById('overlay').style.display = 'block';
            console.log('Overlay shown - test language dropdown now');
        }
        
        function hideOverlay() {
            document.getElementById('overlay').style.display = 'none';
        }
        
        function checkDropdownCSS() {
            const result = document.getElementById('css-check');
            let messages = [];
            
            // Check if dropdown fix CSS is loaded
            const stylesheets = Array.from(document.styleSheets);
            const dropdownFixCSS = stylesheets.find(sheet => 
                sheet.href && sheet.href.includes('dropdown-z-index-fix.css')
            );
            
            if (dropdownFixCSS) {
                messages.push('✅ Dropdown Z-Index Fix CSS is loaded');
                console.log('Dropdown fix CSS found:', dropdownFixCSS.href);
            } else {
                messages.push('❌ Dropdown Z-Index Fix CSS not found');
            }
            
            // Check for language selector elements
            const languageSelector = document.querySelector('.language-selector');
            if (languageSelector) {
                messages.push('✅ Language selector found');
                
                const computedStyle = getComputedStyle(languageSelector);
                const zIndex = computedStyle.zIndex;
                messages.push(`Language selector z-index: ${zIndex}`);
                
                const dropdown = languageSelector.querySelector('.dropdown-menu');
                if (dropdown) {
                    messages.push('✅ Dropdown menu found');
                    
                    const dropdownStyle = getComputedStyle(dropdown);
                    const dropdownZIndex = dropdownStyle.zIndex;
                    messages.push(`Dropdown menu z-index: ${dropdownZIndex}`);
                    
                    if (parseInt(dropdownZIndex) >= 10000) {
                        messages.push('✅ Dropdown has high z-index (≥10000)');
                    } else {
                        messages.push('⚠️ Dropdown z-index might be too low');
                    }
                } else {
                    messages.push('❌ Dropdown menu not found');
                }
            } else {
                messages.push('❌ Language selector not found');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = messages.some(m => m.includes('❌')) ? 'result error' : 'result success';
        }
        
        function openMainApp() {
            window.open('http://127.0.0.1:5001/?tab=text-converter', '_blank');
        }
        
        function testResponsive() {
            const result = document.getElementById('responsive-test');
            let messages = [];
            
            const viewport = {
                width: window.innerWidth,
                height: window.innerHeight
            };
            
            messages.push(`Current viewport: ${viewport.width}x${viewport.height}`);
            
            if (viewport.width <= 576) {
                messages.push('📱 Extra small screen detected');
                messages.push('Check: Dropdown should be compact and not overflow');
            } else if (viewport.width <= 768) {
                messages.push('📱 Small screen detected');
                messages.push('Check: Dropdown should adjust for mobile');
            } else if (viewport.width <= 992) {
                messages.push('💻 Medium screen detected');
                messages.push('Check: Dropdown should work normally');
            } else {
                messages.push('🖥️ Large screen detected');
                messages.push('Check: Dropdown should have full styling');
            }
            
            // Test media queries
            const mediaQueries = [
                '(max-width: 576px)',
                '(max-width: 768px)',
                '(max-width: 992px)',
                '(prefers-reduced-motion: reduce)',
                '(prefers-contrast: high)'
            ];
            
            mediaQueries.forEach(query => {
                if (window.matchMedia(query).matches) {
                    messages.push(`✅ Media query matches: ${query}`);
                }
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result info';
        }
        
        // Auto-run CSS check on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkDropdownCSS, 1000);
        });
        
        // Monitor window resize
        window.addEventListener('resize', function() {
            console.log('Window resized to:', window.innerWidth, 'x', window.innerHeight);
        });
        
        // Close overlay on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                hideOverlay();
            }
        });
        
        console.log('Dropdown Z-Index Fix Test page loaded');
    </script>
</body>
</html>
