/**
 * Document Editor for Advanced Text Converter
 *
 * This script initializes CKEditor 5 in Document Editor mode
 * and handles the conversion of complex documents with tables.
 */

let documentEditor = null;

document.addEventListener('DOMContentLoaded', function() {
    // Check if advanced converter tab is active on page load
    const advancedTab = document.querySelector('[data-tab="advanced-converter"]');
    const advancedContent = document.getElementById('advanced-converter');

    // Initialize immediately if the advanced tab is active
    if (advancedContent && advancedContent.classList.contains('active')) {
        console.log('Advanced tab is active on page load, initializing editor...');
        setTimeout(initDocumentEditor, 100); // Small delay to ensure DOM is ready
    }

    // Initialize the editor when the advanced converter tab is shown
    if (advancedTab) {
        advancedTab.addEventListener('click', function() {
            console.log('Advanced tab clicked');
            if (!documentEditor) {
                initDocumentEditor();
            }
        });
    }

    // Add event listener for convert button
    const convertBtn = document.getElementById('convert-document-btn');
    if (convertBtn) {
        convertBtn.addEventListener('click', convertDocument);
    }

    // Add event listener for copy button
    const copyBtn = document.getElementById('copy-document-btn');
    if (copyBtn) {
        copyBtn.addEventListener('click', copyDocumentContent);
    }

    // Add event listener for clear button
    const clearBtn = document.getElementById('clear-document-btn');
    if (clearBtn) {
        clearBtn.addEventListener('click', clearDocumentContent);
    }
});

/**
 * Initialize the Document Editor
 */
function initDocumentEditor() {
    // Check if editor is already initialized
    if (documentEditor) {
        return;
    }

    // Initialize CKEditor 5 in Document Editor mode
    DecoupledEditor
        .create(document.querySelector('#document-editor-content'), {
            // Editor configuration
            toolbar: [
                'heading', '|',
                'bold', 'italic', 'underline', 'strikethrough', '|',
                'fontSize', 'fontFamily', 'fontColor', 'fontBackgroundColor', '|',
                'alignment', 'indent', 'outdent', '|',
                'bulletedList', 'numberedList', '|',
                'link', 'blockQuote', 'insertTable', 'mediaEmbed', '|',
                'undo', 'redo'
            ],
            // Table configuration
            table: {
                contentToolbar: [
                    'tableColumn', 'tableRow', 'mergeTableCells',
                    'tableProperties', 'tableCellProperties'
                ]
            },
            // Image configuration
            image: {
                toolbar: [
                    'imageStyle:inline',
                    'imageStyle:block',
                    'imageStyle:side',
                    '|',
                    'toggleImageCaption',
                    'imageTextAlternative'
                ]
            },
            // Font family configuration
            fontFamily: {
                options: [
                    'default',
                    'Arial, Helvetica, sans-serif',
                    'Courier New, Courier, monospace',
                    'Georgia, serif',
                    'Lucida Sans Unicode, Lucida Grande, sans-serif',
                    'Tahoma, Geneva, sans-serif',
                    'Times New Roman, Times, serif',
                    'Trebuchet MS, Helvetica, sans-serif',
                    'Verdana, Geneva, sans-serif',
                    'Noto Sans Tifinagh, Arial, sans-serif',
                    'Noto Sans Arabic, Arial, sans-serif'
                ],
                supportAllValues: true
            },
            // Language configuration
            language: 'en'
        })
        .then(editor => {
            // Store editor instance
            documentEditor = editor;

            // Get the toolbar container
            const toolbarContainer = document.getElementById('document-editor-toolbar');

            // Add the toolbar to the container
            toolbarContainer.appendChild(editor.ui.view.toolbar.element);

            // Log success
            console.log('Document Editor initialized successfully');

            // Add event listener for content changes to update character count
            editor.model.document.on('change:data', () => {
                updateCharacterCount();
            });

            // Initial character count update
            updateCharacterCount();
        })
        .catch(error => {
            console.error('Error initializing Document Editor:', error);
        });
}

/**
 * Convert document content to Tifinagh
 */
function convertDocument() {
    // Check if editor is initialized
    if (!documentEditor) {
        console.error('Document Editor not initialized');
        return;
    }

    // Get editor content
    const content = documentEditor.getData();

    // Create a temporary element to extract text from tables and other elements
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Find all text nodes that need to be converted
    const textNodes = [];
    const tables = tempDiv.querySelectorAll('table');

    // Process tables separately
    if (tables.length > 0) {
        // Process each table
        tables.forEach((table, tableIndex) => {
            // Process cells in the table
            const cells = table.querySelectorAll('td, th');
            cells.forEach((cell, cellIndex) => {
                // Add a data attribute to identify the cell
                const cellId = `table-${tableIndex}-cell-${cellIndex}`;
                cell.setAttribute('data-cell-id', cellId);

                // Add cell to conversion list
                textNodes.push({
                    id: cellId,
                    text: cell.textContent.trim(),
                    type: 'cell'
                });
            });
        });
    }

    // Process other text elements
    const paragraphs = tempDiv.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, blockquote');
    paragraphs.forEach((paragraph, index) => {
        // Skip if inside a table
        if (paragraph.closest('table')) {
            return;
        }

        // Add a data attribute to identify the paragraph
        const paraId = `para-${index}`;
        paragraph.setAttribute('data-para-id', paraId);

        // Add paragraph to conversion list
        textNodes.push({
            id: paraId,
            text: paragraph.textContent.trim(),
            type: 'paragraph'
        });
    });

    // Show loading state
    const convertBtn = document.getElementById('convert-document-btn');
    if (convertBtn) {
        // Store original SVG
        const originalSvg = convertBtn.innerHTML;

        // Replace with loading spinner
        convertBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-repeat spin" viewBox="0 0 16 16">
                <path d="M11.534 7h3.932a.25.25 0 0 1 .192.41l-1.966 2.36a.25.25 0 0 1-.384 0l-1.966-2.36a.25.25 0 0 1 .192-.41zm-11 2h3.932a.25.25 0 0 0 .192-.41L2.692 6.23a.25.25 0 0 0-.384 0L.342 8.59A.25.25 0 0 0 .534 9z"/>
                <path fill-rule="evenodd" d="M8 3c-1.552 0-2.94.707-3.857 1.818a.5.5 0 1 1-.771-.636A6.002 6.002 0 0 1 13.917 7H12.9A5.002 5.002 0 0 0 8 3zM3.1 9a5.002 5.002 0 0 0 8.757 2.182.5.5 0 1 1 .771.636A6.002 6.002 0 0 1 2.083 9H3.1z"/>
            </svg>
            <span>Converting...</span>`;
        convertBtn.disabled = true;

        // Store the original SVG for later restoration
        convertBtn.dataset.originalSvg = originalSvg;
    }

    // Send data to server for conversion
    fetch('/api/convert-document', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ nodes: textNodes })
    })
    .then(response => response.json())
    .then(data => {
        // Update content with converted text
        data.nodes.forEach(node => {
            if (node.type === 'cell') {
                const cellElement = tempDiv.querySelector(`[data-cell-id="${node.id}"]`);
                if (cellElement) {
                    cellElement.textContent = node.convertedText;
                }
            } else if (node.type === 'paragraph') {
                const paraElement = tempDiv.querySelector(`[data-para-id="${node.id}"]`);
                if (paraElement) {
                    paraElement.textContent = node.convertedText;
                }
            }
        });

        // Update the editor content
        documentEditor.setData(tempDiv.innerHTML);

        // Reset button state
        if (convertBtn) {
            // Restore original SVG
            if (convertBtn.dataset.originalSvg) {
                convertBtn.innerHTML = convertBtn.dataset.originalSvg;
                delete convertBtn.dataset.originalSvg;
            }
            convertBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error converting document:', error);

        // Reset button state
        if (convertBtn) {
            // Restore original SVG
            if (convertBtn.dataset.originalSvg) {
                convertBtn.innerHTML = convertBtn.dataset.originalSvg;
                delete convertBtn.dataset.originalSvg;
            }
            convertBtn.disabled = false;
        }

        // Show error message
        alert('Error converting document. Please try again.');
    });
}

/**
 * Copy document content to clipboard
 */
function copyDocumentContent() {
    // Check if editor is initialized
    if (!documentEditor) {
        console.error('Document Editor not initialized');
        return;
    }

    // Get editor content as HTML
    const content = documentEditor.getData();

    // Create a temporary textarea element to copy HTML content
    const tempTextarea = document.createElement('textarea');
    tempTextarea.value = content;
    document.body.appendChild(tempTextarea);

    // Select and copy the content
    tempTextarea.select();
    document.execCommand('copy');

    // Remove the temporary textarea
    document.body.removeChild(tempTextarea);

    // Show success state
    const copyBtn = document.getElementById('copy-document-btn');
    if (copyBtn) {
        // Store original SVG
        const originalSvg = copyBtn.innerHTML;

        // Replace with checkmark SVG
        copyBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-check-circle-fill" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
            </svg>`;
        copyBtn.classList.add('success');

        // Reset after 2 seconds
        setTimeout(() => {
            copyBtn.innerHTML = originalSvg;
            copyBtn.classList.remove('success');
        }, 2000);
    }
}

/**
 * Clear document content
 */
function clearDocumentContent() {
    // Check if editor is initialized
    if (!documentEditor) {
        console.error('Document Editor not initialized');
        return;
    }

    // Confirm before clearing
    if (confirm('Are you sure you want to clear all content?')) {
        // Clear editor content
        documentEditor.setData('');

        // Update character count
        updateCharacterCount();
    }
}

/**
 * Update character count in the document editor
 */
function updateCharacterCount() {
    // Check if editor is initialized
    if (!documentEditor) {
        return;
    }

    // Get the character count element
    const charCountElement = document.querySelector('#advanced-converter .character-count');
    if (!charCountElement) {
        return;
    }

    // Get editor content as plain text
    const content = documentEditor.getData();

    // Create a temporary element to extract text
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Get text content and count characters
    const text = tempDiv.textContent || tempDiv.innerText || '';
    const charCount = text.length;

    // Update the character count display
    charCountElement.textContent = `${charCount} characters`;
}
