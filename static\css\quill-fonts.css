/**
 * Custom fonts for Quill.js editor
 */

/* Aref Ruqaa <PERSON>ont (Default) */
@font-face {
    font-family: 'aref';
    src: url('https://fonts.gstatic.com/s/arefruqaa/v25/WwkbxPW1E165rajQKDulIIcoVQ.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
}

/* Mirza Font */
@font-face {
    font-family: 'mirza';
    src: url('https://fonts.gstatic.com/s/mirza/v15/co3ImWlikiN5EtraElU_NQ.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
}

/* Roboto Font */
@font-face {
    font-family: 'roboto';
    src: url('https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2') format('woff2');
    font-weight: normal;
    font-style: normal;
}

/* Font classes for Quill */
.ql-font-aref {
    font-family: 'aref', 'Aref Ruqaa', serif;
}

.ql-font-mirza {
    font-family: 'mirza', Arial, sans-serif;
}

.ql-font-roboto {
    font-family: 'roboto', Arial, sans-serif;
}

/* Default font styling */
.ql-editor {
    font-family: 'aref', 'Aref Ruqaa', serif;
}
