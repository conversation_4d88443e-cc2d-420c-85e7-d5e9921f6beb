import os
import logging
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Config:
    """Base configuration."""
    # Secret key for the application
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-key-for-latin-to-tifinagh')

    # Upload folder
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', os.path.join(os.getcwd(), 'uploads'))

    # Maximum content length (16 MB)
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))

    # Allowed file extensions
    ALLOWED_EXTENSIONS = {'txt', 'docx'}

    # Debug mode
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() in ('true', '1', 't')


class DevelopmentConfig(Config):
    """Development environment configuration."""
    DEBUG = True
    TESTING = False


class TestingConfig(Config):
    """Testing environment configuration."""
    DEBUG = True
    TESTING = True
    # Use a temporary folder for uploads during testing
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'test_uploads')


class ProductionConfig(Config):
    """Production environment configuration."""
    DEBUG = False
    TESTING = False


# Define configuration based on environment variable
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# Get current configuration
def get_config():
    """
    Get application configuration based on FLASK_ENV environment variable.

    Returns:
        Config: Configuration object appropriate for the current environment
    """
    env = os.environ.get('FLASK_ENV', 'default')
    return config_by_name.get(env, config_by_name['default'])
