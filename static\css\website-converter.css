/**
 * Direct Website Converter Styles
 */

/* Intro */
.web-converter-intro {
    margin-bottom: 1.5rem;
}

.web-converter-intro h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.web-converter-intro p {
    color: var(--text-light);
    line-height: 1.5;
}

/* URL Input */
.url-form {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-light);
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
}

.url-input-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.url-input-container input {
    width: 100%;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 1rem;
}

.url-input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.conversion-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.conversion-toggle input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
}

.conversion-toggle label {
    font-size: 0.875rem;
    color: var(--text-color);
}

.url-examples {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.example-url {
    color: var(--primary-color);
    text-decoration: none;
    padding: 0.35rem 0.75rem;
    border-radius: 1rem;
    background-color: rgba(var(--primary-rgb), 0.1);
    transition: all var(--transition-speed) ease;
    border: 1px solid transparent;
}

.example-url:hover {
    background-color: rgba(var(--primary-rgb), 0.15);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
    border-color: rgba(var(--primary-rgb), 0.2);
}

.example-url:active {
    transform: translateY(0);
}

.load-website-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    margin: 1rem auto;
    display: block;
    width: 50%;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.2);
}

.load-website-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.3);
}

.load-website-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(var(--primary-rgb), 0.2);
}

/* Loading State */
.website-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1rem;
    color: var(--text-light);
}

/* Preview Header */
.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.preview-url {
    font-size: 0.875rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60%;
}

.preview-actions {
    display: flex;
    gap: 0.5rem;
}

.toggle-tifinagh-btn, .open-website-btn {
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.toggle-tifinagh-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.toggle-tifinagh-btn:hover {
    background-color: var(--primary-hover);
}

.toggle-tifinagh-btn.active {
    background-color: #dc3545;
}

.open-website-btn {
    background-color: var(--bg-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.open-website-btn:hover {
    background-color: white;
}

/* Alternative Conversion */
.alternative-toggle {
    margin-top: 1rem;
    text-align: center;
}

.alternative-toggle .btn-link {
    color: var(--primary-color);
    text-decoration: none;
    background: none;
    border: none;
    padding: 0;
    font-size: 0.875rem;
    cursor: pointer;
}

.alternative-toggle .btn-link:hover {
    text-decoration: underline;
}

.alternative-conversion {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: var(--bg-light);
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
}

.alternative-conversion h3 {
    margin-bottom: 0.75rem;
    color: var(--primary-color);
    font-size: 1.25rem;
}

.alternative-conversion p {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.alternative-conversion-content {
    display: flex;
    flex-direction: column;
}

.alternative-conversion textarea {
    height: 150px;
    margin-bottom: 1rem;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    font-family: inherit;
    resize: vertical;
}

.alternative-actions {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.convert-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.convert-btn:hover {
    background-color: var(--primary-hover);
}

.alternative-actions .clear-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    cursor: pointer;
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    transition: all 0.2s ease;
}

.alternative-actions .clear-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: rgba(79, 70, 229, 0.05);
}

.result-container {
    margin-top: 1rem;
    position: relative;
}

.result-header {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 10;
    display: block;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    justify-content: flex-end;
}

.result-actions .copy-btn, .result-actions .download-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    cursor: pointer;
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    transition: all 0.2s ease;
}

.result-actions .copy-btn:hover, .result-actions .download-btn:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: rgba(79, 70, 229, 0.05);
}

.action-btn {
    background: none;
    border: none;
    padding: 0.25rem;
    cursor: pointer;
    color: var(--text-light);
    border-radius: 0.25rem;
    display: inline-block;
    vertical-align: middle;
    transition: all 0.2s;
    margin-right: 5px;
}

.action-btn:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.tifinagh-result {
    min-height: 150px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: white;
    font-family: 'Noto Sans Tifinagh', sans-serif;
}

.try-again-btn {
    display: block;
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    background-color: var(--bg-light);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.try-again-btn:hover {
    background-color: white;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .preview-url {
        max-width: 100%;
    }

    .alternative-actions {
        flex-direction: column;
    }

    .url-input-container {
        flex-direction: column;
    }

    .load-website-btn {
        padding: 0.75rem;
    }
}
