<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switching Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button { margin: 5px; padding: 10px 15px; }
        .status { font-weight: bold; font-size: 18px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
        .test-element { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>🌐 Language Switching Comprehensive Test</h1>
    
    <div class="test-section">
        <h2>1. System Status Check</h2>
        <button onclick="checkSystemStatus()">Check System Status</button>
        <div id="system-status-result" class="result">Click button to check...</div>
    </div>
    
    <div class="test-section">
        <h2>2. Translation System Test</h2>
        <button onclick="testTranslationSystem()">Test Translation System</button>
        <div id="translation-test-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>3. Language Button Detection</h2>
        <button onclick="detectLanguageButtons()">Detect Language Buttons</button>
        <div id="button-detection-result" class="result">Click button to detect...</div>
    </div>
    
    <div class="test-section">
        <h2>4. Manual Language Switch Test</h2>
        <button onclick="testLanguageSwitch('en')">Switch to English</button>
        <button onclick="testLanguageSwitch('am')">Switch to Amazigh</button>
        <button onclick="getCurrentLanguage()">Get Current Language</button>
        <div id="manual-switch-result" class="result">Click buttons to test...</div>
    </div>
    
    <div class="test-section">
        <h2>5. Event Listeners Test</h2>
        <button onclick="testEventListeners()">Test Event Listeners</button>
        <div id="event-listeners-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>6. Translation Keys Test</h2>
        <button onclick="testTranslationKeys()">Test Translation Keys</button>
        <div id="translation-keys-result" class="result">Click button to test...</div>
    </div>
    
    <div class="test-section">
        <h2>7. Live Translation Test Elements</h2>
        <div class="test-element">
            <p><strong>English Text:</strong> <span data-i18n="common.english">English</span></p>
            <p><strong>Amazigh Text:</strong> <span data-i18n="common.amazigh">ⵜⴰⵎⴰⵣⵉⵖⵜ</span></p>
            <p><strong>App Name:</strong> <span data-i18n="common.app_name">Tifinagh Converter</span></p>
            <p><strong>Language:</strong> <span data-i18n="common.language">Language</span></p>
        </div>
        <button onclick="updateTestElements()">Update Test Elements</button>
        <div id="live-test-result" class="result">Test elements above should change when language switches</div>
    </div>
    
    <div class="test-section">
        <h2>8. Debug Console</h2>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <button onclick="clearDebugInfo()">Clear Debug</button>
        <div id="debug-console" class="result">
            <pre id="debug-output">Debug information will appear here...</pre>
        </div>
    </div>

    <script>
        let debugOutput = [];
        
        function log(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugOutput.push(logEntry);
            if (data) {
                debugOutput.push(JSON.stringify(data, null, 2));
            }
            console.log(logEntry, data || '');
            updateDebugConsole();
        }
        
        function updateDebugConsole() {
            const debugElement = document.getElementById('debug-output');
            if (debugElement) {
                debugElement.textContent = debugOutput.slice(-20).join('\n');
            }
        }
        
        function checkSystemStatus() {
            const result = document.getElementById('system-status-result');
            let messages = [];
            let allGood = true;
            
            log('Checking system status...');
            
            // Check if i18n system exists
            if (typeof window.i18n !== 'undefined') {
                messages.push('✅ i18n system is loaded');
                log('i18n system found');
                
                if (window.i18n.isLoaded) {
                    messages.push('✅ i18n system is initialized');
                    log('i18n system is initialized');
                } else {
                    messages.push('❌ i18n system not initialized');
                    log('i18n system not initialized');
                    allGood = false;
                }
                
                messages.push(`Current language: ${window.i18n.currentLang || 'undefined'}`);
                log('Current language', window.i18n.currentLang);
                
                const translationCount = Object.keys(window.i18n.translations || {}).length;
                messages.push(`Translation keys loaded: ${translationCount}`);
                log('Translation keys count', translationCount);
                
            } else {
                messages.push('❌ i18n system not found');
                log('i18n system not found');
                allGood = false;
            }
            
            // Check if language switch fix is loaded
            if (typeof window.testLanguageSwitch === 'function') {
                messages.push('✅ Language switch fix is loaded');
                log('Language switch fix found');
            } else {
                messages.push('⚠️ Language switch fix not detected');
                log('Language switch fix not found');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (allGood ? 'success' : 'error');
        }
        
        function testTranslationSystem() {
            const result = document.getElementById('translation-test-result');
            let messages = [];
            
            log('Testing translation system...');
            
            if (window.i18n && window.i18n.translate) {
                // Test common translation keys
                const testKeys = [
                    'common.english',
                    'common.amazigh',
                    'common.app_name',
                    'common.language'
                ];
                
                testKeys.forEach(key => {
                    const translation = window.i18n.translate(key);
                    messages.push(`${key}: "${translation}"`);
                    log(`Translation test for ${key}`, translation);
                });
                
                result.className = 'result success';
            } else {
                messages.push('❌ Translation system not available');
                log('Translation system not available');
                result.className = 'result error';
            }
            
            result.innerHTML = messages.join('<br>');
        }
        
        function detectLanguageButtons() {
            const result = document.getElementById('button-detection-result');
            let messages = [];
            
            log('Detecting language buttons...');
            
            const languageButtons = document.querySelectorAll('.lang-btn');
            messages.push(`Found ${languageButtons.length} language buttons`);
            log('Language buttons found', languageButtons.length);
            
            languageButtons.forEach((button, index) => {
                const lang = button.getAttribute('data-lang');
                const text = button.textContent.trim();
                messages.push(`Button ${index + 1}: data-lang="${lang}", text="${text}"`);
                log(`Button ${index + 1}`, { lang, text });
            });
            
            // Check dropdown
            const dropdown = document.querySelector('.language-selector');
            if (dropdown) {
                messages.push('✅ Language dropdown found');
                log('Language dropdown found');
            } else {
                messages.push('❌ Language dropdown not found');
                log('Language dropdown not found');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (languageButtons.length > 0 ? 'success' : 'error');
        }
        
        function testLanguageSwitch(lang) {
            const result = document.getElementById('manual-switch-result');
            
            log(`Testing language switch to: ${lang}`);
            
            if (window.i18n && window.i18n.setLanguage) {
                try {
                    const oldLang = window.i18n.currentLang;
                    window.i18n.setLanguage(lang);
                    const newLang = window.i18n.currentLang;
                    
                    const message = `Language switched from "${oldLang}" to "${newLang}"`;
                    result.innerHTML = `✅ ${message}`;
                    result.className = 'result success';
                    log('Language switch successful', { from: oldLang, to: newLang });
                    
                    // Update test elements
                    setTimeout(updateTestElements, 100);
                    
                } catch (error) {
                    const message = `Error switching language: ${error.message}`;
                    result.innerHTML = `❌ ${message}`;
                    result.className = 'result error';
                    log('Language switch error', error);
                }
            } else {
                const message = 'Translation system not available';
                result.innerHTML = `❌ ${message}`;
                result.className = 'result error';
                log('Translation system not available for switch');
            }
        }
        
        function getCurrentLanguage() {
            const result = document.getElementById('manual-switch-result');
            
            if (window.i18n) {
                const currentLang = window.i18n.currentLang;
                const message = `Current language: ${currentLang}`;
                result.innerHTML = `ℹ️ ${message}`;
                result.className = 'result';
                log('Current language check', currentLang);
            } else {
                result.innerHTML = '❌ Translation system not available';
                result.className = 'result error';
                log('Translation system not available for current language check');
            }
        }
        
        function testEventListeners() {
            const result = document.getElementById('event-listeners-result');
            let messages = [];
            
            log('Testing event listeners...');
            
            const languageButtons = document.querySelectorAll('.lang-btn');
            
            languageButtons.forEach((button, index) => {
                // Check if button has event listeners (this is a rough check)
                const hasClickHandler = button.onclick !== null;
                const lang = button.getAttribute('data-lang');
                
                messages.push(`Button ${index + 1} (${lang}): ${hasClickHandler ? 'Has click handler' : 'No direct click handler'}`);
                log(`Button ${index + 1} event check`, { lang, hasClickHandler });
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result';
        }
        
        function testTranslationKeys() {
            const result = document.getElementById('translation-keys-result');
            let messages = [];
            
            log('Testing translation keys...');
            
            if (window.i18n && window.i18n.translations) {
                const keys = Object.keys(window.i18n.translations);
                messages.push(`Total translation keys: ${keys.length}`);
                
                // Test specific keys
                const importantKeys = [
                    'common.english',
                    'common.amazigh',
                    'general.language_english',
                    'general.language_amazigh'
                ];
                
                importantKeys.forEach(key => {
                    if (window.i18n.translations[key]) {
                        const en = window.i18n.translations[key].en || 'N/A';
                        const am = window.i18n.translations[key].am || 'N/A';
                        messages.push(`✅ ${key}: EN="${en}", AM="${am}"`);
                        log(`Key test ${key}`, { en, am });
                    } else {
                        messages.push(`❌ ${key}: Missing`);
                        log(`Key test ${key}`, 'Missing');
                    }
                });
                
                result.className = 'result success';
            } else {
                messages.push('❌ No translations available');
                log('No translations available');
                result.className = 'result error';
            }
            
            result.innerHTML = messages.join('<br>');
        }
        
        function updateTestElements() {
            log('Updating test elements...');
            
            if (window.i18n && window.i18n.updateUI) {
                window.i18n.updateUI();
                log('UI updated via i18n.updateUI()');
            } else {
                log('Manual UI update...');
                // Manual update
                document.querySelectorAll('[data-i18n]').forEach(element => {
                    const key = element.getAttribute('data-i18n');
                    if (window.i18n && window.i18n.translate) {
                        const text = window.i18n.translate(key);
                        element.textContent = text;
                        log(`Updated element ${key}`, text);
                    }
                });
            }
            
            const result = document.getElementById('live-test-result');
            result.innerHTML = '✅ Test elements updated';
            result.className = 'result success';
        }
        
        function showDebugInfo() {
            log('=== DEBUG INFO ===');
            log('Window.i18n object', window.i18n);
            log('Available functions', Object.getOwnPropertyNames(window.i18n || {}));
            log('Current language', window.i18n?.currentLang);
            log('Is loaded', window.i18n?.isLoaded);
            log('Translations count', Object.keys(window.i18n?.translations || {}).length);
            log('Language buttons', document.querySelectorAll('.lang-btn').length);
            log('localStorage selectedLanguage', localStorage.getItem('selectedLanguage'));
            log('localStorage preferred_language', localStorage.getItem('preferred_language'));
        }
        
        function clearDebugInfo() {
            debugOutput = [];
            updateDebugConsole();
        }
        
        // Auto-run basic checks on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkSystemStatus();
                detectLanguageButtons();
            }, 1000);
        });
        
        // Listen for language change events
        document.addEventListener('languageChanged', function(e) {
            log('Language changed event received', e.detail);
        });
        
        document.addEventListener('languageSystemLoaded', function(e) {
            log('Language system loaded event received', e.detail);
        });
    </script>
</body>
</html>
