/**
 * CKEditor 5 Setup for Tifinagh Converter
 *
 * This script initializes CKEditor 5 with table support and handles
 * the conversion between simple editor and advanced editor.
 */

let ckEditor = null;

document.addEventListener('DOMContentLoaded', function() {
    // Get tab buttons
    const textConverterTab = document.querySelector('[data-tab="text-converter"]');
    const advancedConverterTab = document.querySelector('[data-tab="advanced-converter"]');

    // Get tab content containers
    const textConverterContent = document.getElementById('text-converter');
    const advancedConverterContent = document.getElementById('advanced-converter');

    // Get convert tables button
    const convertTablesBtn = document.getElementById('convert-tables-btn');

    // Initialize CKEditor when advanced converter tab is shown
    if (advancedConverterTab) {
        advancedConverterTab.addEventListener('click', function() {
            // Initialize CKEditor if not already initialized
            if (!ckEditor) {
                // Small delay to ensure DOM is ready
                setTimeout(initCKEditor, 100);
            }

            // Transfer content from simple editor to advanced editor
            setTimeout(transferContentToAdvancedEditor, 200);
        });
    }

    // Transfer content back when switching to text converter tab
    if (textConverterTab) {
        textConverterTab.addEventListener('click', function() {
            // Transfer content from advanced editor to simple editor if CKEditor is initialized
            if (ckEditor) {
                transferContentToSimpleEditor();
            }
        });
    }

    // Add event listener for convert tables button
    if (convertTablesBtn) {
        convertTablesBtn.addEventListener('click', function() {
            convertTableCells();
        });
    }
});

/**
 * Initialize CKEditor
 */
function initCKEditor() {
    // Get the editor container - in the advanced converter tab
    const editorContainer = document.querySelector('#advanced-converter .document-editor-content');

    if (!editorContainer) {
        console.error('Editor container not found');
        return;
    }

    // Check if CKEditor is available
    if (typeof DecoupledEditor === 'undefined') {
        console.error('CKEditor not loaded');
        return;
    }

    // Initialize CKEditor with decoupled UI and table support
    DecoupledEditor
        .create(editorContainer, {
            // Configure toolbar with essential features including table support
            toolbar: [
                'heading', '|',
                'bold', 'italic', 'underline', '|',
                'bulletedList', 'numberedList', '|',
                'link', 'blockQuote', 'insertTable', '|',
                'undo', 'redo'
            ],
            // Table configuration
            table: {
                contentToolbar: [
                    'tableColumn', 'tableRow', 'mergeTableCells'
                ]
            },
            placeholder: 'Enter text in Latin script...'
        })
        .then(editor => {
            // Store editor instance
            ckEditor = editor;

            // Get the toolbar container
            const toolbarContainer = document.getElementById('document-editor-toolbar');
            if (toolbarContainer) {
                // Add the toolbar to the container
                toolbarContainer.appendChild(editor.ui.view.toolbar.element);
            }

            // Log success
            console.log('CKEditor initialized successfully');
            
            // Fix scrollbar issues - apply our custom overflow settings
            if (window.editorHeightControl && window.editorHeightControl.fixScrollbar) {
                window.editorHeightControl.fixScrollbar();
            }

            // Add event listener for content changes
            editor.model.document.on('change:data', () => {
                // Apply scrollbar fixes when content changes
                if (window.editorHeightControl && window.editorHeightControl.fixScrollbar) {
                    window.editorHeightControl.fixScrollbar();
                }
                
                // Check if content contains tables
                const content = editor.getData();
                if (content.includes('<table') || content.includes('<td') || content.includes('<th')) {
                    // Show convert tables button
                    const convertTablesBtn = document.getElementById('convert-tables-btn');
                    if (convertTablesBtn) {
                        convertTablesBtn.style.display = 'flex';
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error initializing CKEditor:', error);
        });
}

/**
 * Transfer content from simple editor to advanced editor
 */
function transferContentToAdvancedEditor() {
    if (!ckEditor) return;

    const simpleEditor = document.getElementById('latin-text');
    if (!simpleEditor) {
        console.error('Simple editor not found');
        return;
    }

    const content = simpleEditor.value;
    if (!content) return;

    // Check if content contains table markup
    if (content.includes('|') && (content.includes('--') || content.includes('-:') || content.includes(':-'))) {
        // Convert markdown tables to HTML
        const htmlContent = convertMarkdownTablesToHtml(content);
        ckEditor.setData(htmlContent);
    } else {
        // Set plain text
        ckEditor.setData(content);
    }

    console.log('Content transferred to advanced editor');
}

/**
 * Transfer content from advanced editor to simple editor
 */
function transferContentToSimpleEditor() {
    if (!ckEditor) return;

    const simpleEditor = document.getElementById('latin-text');
    if (!simpleEditor) {
        console.error('Simple editor not found');
        return;
    }

    const content = ckEditor.getData();
    if (!content) {
        simpleEditor.value = '';
        simpleEditor.dispatchEvent(new Event('input'));
        return;
    }

    // Convert HTML to plain text
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Handle tables specially - convert to markdown
    const tables = tempDiv.querySelectorAll('table');
    if (tables.length > 0) {
        // Process each table
        tables.forEach(table => {
            const markdownTable = convertHtmlTableToMarkdown(table);
            // Replace the table with its markdown representation
            table.outerHTML = '\n\n' + markdownTable + '\n\n';
        });
    }

    // Get the text content
    simpleEditor.value = tempDiv.textContent;

    // Trigger input event to update character count
    simpleEditor.dispatchEvent(new Event('input'));

    console.log('Content transferred to simple editor');
}

/**
 * Convert HTML table to Markdown format
 */
function convertHtmlTableToMarkdown(table) {
    let markdown = '';
    const rows = table.querySelectorAll('tr');

    // Process each row
    rows.forEach((row, rowIndex) => {
        const cells = row.querySelectorAll('th, td');
        let rowContent = '|';

        // Process each cell
        cells.forEach(cell => {
            rowContent += ' ' + cell.textContent.trim() + ' |';
        });

        markdown += rowContent + '\n';

        // Add separator row after header
        if (rowIndex === 0) {
            let separator = '|';
            for (let i = 0; i < cells.length; i++) {
                separator += ' --- |';
            }
            markdown += separator + '\n';
        }
    });

    return markdown;
}

/**
 * Convert Markdown tables to HTML
 */
function convertMarkdownTablesToHtml(text) {
    let result = '';
    const lines = text.split('\n');
    let inTable = false;
    let tableHTML = '';

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // Check if line is part of a table
        if (line.trim().startsWith('|') && line.trim().endsWith('|')) {
            if (!inTable) {
                // Start of a new table
                inTable = true;
                tableHTML = '<table><tbody>';
            }

            // Skip separator line
            if (line.includes('|-') || line.includes('-|')) {
                continue;
            }

            // Process table row
            const cells = line.split('|').filter(cell => cell.trim() !== '');
            tableHTML += '<tr>';

            for (const cell of cells) {
                tableHTML += `<td>${cell.trim()}</td>`;
            }

            tableHTML += '</tr>';
        } else {
            if (inTable) {
                // End of table
                inTable = false;
                tableHTML += '</tbody></table>';
                result += tableHTML + '\n';
                tableHTML = '';
            }

            // Add non-table line
            result += line + '\n';
        }
    }

    // Close any open table
    if (inTable) {
        tableHTML += '</tbody></table>';
        result += tableHTML;
    }

    return result;
}

/**
 * Convert table cells from Latin to Tifinagh
 */
function convertTableCells() {
    if (!ckEditor) return;

    // Get editor content
    const content = ckEditor.getData();

    // Create a temporary element to extract tables
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Find all tables
    const tables = tempDiv.querySelectorAll('table');

    if (tables.length === 0) {
        // No tables found, use regular conversion
        if (typeof window.convertText === 'function') {
            return window.convertText();
        } else {
            console.error('convertText function not available');
            return;
        }
    }

    // Get target element
    const targetText = document.getElementById('tifinagh-text');
    if (!targetText) {
        console.error('Target element not found');
        return;
    }

    // Show loading state
    targetText.textContent = 'Converting tables...';

    // Prepare data for conversion
    const cellsToConvert = [];
    let cellId = 0;

    // Extract text from each cell
    tables.forEach(table => {
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
            // Add a data attribute to identify the cell
            cell.setAttribute('data-cell-id', cellId);

            // Add cell to conversion list
            cellsToConvert.push({
                id: cellId,
                text: cell.textContent.trim()
            });

            cellId++;
        });
    });

    // Send data to server for conversion
    fetch('/api/convert-table-cells', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cells: cellsToConvert })
    })
    .then(response => response.json())
    .then(data => {
        // Update cell content with converted text
        data.cells.forEach(cell => {
            const cellElement = tempDiv.querySelector(`[data-cell-id="${cell.id}"]`);
            if (cellElement) {
                cellElement.textContent = cell.convertedText;
            }
        });

        // Update the output area with the converted HTML
        targetText.innerHTML = tempDiv.innerHTML;
        targetText.classList.add('contains-table');

        // Update character counter
        if (typeof updateCharCounter === 'function') {
            updateCharCounter();
        }
    })
    .catch(error => {
        console.error('Error converting table cells:', error);
        targetText.textContent = 'Error: ' + error.message;
    });
}
