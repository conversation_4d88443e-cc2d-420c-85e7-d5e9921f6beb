/**
 * ملف CSS بأولوية قصوى لضمان ظهور أشرطة التمرير في المحرر البسيط
 * يتم تحميله أخيراً لضمان تطبيق القواعد
 */

/* أولوية قصوى - إجبار ظهور شريط التمرير في المحرر البسيط */
#latin-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important; /* Firefox */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ */
    min-height: 500px !important;
    max-height: 500px !important;
    height: 500px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    box-sizing: border-box !important;
}

#tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important; /* Firefox */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ */
    min-height: 500px !important;
    max-height: 500px !important;
    height: 500px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    box-sizing: border-box !important;
}

/* إجبار ظهور شريط التمرير في Webkit browsers */
#latin-text::-webkit-scrollbar {
    display: block !important;
    width: 8px !important;
    height: 8px !important;
    background: transparent !important;
}

#tifinagh-text::-webkit-scrollbar {
    display: block !important;
    width: 8px !important;
    height: 8px !important;
    background: transparent !important;
}

/* تخصيص مظهر شريط التمرير */
#latin-text::-webkit-scrollbar-track,
#tifinagh-text::-webkit-scrollbar-track {
    background: #f8f9fa !important;
    border-radius: 4px !important;
    margin: 2px !important;
}

#latin-text::-webkit-scrollbar-thumb,
#tifinagh-text::-webkit-scrollbar-thumb {
    background: #dee2e6 !important;
    border-radius: 4px !important;
    border: 1px solid #f8f9fa !important;
    min-height: 20px !important;
}

#latin-text::-webkit-scrollbar-thumb:hover,
#tifinagh-text::-webkit-scrollbar-thumb:hover {
    background: #adb5bd !important;
}

#latin-text::-webkit-scrollbar-thumb:active,
#tifinagh-text::-webkit-scrollbar-thumb:active {
    background: #6c757d !important;
}

#latin-text::-webkit-scrollbar-corner,
#tifinagh-text::-webkit-scrollbar-corner {
    background: #f8f9fa !important;
}

/* إعدادات إضافية بأولوية عالية */
.text-area textarea,
.text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
}

.text-area textarea::-webkit-scrollbar,
.text-area .tifinagh-text::-webkit-scrollbar {
    display: block !important;
    width: 8px !important;
    height: 8px !important;
}

/* إعدادات خاصة للحاوي */
#text-converter .text-area {
    overflow: visible !important;
}

#text-converter .text-area textarea,
#text-converter .text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
}

/* إعدادات للشاشات المختلفة */
@media (max-width: 768px) {
    #latin-text,
    #tifinagh-text {
        min-height: 400px !important;
        max-height: 500px !important;
        height: 400px !important;
    }
    
    #latin-text::-webkit-scrollbar,
    #tifinagh-text::-webkit-scrollbar {
        width: 6px !important;
    }
}

@media (max-width: 480px) {
    #latin-text,
    #tifinagh-text {
        min-height: 350px !important;
        max-height: 450px !important;
        height: 350px !important;
    }
    
    #latin-text::-webkit-scrollbar,
    #tifinagh-text::-webkit-scrollbar {
        width: 5px !important;
    }
}

/* إزالة أي قواعد قد تخفي شريط التمرير - أولوية قصوى */
#latin-text,
#tifinagh-text,
.text-area textarea,
.text-area .tifinagh-text {
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
}

#latin-text::-webkit-scrollbar,
#tifinagh-text::-webkit-scrollbar,
.text-area textarea::-webkit-scrollbar,
.text-area .tifinagh-text::-webkit-scrollbar {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* تأكيد إضافي لضمان عدم إخفاء أشرطة التمرير */
#text-converter #latin-text,
#text-converter #tifinagh-text {
    overflow-y: auto !important;
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
}

#text-converter #latin-text::-webkit-scrollbar,
#text-converter #tifinagh-text::-webkit-scrollbar {
    display: block !important;
    width: 8px !important;
}

/* إعدادات نهائية بأولوية مطلقة */
html #latin-text,
html #tifinagh-text {
    overflow-y: auto !important;
    scrollbar-width: auto !important;
    -ms-overflow-style: auto !important;
}

html #latin-text::-webkit-scrollbar,
html #tifinagh-text::-webkit-scrollbar {
    display: block !important;
    width: 8px !important;
    opacity: 1 !important;
    visibility: visible !important;
}
