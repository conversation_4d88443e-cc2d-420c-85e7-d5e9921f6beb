/**
 * <PERSON><PERSON><PERSON> to fix editor height issues and prevent double scrollbar problems
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check if editorHeightControl exists from editor-height-control.js
    if (!window.editorHeightControl) {
        window.editorHeightControl = {};
    }
    
    // Add our fix function to the existing control object
    window.editorHeightControl.fixScrollbar = function() {
        fixEditorHeight();
    };
    
    // Watch for editor creation and fix its height behavior
    const observer = new MutationObserver(function(mutations) {
        // Check if the editor has been initialized
        const editorContent = document.querySelector('.ck-editor__editable');
        if (editorContent) {
            // Fix the editor's height behavior
            fixEditorHeight();
            // We found the editor, so we can disconnect the observer
            observer.disconnect();
        }
    });

    // Start observing the document body for the editor to be created
    observer.observe(document.body, { childList: true, subtree: true });

    // Function to fix the editor's height behavior
    function fixEditorHeight() {
        // Get editor elements
        const editorContent = document.querySelector('.ck-editor__editable');
        const documentEditorContent = document.querySelector('.document-editor-content');
        
        if (editorContent && documentEditorContent) {
            // Remove any fixed height constraints
            editorContent.style.maxHeight = 'none';
            editorContent.style.overflow = 'visible';
            documentEditorContent.style.maxHeight = 'none';
            documentEditorContent.style.overflow = 'visible';
            
            // Check if we have the advanced tab
            const advancedTab = document.getElementById('advanced-converter');
            if (advancedTab) {
                advancedTab.style.height = 'auto';
                advancedTab.style.overflow = 'visible';
            }
        }
    }
    
    // Handle tab switching to ensure editor is properly sized when its tab is shown
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // If clicking the advanced tab button, fix editor height after a brief delay
            if (this.getAttribute('data-tab') === 'advanced-converter') {
                setTimeout(fixEditorHeight, 100);
            }
        });
    });
});
