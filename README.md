# محول اللاتينية إلى تيفيناغ

تطبيق ويب لتحويل النصوص من الأحرف اللاتينية إلى أحرف تيفيناغ.

## الميزات

- تحويل النص المباشر من اللاتينية إلى تيفيناغ
- تحويل ملفات النصوص (.txt) وملفات Word (.docx)
- واجهة مستخدم سهلة الاستخدام
- دعم تحويل مواقع الويب

## متطلبات النظام

- Python 3.8+
- Flask
- Werkzeug
- python-dotenv
- python-docx (لدعم ملفات Word)
- beautifulsoup4 (لدعم تحويل مواقع الويب)
- docx2python (لدعم ملفات Word)

## التثبيت والتشغيل

### الطريقة السهلة (Windows):

1. قم بتشغيل ملف `setup.bat` لإعداد البيئة وتثبيت المكتبات المطلوبة:
```
setup.bat
```

2. بعد اكتمال الإعداد، قم بتشغيل التطبيق باستخدام ملف `run.bat`:
```
run.bat
```

3. سيتم فتح المتصفح تلقائيًا على العنوان:
```
http://localhost:5000
```

### الطريقة اليدوية:

1. إنشاء بيئة افتراضية وتفعيلها:
```
python -m venv venv
# في Windows
venv\Scripts\activate
# في Linux/Mac
source venv/bin/activate
```

2. تثبيت المتطلبات:
```
pip install flask werkzeug jinja2 python-dotenv python-docx bs4 docx2python
```

3. تشغيل التطبيق:
```
python app.py
```

4. فتح المتصفح على العنوان:
```
http://localhost:5000
```

## الاستخدام

### تحويل النص

1. انتقل إلى الصفحة الرئيسية
2. أدخل النص اللاتيني في مربع النص
3. انقر على زر "تحويل"
4. سيظهر النص المحول بأحرف تيفيناغ

### تحويل الملفات

1. انتقل إلى علامة تبويب "تحويل الملفات"
2. اختر ملف نصي (.txt)
3. انقر على زر "تحويل"
4. سيتم تنزيل الملف المحول تلقائيًا

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. افتح issue لمناقشة التغيير الذي ترغب في إجرائه
2. قم بعمل fork للمستودع
3. قم بإنشاء فرع جديد للميزة الخاصة بك
4. قم بإجراء التغييرات وإضافة الاختبارات إذا أمكن
5. قم بإرسال طلب سحب (pull request)

## الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).
