/**
 * ملف اختبار لفحص أشرطة التمرير
 * يمكن استخدامه في وضع التطوير للتأكد من عمل الحل بشكل صحيح
 */

// دالة لفحص أشرطة التمرير
function testScrollbars() {
    console.log('🔍 بدء فحص أشرطة التمرير...');

    const results = {
        advancedTabActive: false,
        simpleTabActive: false,
        mainScrollbarHidden: false,
        editorScrollbarOnly: false,
        simpleEditorScrollbar: false,
        noExtraScrollbars: true,
        responsiveWorking: true
    };

    // فحص إذا كان المحرر المتقدم نشطاً
    const advancedConverter = document.getElementById('advanced-converter');
    results.advancedTabActive = advancedConverter && advancedConverter.classList.contains('active');
    console.log(`🎯 المحرر المتقدم نشط: ${results.advancedTabActive ? '✅' : '❌'}`);

    // فحص إذا كان المحرر البسيط نشطاً
    const simpleConverter = document.getElementById('simple-converter');
    results.simpleTabActive = simpleConverter && simpleConverter.classList.contains('active');
    console.log(`📝 المحرر البسيط نشط: ${results.simpleTabActive ? '✅' : '❌'}`);

    if (results.advancedTabActive) {
        // فحص إخفاء شريط التمرير الرئيسي (فقط في المحرر المتقدم)
        const htmlOverflow = window.getComputedStyle(document.documentElement).overflow;
        results.mainScrollbarHidden = htmlOverflow === 'hidden';
        console.log(`📊 إخفاء شريط التمرير الرئيسي: ${results.mainScrollbarHidden ? '✅' : '❌'} (${htmlOverflow})`);

        // فحص شريط تمرير المحرر الوحيد
        const editorContent = document.querySelector('.document-editor-content');
        if (editorContent) {
            const editorOverflow = window.getComputedStyle(editorContent).overflowY;
            results.editorScrollbarOnly = editorOverflow === 'auto';
            console.log(`📝 شريط تمرير المحرر الوحيد: ${results.editorScrollbarOnly ? '✅' : '❌'} (${editorOverflow})`);
        } else {
            console.log('📝 المحرر غير موجود في الصفحة الحالية');
        }
    } else if (results.simpleTabActive) {
        // فحص شريط التمرير في المحرر البسيط
        const simpleTextareas = document.querySelectorAll('.text-area textarea, .text-area .tifinagh-text');
        if (simpleTextareas.length > 0) {
            let allHaveScrollbar = true;
            simpleTextareas.forEach(textarea => {
                const overflow = window.getComputedStyle(textarea).overflowY;
                if (overflow !== 'auto') {
                    allHaveScrollbar = false;
                }
            });
            results.simpleEditorScrollbar = allHaveScrollbar;
            console.log(`📝 شريط التمرير في المحرر البسيط: ${results.simpleEditorScrollbar ? '✅' : '❌'}`);
        } else {
            console.log('📝 مناطق النص في المحرر البسيط غير موجودة');
        }

        // في المحرر البسيط، يجب أن يكون التمرير طبيعياً للصفحة
        const htmlOverflow = window.getComputedStyle(document.documentElement).overflow;
        const isNormalScrolling = htmlOverflow !== 'hidden';
        console.log(`📊 التمرير الطبيعي للصفحة: ${isNormalScrolling ? '✅' : '❌'} (${htmlOverflow})`);
    } else {
        console.log('ℹ️ الاختبار يعمل في تبويب المحرر المتقدم أو البسيط');
        // في التبويبات الأخرى، يجب أن يكون التمرير طبيعياً
        const htmlOverflow = window.getComputedStyle(document.documentElement).overflow;
        const isNormalScrolling = htmlOverflow !== 'hidden';
        console.log(`📊 التمرير الطبيعي في التبويبات الأخرى: ${isNormalScrolling ? '✅' : '❌'} (${htmlOverflow})`);
    }
    
    // فحص عدم وجود أشرطة تمرير إضافية
    const containers = document.querySelectorAll('.advanced-editor-wrapper, #document-editor, #advanced-converter, body, html');
    containers.forEach((container, index) => {
        const overflow = window.getComputedStyle(container).overflow;
        const tagName = container.tagName.toLowerCase();

        if (tagName === 'html' || tagName === 'body') {
            if (overflow !== 'hidden') {
                results.noExtraScrollbars = false;
                console.log(`⚠️ ${tagName} لديه overflow: ${overflow} (يجب أن يكون hidden)`);
            }
        } else {
            if (overflow !== 'hidden') {
                results.noExtraScrollbars = false;
                console.log(`⚠️ حاوية ${container.className || container.id} لديها overflow: ${overflow}`);
            }
        }
    });

    if (results.noExtraScrollbars) {
        console.log('🚫 لا توجد أشرطة تمرير إضافية: ✅');
    }
    
    // فحص الاستجابة للشاشات المختلفة
    const ckContent = document.querySelector('.ck-content, .document-editor-content');
    if (ckContent) {
        const maxHeight = window.getComputedStyle(ckContent).maxHeight;
        let expectedHeight;

        if (window.innerWidth <= 480) {
            expectedHeight = '300px';
        } else if (window.innerWidth <= 768) {
            expectedHeight = '350px';
        } else {
            expectedHeight = '450px';
        }

        results.responsiveWorking = maxHeight === expectedHeight;
        console.log(`📱 الاستجابة للشاشات: ${results.responsiveWorking ? '✅' : '❌'} (${maxHeight} vs ${expectedHeight})`);
    }

    // فحص إضافي: التأكد من وجود شريط تمرير واحد فقط
    const scrollableElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const style = window.getComputedStyle(el);
        return style.overflowY === 'auto' || style.overflowY === 'scroll';
    });

    const editorScrollbars = scrollableElements.filter(el =>
        el.matches('.document-editor-content, .ck-content, .ck-editor__editable')
    );

    console.log(`📊 عدد العناصر القابلة للتمرير: ${scrollableElements.length}`);
    console.log(`📝 عدد أشرطة تمرير المحرر: ${editorScrollbars.length}`);

    if (scrollableElements.length === editorScrollbars.length && editorScrollbars.length <= 1) {
        console.log('✅ يوجد شريط تمرير واحد فقط (المحرر)');
    } else {
        console.log('❌ يوجد أكثر من شريط تمرير');
        results.noExtraScrollbars = false;
    }

    // النتيجة النهائية
    const allPassed = Object.values(results).every(result => result === true);
    console.log(`\n🎯 النتيجة النهائية: ${allPassed ? '✅ جميع الاختبارات نجحت' : '❌ بعض الاختبارات فشلت'}`);

    return results;
}

// دالة لفحص التداخل في أشرطة التمرير
function detectScrollbarOverlap() {
    console.log('🔍 فحص تداخل أشرطة التمرير...');
    
    const scrollableElements = document.querySelectorAll('*');
    const scrollbars = [];
    
    scrollableElements.forEach(element => {
        const style = window.getComputedStyle(element);
        if (style.overflowY === 'scroll' || style.overflowY === 'auto') {
            const rect = element.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
                scrollbars.push({
                    element: element,
                    selector: element.tagName + (element.className ? '.' + element.className.split(' ').join('.') : ''),
                    rect: rect
                });
            }
        }
    });
    
    console.log(`📊 عدد العناصر القابلة للتمرير: ${scrollbars.length}`);
    scrollbars.forEach((item, index) => {
        console.log(`${index + 1}. ${item.selector}`);
    });
    
    return scrollbars;
}

// تشغيل الاختبارات عند تحميل الصفحة
if (window.location.search.includes('debug=scrollbar')) {
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            console.log('🚀 بدء اختبارات أشرطة التمرير...');
            testScrollbars();
            detectScrollbarOverlap();
        }, 2000);
    });
}

// تصدير الدوال للاستخدام في وحدة التحكم
window.testScrollbars = testScrollbars;
window.detectScrollbarOverlap = detectScrollbarOverlap;
