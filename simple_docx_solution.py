"""
حل بسيط لتحويل ملفات Word من النص اللاتيني إلى تيفيناغ
"""
import os
import io
import logging
from utils.converter import latin_to_tifinagh

# إعداد التسجيل
logger = logging.getLogger(__name__)

def convert_docx_simple(input_file, output_file):
    """
    تحويل بسيط لملف Word من النص اللاتيني إلى تيفيناغ.
    يستخدم فقط في حالة عدم توفر مكتبة python-docx.
    
    Args:
        input_file: مسار الملف المدخل أو كائن ملف
        output_file: مسار الملف المخرج
        
    Returns:
        bool: True في حالة النجاح، False في حالة الفشل
    """
    try:
        # محاولة استيراد مكتبة python-docx
        try:
            from docx import Document
            has_docx = True
        except ImportError:
            has_docx = False
            logger.warning("مكتبة python-docx غير متوفرة. سيتم استخدام طريقة النص العادي.")
        
        if has_docx:
            # استخدام مكتبة python-docx إذا كانت متوفرة
            doc = Document(input_file)
            
            # تحويل النص في كل فقرة
            for paragraph in doc.paragraphs:
                paragraph.text = latin_to_tifinagh(paragraph.text)
            
            # تحويل النص في كل جدول
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        for paragraph in cell.paragraphs:
                            paragraph.text = latin_to_tifinagh(paragraph.text)
            
            # حفظ المستند
            doc.save(output_file)
            
        else:
            # استخدام محول بسيط في حالة عدم توفر المكتبة
            from utils.docx_converter import convert_docx_to_tifinagh
            convert_docx_to_tifinagh(input_file, output_file)
        
        return True
    
    except Exception as e:
        logger.error(f"حدث خطأ أثناء تحويل ملف Word: {str(e)}")
        return False
