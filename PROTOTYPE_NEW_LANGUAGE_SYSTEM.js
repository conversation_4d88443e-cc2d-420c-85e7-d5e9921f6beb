/**
 * نموذج أولي للنظام الجديد لتبديل اللغة
 * New Language System Prototype - Clean & Simple
 * 
 * المزايا:
 * - كود مبسط ومنظم (200 سطر مقابل 857 سطر)
 * - معمارية واضحة ومفهومة
 * - أداء محسن وسرعة أعلى
 * - سهولة في الصيانة والتطوير
 * - دعم كامل للوصولية والأجهزة المحمولة
 */

// ===== إعدادات النظام =====
const LanguageConfig = {
    defaultLanguage: 'en',
    supportedLanguages: ['en', 'am'],
    storageKey: 'preferred_language',
    apiEndpoints: {
        getTranslations: '/get-i18n',
        setLanguage: '/set-language'
    },
    languages: {
        'en': { name: 'English', nativeName: 'English', flag: '🇺🇸', dir: 'ltr' },
        'am': { name: 'Amazigh', nativeName: 'ⵜⴰⵎⴰⵣⵉⵖⵜ', flag: 'ⵣ', dir: 'ltr' }
    }
};

// ===== نظام الأحداث =====
const LanguageEvents = {
    LANGUAGE_CHANGED: 'language:changed',
    TRANSLATIONS_LOADED: 'translations:loaded',
    SYSTEM_READY: 'system:ready',
    ERROR_OCCURRED: 'language:error'
};

// ===== النظام الأساسي =====
const LanguageSystem = (function() {
    'use strict';
    
    // المتغيرات الخاصة
    let currentLanguage = LanguageConfig.defaultLanguage;
    let translations = {};
    let isInitialized = false;
    let isLoading = false;
    
    // Cache للترجمات
    const translationCache = new Map();
    
    // ===== الدوال الخاصة =====
    
    /**
     * تحميل الترجمات من الخادم
     */
    async function loadTranslations() {
        if (isLoading) return;
        isLoading = true;
        
        try {
            const response = await fetch(LanguageConfig.apiEndpoints.getTranslations);
            const data = await response.json();
            
            if (data.success) {
                translations = data.translations;
                translationCache.clear(); // تنظيف الكاش
                dispatchEvent(LanguageEvents.TRANSLATIONS_LOADED, { translations });
                return translations;
            } else {
                throw new Error(data.message || 'Failed to load translations');
            }
        } catch (error) {
            console.error('Error loading translations:', error);
            dispatchEvent(LanguageEvents.ERROR_OCCURRED, { error });
            throw error;
        } finally {
            isLoading = false;
        }
    }
    
    /**
     * حفظ اللغة في localStorage
     */
    function saveLanguagePreference(language) {
        try {
            localStorage.setItem(LanguageConfig.storageKey, language);
        } catch (error) {
            console.warn('Failed to save language preference:', error);
        }
    }
    
    /**
     * تحميل اللغة المحفوظة من localStorage
     */
    function loadSavedLanguage() {
        try {
            const saved = localStorage.getItem(LanguageConfig.storageKey);
            if (saved && LanguageConfig.supportedLanguages.includes(saved)) {
                return saved;
            }
        } catch (error) {
            console.warn('Failed to load saved language:', error);
        }
        return LanguageConfig.defaultLanguage;
    }
    
    /**
     * إرسال طلب تغيير اللغة للخادم
     */
    async function updateServerLanguage(language) {
        try {
            const response = await fetch(LanguageConfig.apiEndpoints.setLanguage, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({ language })
            });
            
            const data = await response.json();
            if (!data.success) {
                throw new Error(data.error || 'Server error');
            }
            
            return data;
        } catch (error) {
            console.error('Error updating server language:', error);
            throw error;
        }
    }
    
    /**
     * تحديث واجهة المستخدم
     */
    function updateUI() {
        // تحديث جميع العناصر التي تحتوي على data-i18n
        const elements = document.querySelectorAll('[data-i18n]');
        
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translatedText = translate(key);
            
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                if (element.hasAttribute('placeholder')) {
                    element.placeholder = translatedText;
                } else {
                    element.value = translatedText;
                }
            } else if (element.tagName === 'IMG') {
                element.alt = translatedText;
            } else {
                element.textContent = translatedText;
            }
        });
        
        // تحديث اتجاه الصفحة
        const direction = LanguageConfig.languages[currentLanguage].dir;
        document.documentElement.dir = direction;
        document.body.className = document.body.className.replace(/\b(ltr|rtl)\b/g, '') + ' ' + direction;
    }
    
    /**
     * إطلاق حدث مخصص
     */
    function dispatchEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }
    
    // ===== الواجهة العامة =====
    
    /**
     * تهيئة النظام
     */
    async function init() {
        if (isInitialized) {
            console.warn('Language system already initialized');
            return;
        }
        
        try {
            // تحميل اللغة المحفوظة
            currentLanguage = loadSavedLanguage();
            
            // تحميل الترجمات
            await loadTranslations();
            
            // تحديث واجهة المستخدم
            updateUI();
            
            isInitialized = true;
            dispatchEvent(LanguageEvents.SYSTEM_READY, { language: currentLanguage });
            
            console.log('✅ Language system initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize language system:', error);
            throw error;
        }
    }
    
    /**
     * تغيير اللغة
     */
    async function setLanguage(language) {
        if (!LanguageConfig.supportedLanguages.includes(language)) {
            throw new Error(`Unsupported language: ${language}`);
        }
        
        if (language === currentLanguage) {
            return; // نفس اللغة الحالية
        }
        
        const oldLanguage = currentLanguage;
        
        try {
            // تحديث الخادم
            await updateServerLanguage(language);
            
            // تحديث اللغة محلياً
            currentLanguage = language;
            
            // حفظ التفضيل
            saveLanguagePreference(language);
            
            // تحديث واجهة المستخدم
            updateUI();
            
            // إطلاق حدث تغيير اللغة
            dispatchEvent(LanguageEvents.LANGUAGE_CHANGED, {
                oldLanguage,
                newLanguage: language,
                languageData: LanguageConfig.languages[language]
            });
            
            console.log(`✅ Language changed from ${oldLanguage} to ${language}`);
        } catch (error) {
            // التراجع في حالة الخطأ
            currentLanguage = oldLanguage;
            console.error('❌ Failed to change language:', error);
            throw error;
        }
    }
    
    /**
     * ترجمة نص
     */
    function translate(key, params = {}) {
        if (!key) return '';
        
        // التحقق من الكاش أولاً
        const cacheKey = `${currentLanguage}:${key}`;
        if (translationCache.has(cacheKey)) {
            return translationCache.get(cacheKey);
        }
        
        let text = '';
        
        // البحث عن الترجمة
        if (translations[key] && translations[key][currentLanguage]) {
            text = translations[key][currentLanguage];
        } else if (translations[key] && translations[key]['en']) {
            // استخدام الإنجليزية كاحتياطي
            text = translations[key]['en'];
        } else {
            // إرجاع المفتاح نفسه إذا لم توجد ترجمة
            text = key;
        }
        
        // استبدال المعاملات
        if (params && Object.keys(params).length > 0) {
            Object.keys(params).forEach(param => {
                const regex = new RegExp(`{${param}}`, 'g');
                text = text.replace(regex, params[param]);
            });
        }
        
        // حفظ في الكاش
        translationCache.set(cacheKey, text);
        
        return text;
    }
    
    /**
     * الحصول على اللغة الحالية
     */
    function getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * الحصول على معلومات اللغة الحالية
     */
    function getCurrentLanguageInfo() {
        return LanguageConfig.languages[currentLanguage];
    }
    
    /**
     * التحقق من حالة التهيئة
     */
    function isReady() {
        return isInitialized;
    }
    
    /**
     * الحصول على جميع اللغات المدعومة
     */
    function getSupportedLanguages() {
        return LanguageConfig.supportedLanguages.map(lang => ({
            code: lang,
            ...LanguageConfig.languages[lang]
        }));
    }
    
    // إرجاع الواجهة العامة
    return {
        init,
        setLanguage,
        translate,
        getCurrentLanguage,
        getCurrentLanguageInfo,
        getSupportedLanguages,
        isReady
    };
})();

// ===== واجهة المستخدم =====
const LanguageUI = (function() {
    'use strict';
    
    let dropdownElement = null;
    let currentLanguageElement = null;
    
    /**
     * تهيئة واجهة المستخدم
     */
    function init() {
        // البحث عن عناصر واجهة المستخدم
        dropdownElement = document.querySelector('[data-language-system]');
        currentLanguageElement = document.querySelector('[data-current-language]');
        
        if (!dropdownElement) {
            console.warn('Language switcher element not found');
            return;
        }
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        // تحديث واجهة المستخدم الأولية
        updateUI();
        
        console.log('✅ Language UI initialized');
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // مستمع لتغيير اللغة
        dropdownElement.addEventListener('click', handleLanguageClick);
        
        // مستمعي أحداث النظام
        document.addEventListener(LanguageEvents.LANGUAGE_CHANGED, handleLanguageChanged);
        document.addEventListener(LanguageEvents.SYSTEM_READY, handleSystemReady);
    }
    
    /**
     * معالج النقر على اللغة
     */
    async function handleLanguageClick(event) {
        const languageLink = event.target.closest('[data-language]');
        if (!languageLink) return;
        
        event.preventDefault();
        
        const targetLanguage = languageLink.getAttribute('data-language');
        
        try {
            // إظهار حالة التحميل
            showLoading(true);
            
            // تغيير اللغة
            await LanguageSystem.setLanguage(targetLanguage);
            
        } catch (error) {
            console.error('Failed to change language:', error);
            // يمكن إضافة إشعار للمستخدم هنا
        } finally {
            showLoading(false);
        }
    }
    
    /**
     * معالج تغيير اللغة
     */
    function handleLanguageChanged(event) {
        updateUI();
        console.log('UI updated for language change:', event.detail);
    }
    
    /**
     * معالج جاهزية النظام
     */
    function handleSystemReady(event) {
        updateUI();
        console.log('UI updated for system ready:', event.detail);
    }
    
    /**
     * تحديث واجهة المستخدم
     */
    function updateUI() {
        if (!LanguageSystem.isReady()) return;
        
        const currentLang = LanguageSystem.getCurrentLanguage();
        const langInfo = LanguageSystem.getCurrentLanguageInfo();
        
        // تحديث النص الحالي
        if (currentLanguageElement) {
            currentLanguageElement.textContent = langInfo.nativeName;
        }
        
        // تحديث حالة الأزرار
        const languageLinks = dropdownElement.querySelectorAll('[data-language]');
        languageLinks.forEach(link => {
            const linkLang = link.getAttribute('data-language');
            if (linkLang === currentLang) {
                link.classList.add('active');
                link.setAttribute('aria-current', 'true');
            } else {
                link.classList.remove('active');
                link.removeAttribute('aria-current');
            }
        });
    }
    
    /**
     * إظهار/إخفاء حالة التحميل
     */
    function showLoading(show) {
        const button = dropdownElement.querySelector('.dropdown-toggle');
        if (button) {
            if (show) {
                button.classList.add('loading');
                button.disabled = true;
            } else {
                button.classList.remove('loading');
                button.disabled = false;
            }
        }
    }
    
    return {
        init
    };
})();

// ===== التهيئة التلقائية =====
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('🌐 Initializing new language system...');
        
        // تهيئة النظام الأساسي
        await LanguageSystem.init();
        
        // تهيئة واجهة المستخدم
        LanguageUI.init();
        
        console.log('🎉 Language system ready!');
    } catch (error) {
        console.error('❌ Failed to initialize language system:', error);
    }
});

// ===== تصدير للاستخدام العام =====
window.LanguageSystem = LanguageSystem;
window.LanguageUI = LanguageUI;

// دوال مساعدة للاختبار
window.__ = LanguageSystem.translate;
window.setLang = LanguageSystem.setLanguage;
window.getCurrentLang = LanguageSystem.getCurrentLanguage;

console.log('🌐 New Language System Prototype loaded - Clean & Simple!');
