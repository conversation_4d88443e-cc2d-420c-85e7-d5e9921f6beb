/**
 * نظام القائمة المنسدلة الجديد لتغيير اللغة
 * New Language Dropdown System
 */

(function() {
    'use strict';
    
    console.log('🌐 Language Dropdown New v1.0.0 loaded');
    
    let dropdownButton = null;
    let dropdownMenu = null;
    let currentLanguageDisplay = null;
    let langButtons = [];
    
    /**
     * تهيئة العناصر
     */
    function initializeElements() {
        dropdownButton = document.getElementById('languageDropdownButton');
        dropdownMenu = document.getElementById('languageDropdownMenu');
        currentLanguageDisplay = document.getElementById('currentLanguageDisplay');
        langButtons = document.querySelectorAll('.lang-btn-new');
        
        if (!dropdownButton || !dropdownMenu || !currentLanguageDisplay) {
            console.warn('🌐 Language dropdown elements not found');
            return false;
        }
        
        console.log('🌐 Language dropdown elements initialized');
        return true;
    }
    
    /**
     * حساب وتعيين موضع القائمة المنسدلة
     */
    function calculateDropdownPosition() {
        const buttonRect = dropdownButton.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const dropdownWidth = 250; // العرض المتوقع للقائمة
        const dropdownHeight = 120; // الارتفاع المتوقع للقائمة

        // حساب الموضع الأفقي
        let rightPosition = viewportWidth - buttonRect.right;

        // التأكد من عدم خروج القائمة من الشاشة أفقياً
        if (buttonRect.right - dropdownWidth < 0) {
            rightPosition = 20; // مسافة أمان من الحافة
        }

        // حساب الموضع العمودي
        let topPosition = buttonRect.bottom + 8;

        // التأكد من عدم خروج القائمة من الشاشة عمودياً
        if (topPosition + dropdownHeight > viewportHeight) {
            topPosition = buttonRect.top - dropdownHeight - 8; // عرض فوق الزر
        }

        // تعيين الموضع
        dropdownMenu.style.right = rightPosition + 'px';
        dropdownMenu.style.top = topPosition + 'px';

        console.log('🌐 Dropdown position calculated:', {
            right: rightPosition,
            top: topPosition,
            buttonRect: buttonRect,
            viewport: { width: viewportWidth, height: viewportHeight }
        });
    }

    /**
     * تبديل حالة القائمة المنسدلة
     */
    function toggleDropdown() {
        const isExpanded = dropdownButton.getAttribute('aria-expanded') === 'true';
        const newState = !isExpanded;

        dropdownButton.setAttribute('aria-expanded', newState);

        if (newState) {
            // حساب الموضع قبل إظهار القائمة
            calculateDropdownPosition();

            dropdownMenu.classList.add('show');
            dropdownMenu.style.display = 'block';

            // إضافة تأثير بصري للزر
            dropdownButton.style.transform = 'translateY(-1px) scale(1.02)';
            dropdownButton.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.2)';
        } else {
            dropdownMenu.classList.remove('show');

            // إزالة التأثير البصري للزر
            dropdownButton.style.transform = '';
            dropdownButton.style.boxShadow = '';

            setTimeout(() => {
                if (!dropdownMenu.classList.contains('show')) {
                    dropdownMenu.style.display = 'none';
                }
            }, 300); // انتظار انتهاء الانتقال
        }

        console.log('🌐 Dropdown toggled:', newState ? 'open' : 'closed');
    }
    
    /**
     * إغلاق القائمة المنسدلة
     */
    function closeDropdown() {
        dropdownButton.setAttribute('aria-expanded', 'false');
        dropdownMenu.classList.remove('show');
        setTimeout(() => {
            if (!dropdownMenu.classList.contains('show')) {
                dropdownMenu.style.display = 'none';
            }
        }, 200);
    }
    
    /**
     * تعيين اللغة النشطة
     */
    function setActiveLanguage(selectedButton) {
        // إزالة الحالة النشطة من جميع الأزرار
        langButtons.forEach(btn => {
            btn.classList.remove('active-lang-item');
            const checkIcon = btn.querySelector('.bi-check2');
            if (checkIcon) {
                checkIcon.classList.add('invisible');
            }
        });
        
        // إضافة الحالة النشطة للزر المحدد
        selectedButton.classList.add('active-lang-item');
        const checkIcon = selectedButton.querySelector('.bi-check2');
        if (checkIcon) {
            checkIcon.classList.remove('invisible');
        }
        
        // تحديث النص المعروض في الزر الرئيسي
        const selectedText = selectedButton.querySelector('span').cloneNode(true);
        currentLanguageDisplay.innerHTML = '';
        currentLanguageDisplay.appendChild(selectedText);
        
        // إغلاق القائمة المنسدلة
        closeDropdown();
        
        console.log('🌐 Active language set:', selectedButton.getAttribute('data-lang'));
    }
    
    /**
     * معالج النقر على زر اللغة
     */
    async function handleLanguageClick(button) {
        const targetLanguage = button.getAttribute('data-lang');
        if (!targetLanguage) return;
        
        console.log('🌐 Language button clicked:', targetLanguage);
        
        // التحقق من أن اللغة ليست نشطة بالفعل
        if (button.classList.contains('active-lang-item')) {
            console.log('🌐 Language already active, closing dropdown');
            closeDropdown();
            return;
        }
        
        try {
            // إظهار حالة التحميل
            button.style.opacity = '0.7';
            button.style.pointerEvents = 'none';
            
            // تغيير اللغة باستخدام النظام الجديد
            if (window.LanguageSystem && typeof window.LanguageSystem.setLanguage === 'function') {
                await window.LanguageSystem.setLanguage(targetLanguage);
            } else if (window.i18n && typeof window.i18n.setLanguage === 'function') {
                // استخدام النظام القديم كاحتياطي
                window.i18n.setLanguage(targetLanguage);
            } else {
                console.warn('🌐 No language system found, updating UI only');
            }
            
            // تحديث واجهة المستخدم
            setActiveLanguage(button);
            
        } catch (error) {
            console.error('🌐 Failed to change language:', error);
        } finally {
            // إزالة حالة التحميل
            button.style.opacity = '';
            button.style.pointerEvents = '';
        }
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // مستمع لزر القائمة المنسدلة
        dropdownButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown();
        });
        
        // مستمعي أزرار اللغة
        langButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                handleLanguageClick(button);
            });
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (event) => {
            if (!dropdownButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
                if (dropdownMenu.classList.contains('show')) {
                    closeDropdown();
                }
            }
        });
        
        // إغلاق القائمة عند الضغط على Escape
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && dropdownMenu.classList.contains('show')) {
                closeDropdown();
                dropdownButton.focus();
            }
        });

        // إعادة حساب الموضع عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            if (dropdownMenu.classList.contains('show')) {
                calculateDropdownPosition();
            }
        });

        // إعادة حساب الموضع عند التمرير
        window.addEventListener('scroll', () => {
            if (dropdownMenu.classList.contains('show')) {
                calculateDropdownPosition();
            }
        });
        
        // مستمعي أحداث تغيير اللغة
        document.addEventListener('language:changed', updateActiveState);
        document.addEventListener('languageChanged', updateActiveState);
        document.addEventListener('system:ready', updateActiveState);
        
        console.log('🌐 Event listeners setup complete');
    }
    
    /**
     * تحديث الحالة النشطة بناءً على اللغة الحالية
     */
    function updateActiveState() {
        let currentLang = 'en'; // افتراضي
        
        // محاولة الحصول على اللغة الحالية من الأنظمة المختلفة
        if (window.LanguageSystem && typeof window.LanguageSystem.getCurrentLanguage === 'function') {
            currentLang = window.LanguageSystem.getCurrentLanguage();
        } else if (window.i18n && window.i18n.currentLang) {
            currentLang = window.i18n.currentLang;
        }
        
        // العثور على الزر المطابق وتعيينه كنشط
        const activeButton = Array.from(langButtons).find(btn => 
            btn.getAttribute('data-lang') === currentLang
        );
        
        if (activeButton) {
            setActiveLanguage(activeButton);
            console.log('🌐 Active state updated for language:', currentLang);
        }
    }
    
    /**
     * تهيئة النظام
     */
    function init() {
        console.log('🌐 Initializing language dropdown...');
        
        if (!initializeElements()) {
            console.warn('🌐 Failed to initialize language dropdown elements');
            return;
        }
        
        setupEventListeners();
        
        // تعيين الحالة الأولية
        setTimeout(() => {
            updateActiveState();
        }, 100);
        
        console.log('🌐 Language dropdown initialized successfully');
    }
    
    // تصدير للاستخدام العام
    window.LanguageDropdownNew = {
        init,
        toggleDropdown,
        closeDropdown,
        setActiveLanguage,
        updateActiveState
    };
    
    // تهيئة تلقائية
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
})();
