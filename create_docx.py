"""
أداة لإنشاء وتحويل ملفات Word (docx) إلى تيفيناغ
"""
import os
import sys
import logging
import importlib.util
from utils.converter import latin_to_tifinagh

# إعداد التسجيل
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_required_libraries():
    """
    التحقق من وجود المكتبات المطلوبة

    Returns:
        dict: حالة توفر المكتبات
    """
    libraries = {
        'python-docx': 'docx',
        'docx2python': 'docx2python'
    }

    results = {}

    for lib_name, module_name in libraries.items():
        spec = importlib.util.find_spec(module_name)
        available = spec is not None
        results[lib_name] = available

        if not available:
            logger.warning(f"مكتبة {lib_name} غير متوفرة. يرجى تثبيتها باستخدام: pip install {lib_name}")

    # التحقق من وجود جميع المكتبات
    all_available = all(results.values())
    results['all_available'] = all_available

    return results

def create_docx(output_path='uploads/test.docx', convert_to_tifinagh=False):
    """
    إنشاء ملف Word جديد مع نص أمازيغي

    Args:
        output_path (str): مسار حفظ الملف
        convert_to_tifinagh (bool): تحويل النص إلى تيفيناغ

    Returns:
        str: مسار الملف المنشأ
    """
    try:
        # التأكد من وجود مكتبة python-docx
        try:
            from docx import Document
        except ImportError:
            logger.error("مكتبة python-docx غير متوفرة. يرجى تثبيتها باستخدام: pip install python-docx")
            raise ImportError("مكتبة python-docx غير متوفرة")

        # التأكد من وجود مجلد الحفظ
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"تم إنشاء مجلد الحفظ: {output_dir}")

        # إنشاء مستند جديد
        doc = Document()

        # النصوص الأمازيغية
        texts = [
            'Azul, amddakel inu!',
            'Nekk isem inu Amazigh.',
            'Taddart inu tella deg idurar n Arrif.',
            'Tutlayt inu d Tamazight.',
            'Tamurt inu d Tamazgha.',
            'Azul fell-awen!'
        ]

        # إضافة النصوص إلى المستند
        for text in texts:
            # تحويل النص إلى تيفيناغ إذا كان مطلوبًا
            if convert_to_tifinagh:
                text = latin_to_tifinagh(text)
            doc.add_paragraph(text)

        # حفظ المستند
        doc.save(output_path)
        logger.info(f"تم إنشاء ملف docx بنجاح: {output_path}")

        return output_path

    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف docx: {str(e)}")
        raise

def convert_existing_docx(input_path, output_path=None):
    """
    تحويل ملف Word موجود إلى تيفيناغ

    Args:
        input_path (str): مسار ملف Word المدخل
        output_path (str): مسار ملف Word المخرج (اختياري)

    Returns:
        str: مسار الملف المحول
    """
    try:
        # التأكد من وجود مكتبة python-docx
        try:
            from docx import Document
            import shutil
        except ImportError:
            logger.error("مكتبة python-docx غير متوفرة. يرجى تثبيتها باستخدام: pip install python-docx")
            raise ImportError("مكتبة python-docx غير متوفرة")

        # التحقق من وجود الملف المدخل
        if not os.path.exists(input_path):
            logger.error(f"الملف المدخل غير موجود: {input_path}")
            raise FileNotFoundError(f"الملف المدخل غير موجود: {input_path}")

        # تحديد مسار الملف المخرج إذا لم يتم تحديده
        if output_path is None:
            file_name, file_ext = os.path.splitext(os.path.basename(input_path))
            output_path = os.path.join('uploads', f"{file_name}_tifinagh{file_ext}")

        # التأكد من وجود مجلد الحفظ
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"تم إنشاء مجلد الحفظ: {output_dir}")

        # طريقة 1: استخدام نهج إنشاء مستند جديد تمامًا (أكثر موثوقية)
        logger.info("استخدام طريقة إنشاء مستند جديد تمامًا")

        # إنشاء مستند جديد
        new_doc = Document()

        # فتح المستند الأصلي
        original_doc = Document(input_path)

        # نسخ خصائص المستند
        # نسخ الأقسام
        for section in original_doc.sections:
            new_section = new_doc.add_section()
            new_section.page_height = section.page_height
            new_section.page_width = section.page_width
            new_section.left_margin = section.left_margin
            new_section.right_margin = section.right_margin
            new_section.top_margin = section.top_margin
            new_section.bottom_margin = section.bottom_margin
            new_section.header_distance = section.header_distance
            new_section.footer_distance = section.footer_distance
            new_section.orientation = section.orientation

        # نسخ الفقرات مع تحويل النص
        for paragraph in original_doc.paragraphs:
            if not paragraph.text.strip():
                # إضافة فقرة فارغة
                new_doc.add_paragraph()
                continue

            # تحويل النص إلى تيفيناغ
            tifinagh_text = latin_to_tifinagh(paragraph.text)

            # إنشاء فقرة جديدة
            new_para = new_doc.add_paragraph()

            # نسخ نمط الفقرة
            new_para.style = paragraph.style
            new_para.alignment = paragraph.alignment

            # إضافة النص المحول
            new_para.add_run(tifinagh_text)

        # نسخ الجداول
        for table in original_doc.tables:
            # التحقق من وجود صفوف في الجدول قبل إنشائه
            if len(table.rows) > 0 and len(table.rows[0].cells) > 0:
                # إنشاء جدول جديد بنفس عدد الصفوف والأعمدة
                new_table = new_doc.add_table(rows=len(table.rows), cols=len(table.rows[0].cells))
                new_table.style = table.style
            else:
                # إنشاء جدول فارغ بصف واحد وعمود واحد
                logger.warning("تم اكتشاف جدول فارغ، إنشاء جدول بصف واحد وعمود واحد")
                new_table = new_doc.add_table(rows=1, cols=1)
                if hasattr(table, 'style'):
                    new_table.style = table.style

            # نسخ محتوى الجدول (فقط إذا كان الجدول يحتوي على صفوف)
            if len(table.rows) > 0:
                for i, row in enumerate(table.rows):
                    # التحقق من أن الصف موجود في الجدول الجديد
                    if i < len(new_table.rows):
                        for j, cell in enumerate(row.cells):
                            # التحقق من أن العمود موجود في الصف
                            if j < len(new_table.rows[i].cells):
                                # الحصول على الخلية المقابلة في الجدول الجديد
                                new_cell = new_table.rows[i].cells[j]

                                # نسخ محتوى الخلية
                                for paragraph in cell.paragraphs:
                                    # تحويل النص إلى تيفيناغ
                                    tifinagh_text = latin_to_tifinagh(paragraph.text)

                                    # إضافة فقرة جديدة إلى الخلية
                                    if paragraph.text.strip():
                                        new_cell.add_paragraph(tifinagh_text)

        # حفظ المستند الجديد
        try:
            new_doc.save(output_path)
            logger.info(f"تم تحويل ملف Word إلى تيفيناغ بنجاح: {output_path}")
            return output_path
        except Exception as save_error:
            logger.error(f"خطأ في حفظ المستند: {str(save_error)}")
            raise

    except Exception as e:
        logger.error(f"خطأ في تحويل ملف Word: {str(e)}")

        # محاولة استخدام طريقة بسيطة جدًا في حالة فشل الطريقة الأولى
        try:
            logger.info("محاولة استخدام طريقة بسيطة جدًا لتحويل المستند")

            # إنشاء مستند جديد بسيط
            from docx import Document
            simple_doc = Document()

            # إضافة عنوان
            simple_doc.add_heading('النص المحول إلى تيفيناغ', level=1)

            # فتح المستند الأصلي
            try:
                original_doc = Document(input_path)

                # نسخ النص فقط من المستند الأصلي
                for paragraph in original_doc.paragraphs:
                    if paragraph.text.strip():
                        tifinagh_text = latin_to_tifinagh(paragraph.text)
                        simple_doc.add_paragraph(tifinagh_text)
            except Exception as read_error:
                logger.error(f"خطأ في قراءة المستند الأصلي: {str(read_error)}")
                # إضافة رسالة خطأ إلى المستند
                simple_doc.add_paragraph("حدث خطأ أثناء قراءة المستند الأصلي.")

            # حفظ المستند البسيط
            simple_doc.save(output_path)
            logger.info(f"تم تحويل ملف Word بطريقة بسيطة جدًا: {output_path}")

            return output_path
        except Exception as fallback_error:
            logger.error(f"فشل أيضًا في استخدام الطريقة البسيطة جدًا: {str(fallback_error)}")

            # محاولة أخيرة باستخدام docxtpl إذا كانت متوفرة
            try:
                logger.info("محاولة استخدام مكتبة docxtpl")

                # التحقق من وجود مكتبة docxtpl
                try:
                    from docxtpl import DocxTemplate
                except ImportError:
                    logger.error("مكتبة docxtpl غير متوفرة. يرجى تثبيتها باستخدام: pip install docxtpl")
                    raise ImportError("مكتبة docxtpl غير متوفرة")

                # إنشاء قالب بسيط
                template_path = os.path.join(os.path.dirname(__file__), 'templates', 'simple_template.docx')

                # التحقق من وجود القالب
                if not os.path.exists(template_path):
                    # إنشاء قالب بسيط
                    simple_template = Document()
                    simple_template.add_paragraph('{{ content }}')
                    os.makedirs(os.path.dirname(template_path), exist_ok=True)
                    simple_template.save(template_path)

                # استخدام القالب
                doc = DocxTemplate(template_path)

                # قراءة النص من الملف الأصلي
                try:
                    original_doc = Document(input_path)
                    content = "\n\n".join([latin_to_tifinagh(p.text) for p in original_doc.paragraphs if p.text.strip()])
                except Exception:
                    content = "حدث خطأ أثناء قراءة المستند الأصلي."

                # تحضير السياق
                context = {'content': content}

                # تطبيق القالب
                doc.render(context)

                # حفظ المستند
                doc.save(output_path)
                logger.info(f"تم تحويل ملف Word باستخدام docxtpl: {output_path}")

                return output_path
            except Exception as docxtpl_error:
                logger.error(f"فشل أيضًا في استخدام مكتبة docxtpl: {str(docxtpl_error)}")
                raise

if __name__ == "__main__":
    try:
        # التحقق من وجود المكتبات المطلوبة
        libraries_status = check_required_libraries()

        if not libraries_status['python-docx']:
            print("تحذير: مكتبة python-docx غير متوفرة. يرجى تثبيتها باستخدام: pip install python-docx")
            print("سيتم محاولة استخدام البدائل إن وجدت.")

        if not libraries_status['docx2python'] and not libraries_status['python-docx']:
            print("خطأ: لا توجد أي مكتبة متوفرة لمعالجة ملفات Word.")
            print("يرجى تثبيت إحدى المكتبات التالية:")
            print("- python-docx: pip install python-docx")
            print("- docx2python: pip install docx2python")
            sys.exit(1)

        # التأكد من وجود مجلد uploads
        if not os.path.exists('uploads'):
            os.makedirs('uploads', exist_ok=True)
            print("تم إنشاء مجلد uploads")

        # إنشاء ملف Word عادي
        regular_docx = create_docx()
        print(f"تم إنشاء ملف docx عادي: {regular_docx}")

        # إنشاء ملف Word مع تحويل النص إلى تيفيناغ
        tifinagh_docx = create_docx('uploads/test_tifinagh.docx', convert_to_tifinagh=True)
        print(f"تم إنشاء ملف docx بتيفيناغ: {tifinagh_docx}")

        # تحويل ملف Word موجود إلى تيفيناغ
        converted_docx = convert_existing_docx(regular_docx)
        print(f"تم تحويل ملف Word موجود إلى تيفيناغ: {converted_docx}")

        print("تمت جميع العمليات بنجاح")

        # عرض مسارات الملفات المنشأة
        print("\nالملفات المنشأة:")
        print(f"1. ملف Word عادي: {os.path.abspath(regular_docx)}")
        print(f"2. ملف Word بتيفيناغ: {os.path.abspath(tifinagh_docx)}")
        print(f"3. ملف Word محول: {os.path.abspath(converted_docx)}")

    except ImportError as e:
        print(f"خطأ في استيراد المكتبات: {str(e)}")
        print("يرجى التأكد من تثبيت المكتبات المطلوبة.")
    except FileNotFoundError as e:
        print(f"خطأ: {str(e)}")
        print("يرجى التأكد من وجود الملفات والمجلدات المطلوبة.")
    except Exception as e:
        print(f"حدث خطأ غير متوقع: {str(e)}")
