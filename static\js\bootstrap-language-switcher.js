/**
 * Bootstrap Language Switcher - نظام تغيير اللغة باستخدام Bootstrap 5.3.3
 * Compatible with Bootstrap dropdown component
 */

(function() {
    'use strict';
    
    console.log('🌐 Bootstrap Language Switcher v1.0.0 loaded');
    
    let currentLanguage = 'en';
    let isInitialized = false;
    
    // نصوص الترجمة الأساسية
    const languageNames = {
        en: 'English',
        am: 'ⵜⴰⵎⴰⵣⵉⵖⵜ'
    };
    
    /**
     * تهيئة نظام تغيير اللغة
     */
    function initializeLanguageSwitcher() {
        console.log('🌐 Initializing Bootstrap Language Switcher...');
        
        // الحصول على اللغة الحالية من الخادم أو التخزين المحلي
        detectCurrentLanguage();
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        // تحديث العرض الأولي
        updateLanguageDisplay();
        
        isInitialized = true;
        console.log('🌐 Bootstrap Language Switcher initialized successfully');
        
        // إرسال حدث التهيئة
        document.dispatchEvent(new CustomEvent('languageSystemLoaded', {
            detail: { system: 'bootstrap', version: '1.0.0' }
        }));
    }
    
    /**
     * اكتشاف اللغة الحالية
     */
    function detectCurrentLanguage() {
        // محاولة الحصول على اللغة من عنصر HTML
        const htmlLang = document.documentElement.lang;
        if (htmlLang && (htmlLang === 'en' || htmlLang === 'am')) {
            currentLanguage = htmlLang;
            console.log('🌐 Language detected from HTML:', currentLanguage);
            return;
        }
        
        // محاولة الحصول على اللغة من النص الحالي
        const currentText = document.getElementById('currentLanguageText');
        if (currentText) {
            const text = currentText.textContent.trim();
            if (text.includes('ⵜⴰⵎⴰⵣⵉⵖⵜ')) {
                currentLanguage = 'am';
            } else {
                currentLanguage = 'en';
            }
            console.log('🌐 Language detected from current text:', currentLanguage);
            return;
        }
        
        // افتراضي
        currentLanguage = 'en';
        console.log('🌐 Using default language:', currentLanguage);
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // مستمعي أزرار اللغة
        const languageButtons = document.querySelectorAll('[data-lang]');
        languageButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const selectedLang = this.getAttribute('data-lang');
                switchLanguage(selectedLang);
            });
        });
        
        console.log('🌐 Event listeners setup for', languageButtons.length, 'language buttons');
    }
    
    /**
     * تغيير اللغة
     */
    function switchLanguage(newLang) {
        if (newLang === currentLanguage) {
            console.log('🌐 Language already selected:', newLang);
            return;
        }
        
        console.log(`🌐 Switching language from ${currentLanguage} to ${newLang}`);
        
        // تحديث اللغة الحالية
        const oldLang = currentLanguage;
        currentLanguage = newLang;
        
        // تحديث العرض
        updateLanguageDisplay();
        
        // إرسال طلب تغيير اللغة للخادم
        sendLanguageChangeRequest(newLang)
            .then(() => {
                console.log('🌐 Language changed successfully on server');
                
                // إرسال أحداث تغيير اللغة
                document.dispatchEvent(new CustomEvent('language:changed', {
                    detail: { 
                        oldLang: oldLang, 
                        newLang: newLang,
                        system: 'bootstrap'
                    }
                }));
                
                document.dispatchEvent(new CustomEvent('languageChanged', {
                    detail: { 
                        language: newLang,
                        system: 'bootstrap'
                    }
                }));
                
                // إعادة تحميل الصفحة لتطبيق الترجمات
                setTimeout(() => {
                    window.location.reload();
                }, 100);
            })
            .catch(error => {
                console.error('🌐 Failed to change language on server:', error);
                // إرجاع اللغة السابقة في حالة الفشل
                currentLanguage = oldLang;
                updateLanguageDisplay();
            });
    }
    
    /**
     * إرسال طلب تغيير اللغة للخادم
     */
    function sendLanguageChangeRequest(lang) {
        return fetch('/set_language', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ language: lang })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status !== 'success') {
                throw new Error(data.message || 'Unknown error');
            }
            return data;
        });
    }
    
    /**
     * تحديث عرض اللغة
     */
    function updateLanguageDisplay() {
        // تحديث النص الحالي في الزر
        const currentText = document.getElementById('currentLanguageText');
        if (currentText) {
            const langName = languageNames[currentLanguage] || 'English';
            if (currentLanguage === 'am') {
                currentText.innerHTML = `<span class="tifinagh-text">${langName}</span>`;
            } else {
                currentText.textContent = langName;
            }
        }
        
        // تحديث أيقونات التحديد
        const languageButtons = document.querySelectorAll('[data-lang]');
        languageButtons.forEach(button => {
            const lang = button.getAttribute('data-lang');
            const checkIcon = button.querySelector('.check-icon');
            
            if (checkIcon) {
                if (lang === currentLanguage) {
                    checkIcon.classList.remove('invisible');
                } else {
                    checkIcon.classList.add('invisible');
                }
            }
        });
        
        console.log('🌐 Language display updated for:', currentLanguage);
    }
    
    /**
     * الحصول على اللغة الحالية
     */
    function getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * التحقق من حالة التهيئة
     */
    function isReady() {
        return isInitialized;
    }
    
    // تصدير الوظائف للاستخدام العام
    window.BootstrapLanguageSwitcher = {
        init: initializeLanguageSwitcher,
        switchLanguage: switchLanguage,
        getCurrentLanguage: getCurrentLanguage,
        isReady: isReady,
        updateDisplay: updateLanguageDisplay
    };
    
    // دعم للأنظمة القديمة
    window.switchToEnglish = () => switchLanguage('en');
    window.switchToAmazigh = () => switchLanguage('am');
    
    // تهيئة تلقائية
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeLanguageSwitcher);
    } else {
        setTimeout(initializeLanguageSwitcher, 100);
    }
    
})();
