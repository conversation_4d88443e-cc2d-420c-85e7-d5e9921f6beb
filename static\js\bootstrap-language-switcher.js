/**
 * Custom Language Switcher - نظام تغيير اللغة المخصص مع Bootstrap Styling
 * Custom dropdown with Bootstrap 5.3.3 styling and high z-index
 */

(function() {
    'use strict';

    console.log('🌐 Custom Language Switcher v1.0.0 loaded');

    let dropdownButton = null;
    let dropdownMenu = null;
    let currentLanguageDisplay = null;
    let langButtons = [];
    let currentLanguage = 'en';
    let isOpen = false;
    let isInitialized = false;
    
    // نصوص الترجمة الأساسية
    const languageNames = {
        en: 'English',
        am: 'ⵜⴰⵎⴰⵣⵉⵖⵜ'
    };
    
    /**
     * تهيئة العناصر
     */
    function initializeElements() {
        dropdownButton = document.getElementById('languageDropdownButton');
        dropdownMenu = document.getElementById('languageDropdownMenu');
        currentLanguageDisplay = document.getElementById('currentLanguageDisplay');
        langButtons = document.querySelectorAll('.lang-btn-new');

        if (!dropdownButton || !dropdownMenu || !currentLanguageDisplay) {
            console.warn('🌐 Language dropdown elements not found');
            return false;
        }

        console.log('🌐 Language dropdown elements initialized');
        return true;
    }

    /**
     * تهيئة نظام تغيير اللغة
     */
    function initializeLanguageSwitcher() {
        console.log('🌐 Initializing Custom Language Switcher...');

        if (!initializeElements()) {
            console.warn('🌐 Failed to initialize language dropdown elements');
            return;
        }

        // الحصول على اللغة الحالية من الخادم أو التخزين المحلي
        detectCurrentLanguage();

        // إعداد مستمعي الأحداث
        setupEventListeners();

        // تحديث العرض الأولي
        updateLanguageDisplay();

        isInitialized = true;
        console.log('🌐 Custom Language Switcher initialized successfully');

        // إرسال حدث التهيئة
        document.dispatchEvent(new CustomEvent('languageSystemLoaded', {
            detail: { system: 'custom', version: '1.0.0' }
        }));
    }
    
    /**
     * اكتشاف اللغة الحالية
     */
    function detectCurrentLanguage() {
        // محاولة الحصول على اللغة من عنصر HTML
        const htmlLang = document.documentElement.lang;
        if (htmlLang && (htmlLang === 'en' || htmlLang === 'am')) {
            currentLanguage = htmlLang;
            console.log('🌐 Language detected from HTML:', currentLanguage);
            return;
        }
        
        // محاولة الحصول على اللغة من النص الحالي
        const currentText = document.getElementById('currentLanguageText');
        if (currentText) {
            const text = currentText.textContent.trim();
            if (text.includes('ⵜⴰⵎⴰⵣⵉⵖⵜ')) {
                currentLanguage = 'am';
            } else {
                currentLanguage = 'en';
            }
            console.log('🌐 Language detected from current text:', currentLanguage);
            return;
        }
        
        // افتراضي
        currentLanguage = 'en';
        console.log('🌐 Using default language:', currentLanguage);
    }
    
    /**
     * حساب موضع القائمة المنسدلة
     */
    function calculateDropdownPosition() {
        const buttonRect = dropdownButton.getBoundingClientRect();
        const menuWidth = 192; // عرض القائمة
        const menuHeight = 120; // ارتفاع القائمة المتوقع
        const margin = 8; // مسافة من الزر

        // حساب الموضع الأفقي (من اليمين)
        let rightPosition = window.innerWidth - buttonRect.right;

        // التأكد من عدم خروج القائمة من الشاشة يساراً
        if (buttonRect.right - menuWidth < 0) {
            rightPosition = 10; // مسافة أمان من الحافة
        }

        // حساب الموضع العمودي
        let topPosition = buttonRect.bottom + margin;

        // التأكد من عدم خروج القائمة من أسفل الشاشة
        if (topPosition + menuHeight > window.innerHeight) {
            topPosition = buttonRect.top - menuHeight - margin; // عرض فوق الزر
        }

        // تطبيق المواضع
        dropdownMenu.style.right = rightPosition + 'px';
        dropdownMenu.style.top = topPosition + 'px';

        console.log('🌐 Dropdown positioned at:', { right: rightPosition, top: topPosition });
    }

    /**
     * تبديل حالة القائمة المنسدلة
     */
    function toggleDropdown() {
        isOpen = !isOpen;

        dropdownButton.setAttribute('aria-expanded', isOpen);

        if (isOpen) {
            // حساب الموضع قبل إظهار القائمة
            calculateDropdownPosition();
            dropdownMenu.style.display = 'block';
            dropdownMenu.classList.add('show');
        } else {
            dropdownMenu.classList.remove('show');
            setTimeout(() => {
                if (!dropdownMenu.classList.contains('show')) {
                    dropdownMenu.style.display = 'none';
                }
            }, 150);
        }

        console.log('🌐 Dropdown toggled:', isOpen ? 'open' : 'closed');
    }

    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // مستمع لزر القائمة المنسدلة
        dropdownButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            toggleDropdown();
        });

        // مستمعي أزرار اللغة
        langButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const selectedLang = this.getAttribute('data-lang');
                switchLanguage(selectedLang);
            });
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (event) => {
            if (!dropdownButton.contains(event.target) && !dropdownMenu.contains(event.target)) {
                if (isOpen) {
                    closeDropdown();
                }
            }
        });

        // إغلاق القائمة عند الضغط على Escape
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && isOpen) {
                closeDropdown();
                dropdownButton.focus();
            }
        });

        // إعادة حساب الموضع عند تغيير حجم النافذة
        window.addEventListener('resize', () => {
            if (isOpen) {
                calculateDropdownPosition();
            }
        });

        // إعادة حساب الموضع عند التمرير
        window.addEventListener('scroll', () => {
            if (isOpen) {
                calculateDropdownPosition();
            }
        });

        console.log('🌐 Event listeners setup for', langButtons.length, 'language buttons');
    }
    
    /**
     * إغلاق القائمة المنسدلة
     */
    function closeDropdown() {
        isOpen = false;
        dropdownButton.setAttribute('aria-expanded', 'false');
        dropdownMenu.classList.remove('show');
        setTimeout(() => {
            if (!dropdownMenu.classList.contains('show')) {
                dropdownMenu.style.display = 'none';
            }
        }, 150);
    }

    /**
     * تغيير اللغة
     */
    function switchLanguage(newLang) {
        if (newLang === currentLanguage) {
            console.log('🌐 Language already selected:', newLang);
            return;
        }
        
        console.log(`🌐 Switching language from ${currentLanguage} to ${newLang}`);
        
        // تحديث اللغة الحالية
        const oldLang = currentLanguage;
        currentLanguage = newLang;
        
        // إغلاق القائمة المنسدلة أولاً
        closeDropdown();

        // تحديث العرض
        updateLanguageDisplay();

        // إرسال طلب تغيير اللغة للخادم
        sendLanguageChangeRequest(newLang)
            .then(() => {
                console.log('🌐 Language changed successfully on server');
                
                // إرسال أحداث تغيير اللغة
                document.dispatchEvent(new CustomEvent('language:changed', {
                    detail: { 
                        oldLang: oldLang, 
                        newLang: newLang,
                        system: 'bootstrap'
                    }
                }));
                
                document.dispatchEvent(new CustomEvent('languageChanged', {
                    detail: { 
                        language: newLang,
                        system: 'bootstrap'
                    }
                }));
                
                // إعادة تحميل الصفحة لتطبيق الترجمات
                setTimeout(() => {
                    window.location.reload();
                }, 100);
            })
            .catch(error => {
                console.error('🌐 Failed to change language on server:', error);
                // إرجاع اللغة السابقة في حالة الفشل
                currentLanguage = oldLang;
                updateLanguageDisplay();
            });
    }
    
    /**
     * إرسال طلب تغيير اللغة للخادم
     */
    function sendLanguageChangeRequest(lang) {
        return fetch('/set_language', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ language: lang })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status !== 'success') {
                throw new Error(data.message || 'Unknown error');
            }
            return data;
        });
    }
    
    /**
     * تحديث عرض اللغة
     */
    function updateLanguageDisplay() {
        // تحديث النص الحالي في الزر
        if (currentLanguageDisplay) {
            const langName = languageNames[currentLanguage] || 'English';
            if (currentLanguage === 'am') {
                currentLanguageDisplay.innerHTML = `<span class="tifinagh-text">${langName}</span>`;
            } else {
                currentLanguageDisplay.textContent = langName;
            }
        }

        // تحديث أيقونات التحديد وحالة الأزرار
        langButtons.forEach(button => {
            const lang = button.getAttribute('data-lang');
            const checkIcon = button.querySelector('.check-icon');

            if (lang === currentLanguage) {
                button.classList.add('active-lang-item');
                if (checkIcon) {
                    checkIcon.classList.remove('invisible');
                }
            } else {
                button.classList.remove('active-lang-item');
                if (checkIcon) {
                    checkIcon.classList.add('invisible');
                }
            }
        });

        console.log('🌐 Language display updated for:', currentLanguage);
    }
    
    /**
     * الحصول على اللغة الحالية
     */
    function getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * التحقق من حالة التهيئة
     */
    function isReady() {
        return isInitialized;
    }
    
    // تصدير الوظائف للاستخدام العام
    window.CustomLanguageSwitcher = {
        init: initializeLanguageSwitcher,
        switchLanguage: switchLanguage,
        getCurrentLanguage: getCurrentLanguage,
        isReady: isReady,
        updateDisplay: updateLanguageDisplay,
        toggleDropdown: toggleDropdown,
        closeDropdown: closeDropdown
    };

    // دعم للأنظمة القديمة
    window.switchToEnglish = () => switchLanguage('en');
    window.switchToAmazigh = () => switchLanguage('am');

    // تهيئة تلقائية
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeLanguageSwitcher);
    } else {
        setTimeout(initializeLanguageSwitcher, 100);
    }
    
})();
