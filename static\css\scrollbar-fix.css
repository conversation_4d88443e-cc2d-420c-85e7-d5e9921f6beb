/**
 * حل موحد لأشرطة التمرير في التطبيق
 * يظهر شريط التمرير الداخلي للمحرر فقط ويخفي شريط التمرير الرئيسي
 */

/* إخفاء شريط التمرير الرئيسي للصفحة */
html {
    overflow: hidden !important; /* إخفاء شريط التمرير الرئيسي */
    height: 100%;
}

body {
    overflow: hidden !important; /* إخفاء شريط التمرير في الجسم */
    height: 100vh;
    margin: 0;
    padding: 0;
}

/* إعدادات المحرر المتقدم فقط - منع أشرطة التمرير الخارجية */
.advanced-editor-wrapper {
    overflow: hidden !important; /* منع أي تمرير خارجي */
    height: auto !important;
}

#document-editor {
    overflow: hidden !important; /* منع أي تمرير خارجي */
    height: auto !important;
}

/* إعدادات خاصة للمحرر المتقدم فقط عندما يكون نشطاً */
#advanced-converter.active {
    overflow: hidden !important;
    height: auto !important; /* تغيير من 100vh إلى auto */
}

/* إزالة الإعدادات العامة للحاوية الرئيسية لتجنب تأثيرها على التبويبات الأخرى */

/* منطقة المحتوى - شريط التمرير الداخلي الوحيد */
.document-editor-content {
    overflow-y: auto !important; /* شريط التمرير الداخلي الوحيد */
    overflow-x: hidden !important;
    max-height: 450px !important; /* تقليل الارتفاع إلى 450px */
    height: 450px !important; /* ارتفاع ثابت */
    min-height: 450px !important;
}

/* إعدادات CKEditor - شريط التمرير الداخلي الوحيد */
.ck-editor__editable,
.ck-content {
    overflow-y: auto !important; /* شريط التمرير الداخلي الوحيد */
    overflow-x: hidden !important;
    max-height: 450px !important; /* تقليل الارتفاع إلى 450px */
    height: 450px !important; /* ارتفاع ثابت */
    min-height: 450px !important;
}

/* منع أشرطة التمرير في حاويات المحرر المتقدم فقط */
#advanced-converter .tab-content,
#advanced-converter .tab-pane {
    overflow: hidden !important;
    height: auto !important;
}

/* السماح بالتمرير الطبيعي في التبويبات الأخرى */
.tab-content:not(#advanced-converter .tab-content),
.tab-pane:not(#advanced-converter .tab-pane) {
    overflow: visible !important;
    height: auto !important;
}

/* إعدادات خاصة للفوتر */
footer {
    width: 100% !important;
    overflow: hidden !important;
    flex-shrink: 0 !important; /* منع تقليص الفوتر */
}

/* تخصيص مظهر شريط التمرير الداخلي الوحيد */
.document-editor-content::-webkit-scrollbar,
.ck-content::-webkit-scrollbar {
    width: 12px; /* عرض أكبر لسهولة الاستخدام */
    height: 12px;
}

.document-editor-content::-webkit-scrollbar-track,
.ck-content::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 6px;
    margin: 2px;
}

.document-editor-content::-webkit-scrollbar-thumb,
.ck-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 6px;
    border: 2px solid #f1f3f4;
    min-height: 20px;
}

.document-editor-content::-webkit-scrollbar-thumb:hover,
.ck-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.document-editor-content::-webkit-scrollbar-thumb:active,
.ck-content::-webkit-scrollbar-thumb:active {
    background: #909090;
}

.document-editor-content::-webkit-scrollbar-corner,
.ck-content::-webkit-scrollbar-corner {
    background: #f1f3f4;
}

/* إعدادات للشاشات المختلفة */
@media (max-width: 768px) {
    .document-editor-content,
    .ck-content {
        max-height: 350px !important; /* ارتفاع أصغر للشاشات المتوسطة */
        height: 350px !important; /* ارتفاع ثابت */
        min-height: 350px !important;
    }

    /* شريط تمرير أصغر للشاشات المتوسطة */
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar {
        width: 10px;
    }
}

@media (max-width: 480px) {
    .document-editor-content,
    .ck-content {
        max-height: 300px !important; /* ارتفاع أصغر للشاشات الصغيرة */
        height: 300px !important; /* ارتفاع ثابت */
        min-height: 300px !important;
    }

    /* شريط تمرير أصغر للشاشات الصغيرة */
    .document-editor-content::-webkit-scrollbar,
    .ck-content::-webkit-scrollbar {
        width: 8px;
    }
}

/* إخفاء أي أشرطة تمرير أخرى قد تظهر (عدا المحرر البسيط والمتقدم) */
*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable):not(.text-area textarea):not(.text-area .tifinagh-text):not(#latin-text):not(#tifinagh-text) {
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

*:not(.document-editor-content):not(.ck-content):not(.ck-editor__editable):not(.text-area textarea):not(.text-area .tifinagh-text):not(#latin-text):not(#tifinagh-text)::-webkit-scrollbar {
    display: none !important; /* Safari and Chrome */
}

/* السماح بشريط التمرير في المحرر البسيط - أولوية عالية */
.text-area textarea,
.text-area .tifinagh-text {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scrollbar-width: auto !important; /* Firefox */
    -ms-overflow-style: auto !important; /* Internet Explorer 10+ */
    /* ضمان ظهور شريط التمرير */
    min-height: 500px !important;
    max-height: 500px !important;
}

/* تخصيص مظهر شريط التمرير في المحرر البسيط */
.text-area textarea::-webkit-scrollbar,
.text-area .tifinagh-text::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
}

.text-area textarea::-webkit-scrollbar-track,
.text-area .tifinagh-text::-webkit-scrollbar-track {
    background: #f8f9fa !important;
    border-radius: 4px !important;
}

.text-area textarea::-webkit-scrollbar-thumb,
.text-area .tifinagh-text::-webkit-scrollbar-thumb {
    background: #dee2e6 !important;
    border-radius: 4px !important;
    border: 1px solid #f8f9fa !important;
}

.text-area textarea::-webkit-scrollbar-thumb:hover,
.text-area .tifinagh-text::-webkit-scrollbar-thumb:hover {
    background: #adb5bd !important;
}
