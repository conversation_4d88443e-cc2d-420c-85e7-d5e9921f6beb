/**
 * إصلاح مشكلة عرض السنة في حقوق النشر
 * يضمن أن السنة الحالية تظهر بشكل صحيح في جميع الأوقات
 */

(function() {
    'use strict';
    
    console.log('🗓️ Copyright Year Fix v1.0.0 loaded');
    
    /**
     * تحديث السنة الحالية في جميع العناصر
     */
    function updateAllYearElements() {
        const yearElements = document.querySelectorAll('#current-year, [id="current-year"]');
        const currentYear = new Date().getFullYear();
        let updatedCount = 0;
        
        yearElements.forEach(element => {
            if (element) {
                const oldValue = element.textContent;
                element.textContent = currentYear;
                
                if (oldValue !== currentYear.toString()) {
                    console.log(`🗓️ Updated year from "${oldValue}" to "${currentYear}" in element:`, element);
                    updatedCount++;
                }
            }
        });
        
        if (updatedCount > 0) {
            console.log(`🗓️ Updated ${updatedCount} year elements to ${currentYear}`);
        }
        
        return updatedCount;
    }
    
    /**
     * مراقب للتغييرات في DOM
     */
    function setupDOMObserver() {
        const observer = new MutationObserver(function(mutations) {
            let shouldUpdate = false;
            
            mutations.forEach(function(mutation) {
                // التحقق من إضافة عقد جديدة
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // التحقق من وجود عناصر current-year في العقد المضافة
                            if (node.id === 'current-year' || 
                                node.querySelector && node.querySelector('#current-year')) {
                                shouldUpdate = true;
                            }
                        }
                    });
                }
                
                // التحقق من تغيير المحتوى
                if (mutation.type === 'characterData' && 
                    mutation.target.parentElement && 
                    mutation.target.parentElement.id === 'current-year') {
                    shouldUpdate = true;
                }
            });
            
            if (shouldUpdate) {
                setTimeout(updateAllYearElements, 10);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        console.log('🗓️ DOM observer setup for year elements');
        return observer;
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    function setupEventListeners() {
        // تحديث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🗓️ DOMContentLoaded - updating year elements');
            updateAllYearElements();
        });
        
        // تحديث عند تحميل نظام الترجمة
        document.addEventListener('languageSystemLoaded', function() {
            console.log('🗓️ Language system loaded - updating year elements');
            setTimeout(updateAllYearElements, 100);
        });
        
        // تحديث عند تغيير اللغة
        document.addEventListener('language:changed', function() {
            console.log('🗓️ Language changed - updating year elements');
            setTimeout(updateAllYearElements, 100);
        });
        
        // تحديث عند تغيير اللغة (النظام القديم)
        document.addEventListener('languageChanged', function() {
            console.log('🗓️ Language changed (legacy) - updating year elements');
            setTimeout(updateAllYearElements, 100);
        });
        
        // تحديث عند جاهزية النظام الجديد
        document.addEventListener('system:ready', function() {
            console.log('🗓️ New system ready - updating year elements');
            setTimeout(updateAllYearElements, 100);
        });
        
        console.log('🗓️ Event listeners setup complete');
    }
    
    /**
     * تهيئة النظام
     */
    function init() {
        console.log('🗓️ Initializing copyright year fix...');
        
        // إعداد مستمعي الأحداث
        setupEventListeners();
        
        // إعداد مراقب DOM
        setupDOMObserver();
        
        // تحديث فوري إذا كانت الصفحة محملة بالفعل
        if (document.readyState === 'loading') {
            console.log('🗓️ Document still loading, waiting for DOMContentLoaded');
        } else {
            console.log('🗓️ Document already loaded, updating immediately');
            updateAllYearElements();
        }
        
        // تحديث دوري كإجراء احتياطي
        setInterval(function() {
            const updated = updateAllYearElements();
            if (updated > 0) {
                console.log('🗓️ Periodic update found and fixed year elements');
            }
        }, 5000); // كل 5 ثوان
        
        console.log('🗓️ Copyright year fix initialized successfully');
    }
    
    // تصدير للاستخدام العام
    window.CopyrightYearFix = {
        updateAllYearElements,
        init
    };
    
    // تهيئة تلقائية
    init();
    
})();
