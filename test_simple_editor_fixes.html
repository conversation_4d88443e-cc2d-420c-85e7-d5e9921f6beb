<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Editor Fixes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-area { width: 100%; height: 200px; border: 1px solid #ccc; padding: 10px; }
        .scrollbar-test { overflow-y: auto; overflow-x: hidden; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 10px 15px; }
    </style>
</head>
<body>
    <h1>🔍 Simple Text Converter - Fix Testing</h1>
    
    <div class="test-section">
        <h2>1. Scrollbar Test</h2>
        <p>This tests if scrollbars appear when content exceeds container height:</p>
        
        <h3>Latin Text Area (like #latin-text):</h3>
        <textarea id="test-latin" class="test-area scrollbar-test" placeholder="Enter long text to test scrollbar...">
This is a test of the scrollbar functionality.
Line 2
Line 3
Line 4
Line 5
Line 6
Line 7
Line 8
Line 9
Line 10
Line 11
Line 12
Line 13
Line 14
Line 15
Line 16
Line 17
Line 18
Line 19
Line 20
This should trigger a scrollbar if working correctly.
        </textarea>
        
        <h3>Tifinagh Output Area (like #tifinagh-text):</h3>
        <div id="test-tifinagh" class="test-area scrollbar-test">
            <div>ⴰⵙⵏⴼⴰⵍ - Asnfal</div>
            <div>ⵜⴰⵎⴰⵣⵉⵖⵜ - Tamazight</div>
            <div>ⴰⵎⴰⵣⵉⵖ - Amazigh</div>
            <div>ⵜⵉⴼⵉⵏⴰⵖ - Tifinagh</div>
            <div>Line 5</div>
            <div>Line 6</div>
            <div>Line 7</div>
            <div>Line 8</div>
            <div>Line 9</div>
            <div>Line 10</div>
            <div>Line 11</div>
            <div>Line 12</div>
            <div>Line 13</div>
            <div>Line 14</div>
            <div>Line 15</div>
            <div>Line 16</div>
            <div>Line 17</div>
            <div>Line 18</div>
            <div>Line 19</div>
            <div>Line 20</div>
        </div>
        
        <div id="scrollbar-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Language System Test</h2>
        <p>This tests the translation system functionality:</p>
        
        <button onclick="testLanguageSystem()">Test Language System</button>
        <button onclick="testLanguageSwitch('en')">Switch to English</button>
        <button onclick="testLanguageSwitch('am')">Switch to Amazigh</button>
        
        <div id="language-result" class="result"></div>
        
        <h3>Test Elements with Translation Keys:</h3>
        <p data-i18n="common.english">English</p>
        <p data-i18n="common.amazigh">ⵜⴰⵎⴰⵣⵉⵖⵜ</p>
        <p data-i18n="text_converter.title">Simple Text Converter</p>
        <p data-i18n="text_converter.latin_placeholder">Enter Latin text here...</p>
    </div>
    
    <div class="test-section">
        <h2>3. CSS Loading Test</h2>
        <p>This checks if the correct CSS files are loaded:</p>
        <button onclick="testCSSLoading()">Check CSS Files</button>
        <div id="css-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. JavaScript Loading Test</h2>
        <p>This checks if the correct JavaScript files are loaded:</p>
        <button onclick="testJSLoading()">Check JavaScript Files</button>
        <div id="js-result" class="result"></div>
    </div>

    <script>
        // Test scrollbar functionality
        function testScrollbars() {
            const latinArea = document.getElementById('test-latin');
            const tifinagh = document.getElementById('test-tifinagh');
            const result = document.getElementById('scrollbar-result');
            
            let messages = [];
            
            // Check computed styles
            const latinStyle = getComputedStyle(latinArea);
            const tifinagStyle = getComputedStyle(tifinagh);
            
            if (latinStyle.overflowY === 'auto' || latinStyle.overflowY === 'scroll') {
                messages.push('✅ Latin textarea has correct overflow-y');
            } else {
                messages.push('❌ Latin textarea overflow-y: ' + latinStyle.overflowY);
            }
            
            if (tifinagStyle.overflowY === 'auto' || tifinagStyle.overflowY === 'scroll') {
                messages.push('✅ Tifinagh div has correct overflow-y');
            } else {
                messages.push('❌ Tifinagh div overflow-y: ' + tifinagStyle.overflowY);
            }
            
            // Check if scrollbars are actually visible
            if (latinArea.scrollHeight > latinArea.clientHeight) {
                messages.push('✅ Latin area content exceeds height (scrollbar should be visible)');
            } else {
                messages.push('⚠️ Latin area content fits within height');
            }
            
            if (tifinagh.scrollHeight > tifinagh.clientHeight) {
                messages.push('✅ Tifinagh area content exceeds height (scrollbar should be visible)');
            } else {
                messages.push('⚠️ Tifinagh area content fits within height');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        // Test language system
        function testLanguageSystem() {
            const result = document.getElementById('language-result');
            let messages = [];
            
            if (typeof window.i18n !== 'undefined') {
                messages.push('✅ i18n system is loaded');
                messages.push('Current language: ' + (window.i18n.currentLang || 'undefined'));
                messages.push('Translations loaded: ' + Object.keys(window.i18n.translations || {}).length);
                
                if (window.i18n.translations && window.i18n.translations['common.english']) {
                    messages.push('✅ Translation keys found');
                } else {
                    messages.push('❌ Translation keys missing');
                }
            } else {
                messages.push('❌ i18n system not loaded');
            }
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        // Test language switching
        function testLanguageSwitch(lang) {
            const result = document.getElementById('language-result');
            
            if (window.i18n && window.i18n.setLanguage) {
                window.i18n.setLanguage(lang);
                result.innerHTML = `Language switched to: ${lang}<br>Current language: ${window.i18n.currentLang}`;
                result.className = 'result success';
            } else {
                result.innerHTML = '❌ Cannot switch language - i18n system not available';
                result.className = 'result error';
            }
        }
        
        // Test CSS loading
        function testCSSLoading() {
            const result = document.getElementById('css-result');
            const stylesheets = Array.from(document.styleSheets);
            let messages = [];
            
            const requiredCSS = [
                'simple-editor-scrollbar.css',
                'scrollbar-fix.css',
                'single-scrollbar-only.css'
            ];
            
            requiredCSS.forEach(css => {
                const found = stylesheets.some(sheet => 
                    sheet.href && sheet.href.includes(css)
                );
                if (found) {
                    messages.push(`✅ ${css} loaded`);
                } else {
                    messages.push(`❌ ${css} not found`);
                }
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        // Test JavaScript loading
        function testJSLoading() {
            const result = document.getElementById('js-result');
            let messages = [];
            
            const requiredJS = [
                { name: 'i18n system', check: () => typeof window.i18n !== 'undefined' },
                { name: 'Bootstrap', check: () => typeof window.bootstrap !== 'undefined' },
                { name: 'jQuery (if used)', check: () => typeof window.$ !== 'undefined' }
            ];
            
            requiredJS.forEach(js => {
                if (js.check()) {
                    messages.push(`✅ ${js.name} loaded`);
                } else {
                    messages.push(`❌ ${js.name} not loaded`);
                }
            });
            
            result.innerHTML = messages.join('<br>');
            result.className = 'result ' + (messages.some(m => m.includes('❌')) ? 'error' : 'success');
        }
        
        // Run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testScrollbars();
                testLanguageSystem();
                testCSSLoading();
                testJSLoading();
            }, 1000);
        });
    </script>
</body>
</html>
