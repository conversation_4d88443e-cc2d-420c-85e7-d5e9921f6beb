<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Switcher Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .debug-panel { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-good { color: #28a745; font-weight: bold; }
        .status-bad { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .code-block { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>🔧 Language Switcher Debug Tool</h1>
    <p>This tool will help diagnose language switching issues step by step.</p>

    <div class="debug-panel">
        <h2>1. System Components Check</h2>
        <button onclick="checkSystemComponents()">Check System Components</button>
        <div id="system-check-result"></div>
    </div>

    <div class="debug-panel">
        <h2>2. HTML Structure Analysis</h2>
        <button onclick="analyzeHTMLStructure()">Analyze HTML Structure</button>
        <div id="html-analysis-result"></div>
    </div>

    <div class="debug-panel">
        <h2>3. JavaScript Objects Inspection</h2>
        <button onclick="inspectJavaScriptObjects()">Inspect JavaScript Objects</button>
        <div id="js-inspection-result"></div>
    </div>

    <div class="debug-panel">
        <h2>4. Event Handlers Testing</h2>
        <button onclick="testEventHandlers()">Test Event Handlers</button>
        <div id="event-test-result"></div>
    </div>

    <div class="debug-panel">
        <h2>5. Translation System Integration</h2>
        <button onclick="testTranslationIntegration()">Test Translation Integration</button>
        <div id="translation-integration-result"></div>
    </div>

    <div class="debug-panel">
        <h2>6. Manual Language Switch Test</h2>
        <button onclick="manualSwitchTest('en')">Switch to English</button>
        <button onclick="manualSwitchTest('am')">Switch to Amazigh</button>
        <div id="manual-switch-result"></div>
    </div>

    <div class="debug-panel">
        <h2>7. Console Errors Monitor</h2>
        <button onclick="startErrorMonitoring()">Start Error Monitoring</button>
        <button onclick="stopErrorMonitoring()">Stop Error Monitoring</button>
        <div id="error-monitor-result"></div>
    </div>

    <div class="debug-panel">
        <h2>8. Live Debug Console</h2>
        <div id="live-console" class="code-block" style="height: 200px; overflow-y: auto; background: #000; color: #0f0;"></div>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script>
        let debugLog = [];
        let errorMonitoring = false;
        let originalConsoleError = console.error;
        let originalConsoleLog = console.log;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push({ message: logEntry, type });
            updateLiveConsole();
            
            // Also log to browser console
            console.log(logEntry);
        }

        function updateLiveConsole() {
            const console = document.getElementById('live-console');
            const lastEntries = debugLog.slice(-20);
            console.innerHTML = lastEntries.map(entry => {
                const color = entry.type === 'error' ? '#f00' : entry.type === 'warning' ? '#ff0' : '#0f0';
                return `<div style="color: ${color}">${entry.message}</div>`;
            }).join('');
            console.scrollTop = console.scrollHeight;
        }

        function checkSystemComponents() {
            log('Starting system components check...');
            const result = document.getElementById('system-check-result');
            let html = '<h3>System Components Status:</h3>';

            // Check i18n system
            if (typeof window.i18n !== 'undefined') {
                html += '<div class="test-result success">✅ window.i18n exists</div>';
                html += `<div class="test-result info">Current language: ${window.i18n.currentLang || 'undefined'}</div>`;
                html += `<div class="test-result info">Is loaded: ${window.i18n.isLoaded}</div>`;
                html += `<div class="test-result info">Translation count: ${Object.keys(window.i18n.translations || {}).length}</div>`;
                log('i18n system found and analyzed');
            } else {
                html += '<div class="test-result error">❌ window.i18n not found</div>';
                log('ERROR: i18n system not found', 'error');
            }

            // Check language switcher
            if (typeof window.languageSwitcher !== 'undefined') {
                html += '<div class="test-result success">✅ window.languageSwitcher exists</div>';
                html += `<div class="test-result info">Is initialized: ${window.languageSwitcher.isInitialized}</div>`;
                html += `<div class="test-result info">Is ready: ${window.languageSwitcher.isReady()}</div>`;
                log('Language switcher found and analyzed');
            } else {
                html += '<div class="test-result error">❌ window.languageSwitcher not found</div>';
                log('ERROR: Language switcher not found', 'error');
            }

            // Check global functions
            const globalFunctions = ['switchToEnglish', 'switchToAmazigh', 'getCurrentLang'];
            globalFunctions.forEach(func => {
                if (typeof window[func] === 'function') {
                    html += `<div class="test-result success">✅ window.${func} function exists</div>`;
                    log(`Global function ${func} found`);
                } else {
                    html += `<div class="test-result error">❌ window.${func} function missing</div>`;
                    log(`ERROR: Global function ${func} missing`, 'error');
                }
            });

            result.innerHTML = html;
        }

        function analyzeHTMLStructure() {
            log('Starting HTML structure analysis...');
            const result = document.getElementById('html-analysis-result');
            let html = '<h3>HTML Structure Analysis:</h3>';

            // Check language dropdown
            const dropdown = document.querySelector('.language-selector');
            if (dropdown) {
                html += '<div class="test-result success">✅ Language selector found</div>';
                log('Language selector found');
            } else {
                html += '<div class="test-result error">❌ Language selector not found</div>';
                log('ERROR: Language selector not found', 'error');
            }

            // Check language buttons
            const langButtons = document.querySelectorAll('.lang-btn');
            html += `<div class="test-result info">Found ${langButtons.length} language buttons</div>`;
            log(`Found ${langButtons.length} language buttons`);

            langButtons.forEach((button, index) => {
                const lang = button.getAttribute('data-lang');
                const text = button.textContent.trim();
                const hasCheckIcon = button.querySelector('.bi-check2') !== null;
                
                html += `<div class="test-result info">Button ${index + 1}: lang="${lang}", text="${text}", hasCheckIcon=${hasCheckIcon}</div>`;
                log(`Button ${index + 1}: lang="${lang}", text="${text}"`);
            });

            // Check current language display
            const currentLangDisplay = document.querySelector('.current-language');
            if (currentLangDisplay) {
                html += '<div class="test-result success">✅ Current language display found</div>';
                html += `<div class="test-result info">Current display text: "${currentLangDisplay.textContent.trim()}"</div>`;
                log('Current language display found');
            } else {
                html += '<div class="test-result error">❌ Current language display not found</div>';
                log('ERROR: Current language display not found', 'error');
            }

            result.innerHTML = html;
        }

        function inspectJavaScriptObjects() {
            log('Starting JavaScript objects inspection...');
            const result = document.getElementById('js-inspection-result');
            let html = '<h3>JavaScript Objects Inspection:</h3>';

            // Detailed i18n inspection
            if (window.i18n) {
                html += '<div class="test-result success">✅ i18n Object Details:</div>';
                html += '<div class="code-block">';
                html += `Current Language: ${window.i18n.currentLang}<br>`;
                html += `Is Loaded: ${window.i18n.isLoaded}<br>`;
                html += `Available Methods: ${Object.getOwnPropertyNames(window.i18n).filter(prop => typeof window.i18n[prop] === 'function').join(', ')}<br>`;
                html += `Translation Keys: ${Object.keys(window.i18n.translations || {}).length} keys<br>`;
                html += '</div>';
                log('i18n object inspected in detail');
            }

            // Detailed language switcher inspection
            if (window.languageSwitcher) {
                html += '<div class="test-result success">✅ Language Switcher Object Details:</div>';
                html += '<div class="code-block">';
                html += `Is Initialized: ${window.languageSwitcher.isInitialized}<br>`;
                html += `Debug Mode: ${window.languageSwitcher.debug}<br>`;
                html += `Retry Count: ${window.languageSwitcher.retryCount}<br>`;
                html += `Max Retries: ${window.languageSwitcher.maxRetries}<br>`;
                html += `Available Methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(window.languageSwitcher)).filter(prop => typeof window.languageSwitcher[prop] === 'function').join(', ')}<br>`;
                html += '</div>';
                log('Language switcher object inspected in detail');
            }

            result.innerHTML = html;
        }

        function testEventHandlers() {
            log('Starting event handlers test...');
            const result = document.getElementById('event-test-result');
            let html = '<h3>Event Handlers Test:</h3>';

            const langButtons = document.querySelectorAll('.lang-btn');
            
            if (langButtons.length === 0) {
                html += '<div class="test-result error">❌ No language buttons found to test</div>';
                log('ERROR: No language buttons found', 'error');
                result.innerHTML = html;
                return;
            }

            langButtons.forEach((button, index) => {
                const lang = button.getAttribute('data-lang');
                
                // Test if button has event listeners (rough check)
                const hasOnClick = button.onclick !== null;
                const hasEventListeners = button.getEventListeners ? Object.keys(button.getEventListeners()).length > 0 : 'Cannot detect';
                
                html += `<div class="test-result info">Button ${index + 1} (${lang}):</div>`;
                html += `<div class="test-result info">- Has onclick: ${hasOnClick}</div>`;
                html += `<div class="test-result info">- Event listeners: ${hasEventListeners}</div>`;
                
                log(`Button ${index + 1} (${lang}) event handlers checked`);
                
                // Try to simulate click (without actually clicking)
                try {
                    const clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    
                    // Don't actually dispatch, just test if we can create the event
                    html += `<div class="test-result success">- Can create click event: ✅</div>`;
                    log(`Button ${index + 1} click event creation test passed`);
                } catch (error) {
                    html += `<div class="test-result error">- Click event creation failed: ${error.message}</div>`;
                    log(`ERROR: Button ${index + 1} click event creation failed: ${error.message}`, 'error');
                }
            });

            result.innerHTML = html;
        }

        function testTranslationIntegration() {
            log('Starting translation integration test...');
            const result = document.getElementById('translation-integration-result');
            let html = '<h3>Translation Integration Test:</h3>';

            if (!window.i18n || !window.i18n.translate) {
                html += '<div class="test-result error">❌ Translation system not available</div>';
                log('ERROR: Translation system not available', 'error');
                result.innerHTML = html;
                return;
            }

            // Test key translations
            const testKeys = [
                'common.english',
                'common.amazigh',
                'common.app_name',
                'common.language',
                'general.language_english',
                'general.language_amazigh'
            ];

            testKeys.forEach(key => {
                try {
                    const translation = window.i18n.translate(key);
                    if (translation && translation !== key) {
                        html += `<div class="test-result success">✅ ${key}: "${translation}"</div>`;
                        log(`Translation test passed for ${key}: "${translation}"`);
                    } else {
                        html += `<div class="test-result warning">⚠️ ${key}: No translation (returned: "${translation}")</div>`;
                        log(`WARNING: No translation for ${key}`, 'warning');
                    }
                } catch (error) {
                    html += `<div class="test-result error">❌ ${key}: Error - ${error.message}</div>`;
                    log(`ERROR: Translation test failed for ${key}: ${error.message}`, 'error');
                }
            });

            // Test updateUI function
            if (typeof window.i18n.updateUI === 'function') {
                html += '<div class="test-result success">✅ updateUI function available</div>';
                log('updateUI function is available');
            } else {
                html += '<div class="test-result error">❌ updateUI function not available</div>';
                log('ERROR: updateUI function not available', 'error');
            }

            result.innerHTML = html;
        }

        function manualSwitchTest(targetLang) {
            log(`Starting manual switch test to ${targetLang}...`);
            const result = document.getElementById('manual-switch-result');
            
            if (!window.languageSwitcher) {
                result.innerHTML = '<div class="test-result error">❌ Language switcher not available</div>';
                log('ERROR: Language switcher not available for manual test', 'error');
                return;
            }

            try {
                const oldLang = window.languageSwitcher.getCurrentLanguage();
                const success = window.languageSwitcher.switchLanguage(targetLang);
                const newLang = window.languageSwitcher.getCurrentLanguage();
                
                let html = `<div class="test-result info">Switch attempt from "${oldLang}" to "${targetLang}"</div>`;
                
                if (success) {
                    html += `<div class="test-result success">✅ Switch successful. Current language: "${newLang}"</div>`;
                    log(`Manual switch to ${targetLang} successful. Current: ${newLang}`);
                } else {
                    html += `<div class="test-result error">❌ Switch failed. Current language: "${newLang}"</div>`;
                    log(`ERROR: Manual switch to ${targetLang} failed`, 'error');
                }
                
                result.innerHTML = html;
                
                // Update other test results to reflect changes
                setTimeout(() => {
                    checkSystemComponents();
                    analyzeHTMLStructure();
                }, 100);
                
            } catch (error) {
                result.innerHTML = `<div class="test-result error">❌ Switch error: ${error.message}</div>`;
                log(`ERROR: Manual switch to ${targetLang} threw error: ${error.message}`, 'error');
            }
        }

        function startErrorMonitoring() {
            log('Starting error monitoring...');
            errorMonitoring = true;
            
            // Override console.error to capture errors
            console.error = function(...args) {
                log(`CONSOLE ERROR: ${args.join(' ')}`, 'error');
                originalConsoleError.apply(console, args);
            };
            
            // Override console.log to capture logs
            console.log = function(...args) {
                if (args[0] && args[0].includes && args[0].includes('[LanguageSwitcher]')) {
                    log(`LANGUAGE SWITCHER: ${args.join(' ')}`, 'info');
                }
                originalConsoleLog.apply(console, args);
            };
            
            // Listen for unhandled errors
            window.addEventListener('error', function(event) {
                log(`UNHANDLED ERROR: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
            });
            
            document.getElementById('error-monitor-result').innerHTML = 
                '<div class="test-result success">✅ Error monitoring started</div>';
        }

        function stopErrorMonitoring() {
            log('Stopping error monitoring...');
            errorMonitoring = false;
            
            // Restore original console functions
            console.error = originalConsoleError;
            console.log = originalConsoleLog;
            
            document.getElementById('error-monitor-result').innerHTML = 
                '<div class="test-result info">ℹ️ Error monitoring stopped</div>';
        }

        function clearConsole() {
            debugLog = [];
            updateLiveConsole();
            log('Console cleared');
        }

        // Auto-start monitoring and initial checks
        document.addEventListener('DOMContentLoaded', function() {
            log('Debug tool loaded');
            startErrorMonitoring();
            
            setTimeout(() => {
                log('Running initial system check...');
                checkSystemComponents();
            }, 1000);
        });
    </script>
</body>
</html>
