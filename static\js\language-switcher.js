/**
 * Clean Language Switcher Implementation
 * Built from scratch for reliable language switching functionality
 */

class LanguageSwitcher {
    constructor() {
        this.isInitialized = false;
        this.debug = true; // Enable debugging
        this.retryCount = 0;
        this.maxRetries = 30; // 3 seconds max wait
        
        this.log('🌐 Language Switcher initialized');
        this.init();
    }
    
    log(message, data = null) {
        if (this.debug) {
            console.log(`[LanguageSwitcher] ${message}`, data || '');
        }
    }
    
    error(message, error = null) {
        console.error(`[LanguageSwitcher] ❌ ${message}`, error || '');
    }
    
    /**
     * Initialize the language switcher
     */
    init() {
        this.log('Starting initialization...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.waitForTranslationSystem());
        } else {
            this.waitForTranslationSystem();
        }
    }
    
    /**
     * Wait for translation system to be ready
     */
    waitForTranslationSystem() {
        this.retryCount++;
        this.log(`Waiting for translation system... (attempt ${this.retryCount})`);

        if (this.retryCount > this.maxRetries) {
            this.error('Max retries reached. Translation system not available.');
            // Try one more time with DOM check
            this.log('Attempting final setup with DOM check...');
            setTimeout(() => this.setupLanguageButtons(), 500);
            return;
        }

        // Check if translation system is ready
        if (window.i18n && window.i18n.isLoaded && typeof window.i18n.setLanguage === 'function') {
            this.log('✅ Translation system is ready');
            // Add extra delay to ensure DOM is fully loaded
            setTimeout(() => this.setupLanguageButtons(), 200);
        } else {
            this.log(`Translation system status: i18n=${!!window.i18n}, isLoaded=${window.i18n?.isLoaded}, setLanguage=${typeof window.i18n?.setLanguage}`);
            // Retry after 100ms
            setTimeout(() => this.waitForTranslationSystem(), 100);
        }
    }
    
    /**
     * Setup language buttons event handlers
     */
    setupLanguageButtons() {
        if (this.isInitialized) {
            this.log('Already initialized, skipping...');
            return;
        }

        this.log('Setting up language buttons...');

        // Wait for DOM to be fully ready
        if (document.readyState !== 'complete') {
            this.log('DOM not ready, waiting...');
            setTimeout(() => this.setupLanguageButtons(), 100);
            return;
        }

        // Find all language buttons with multiple selectors
        let languageButtons = document.querySelectorAll('.lang-btn');

        // If not found, try alternative selectors
        if (languageButtons.length === 0) {
            this.log('No .lang-btn found, trying alternative selectors...');
            languageButtons = document.querySelectorAll('[data-lang]');
        }

        if (languageButtons.length === 0) {
            this.log('No language buttons found, trying dropdown items...');
            languageButtons = document.querySelectorAll('.dropdown-item[data-lang]');
        }

        this.log(`Found ${languageButtons.length} language buttons`);

        if (languageButtons.length === 0) {
            this.error('No language buttons found with any selector');
            // Log DOM structure for debugging
            const languageSelector = document.querySelector('.language-selector');
            if (languageSelector) {
                this.log('Language selector found, innerHTML:', languageSelector.innerHTML);
            } else {
                this.log('Language selector not found');
            }
            return;
        }

        // Add event listeners to each button
        languageButtons.forEach((button, index) => {
            const lang = button.getAttribute('data-lang');
            this.log(`Setting up button ${index + 1}: ${lang}`);

            // Remove any existing event listeners by cloning
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add click event listener
            newButton.addEventListener('click', (e) => this.handleLanguageClick(e, lang));
        });

        // Load saved language preference
        this.loadSavedLanguage();

        // Update button states for current language
        if (window.i18n && window.i18n.currentLang) {
            this.updateButtonStates(window.i18n.currentLang);
        }

        this.isInitialized = true;
        this.log('✅ Language switcher setup complete');
    }
    
    /**
     * Handle language button click
     */
    handleLanguageClick(event, lang) {
        event.preventDefault();
        event.stopPropagation();

        this.log(`Language button clicked: ${lang}`);

        if (!lang) {
            this.error('No language specified');
            return;
        }

        if (!window.i18n || !window.i18n.setLanguage) {
            this.error('Translation system not available');
            return;
        }

        try {
            // Change language in i18n system
            const oldLang = window.i18n.currentLang;
            window.i18n.setLanguage(lang);

            this.log(`Language changed from ${oldLang} to ${lang}`);

            // Update server session
            this.updateServerLanguage(lang);

            // Update button states
            this.updateButtonStates(lang);

            // Update dropdown text
            this.updateDropdownText(event.target);

            // Save preference
            this.saveLanguagePreference(lang);

            this.log('✅ Language switch completed successfully');

        } catch (error) {
            this.error('Failed to switch language', error);
        }
    }
    
    /**
     * Update button states to reflect current language
     */
    updateButtonStates(currentLang) {
        this.log(`Updating button states for language: ${currentLang}`);
        
        const languageButtons = document.querySelectorAll('.lang-btn');
        
        languageButtons.forEach(button => {
            const buttonLang = button.getAttribute('data-lang');
            const checkIcon = button.querySelector('.bi-check2');
            
            // Remove active state from all buttons
            button.classList.remove('active');
            button.removeAttribute('aria-current');
            
            if (checkIcon) {
                if (buttonLang === currentLang) {
                    // Show check icon for current language
                    checkIcon.classList.remove('invisible');
                    button.classList.add('active');
                    button.setAttribute('aria-current', 'true');
                    this.log(`Activated button for language: ${buttonLang}`);
                } else {
                    // Hide check icon for other languages
                    checkIcon.classList.add('invisible');
                }
            }
        });
    }
    
    /**
     * Update dropdown button text
     */
    updateDropdownText(clickedButton) {
        const currentLanguageDisplay = document.querySelector('.current-language');
        
        if (!currentLanguageDisplay) {
            this.log('Current language display element not found');
            return;
        }
        
        // Find the text span in the clicked button
        const textSpan = clickedButton.querySelector('span:last-child') || 
                        clickedButton.closest('.lang-btn').querySelector('span:last-child');
        
        if (textSpan) {
            // Clone the span to preserve all attributes and content
            const clonedSpan = textSpan.cloneNode(true);
            currentLanguageDisplay.innerHTML = '';
            currentLanguageDisplay.appendChild(clonedSpan);
            
            this.log('Updated dropdown text');
        } else {
            this.log('Text span not found in clicked button');
        }
    }
    
    /**
     * Update language on server
     */
    updateServerLanguage(lang) {
        try {
            fetch('/set-language', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ language: lang })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.log(`Server language updated to: ${lang}`);
                } else {
                    this.error(`Failed to update server language: ${data.error}`);
                }
            })
            .catch(error => {
                this.error('Network error updating server language', error);
            });
        } catch (error) {
            this.error('Failed to update server language', error);
        }
    }

    /**
     * Save language preference to localStorage
     */
    saveLanguagePreference(lang) {
        try {
            localStorage.setItem('selectedLanguage', lang);
            localStorage.setItem('preferred_language', lang);
            this.log(`Saved language preference: ${lang}`);
        } catch (error) {
            this.error('Failed to save language preference', error);
        }
    }
    
    /**
     * Load saved language preference
     */
    loadSavedLanguage() {
        try {
            const savedLang = localStorage.getItem('selectedLanguage') || 
                             localStorage.getItem('preferred_language');
            
            if (savedLang && savedLang !== window.i18n.currentLang) {
                this.log(`Loading saved language: ${savedLang}`);
                window.i18n.setLanguage(savedLang);
                this.updateButtonStates(savedLang);
                
                // Update dropdown text for saved language
                const savedLangButton = document.querySelector(`.lang-btn[data-lang="${savedLang}"]`);
                if (savedLangButton) {
                    this.updateDropdownText(savedLangButton);
                }
            } else if (savedLang) {
                this.log(`Current language matches saved language: ${savedLang}`);
            } else {
                this.log('No saved language preference found');
            }
        } catch (error) {
            this.error('Failed to load saved language', error);
        }
    }
    
    /**
     * Public method to manually switch language (for testing)
     */
    switchLanguage(lang) {
        this.log(`Manual language switch requested: ${lang}`);
        
        if (!window.i18n || !window.i18n.setLanguage) {
            this.error('Translation system not available for manual switch');
            return false;
        }
        
        try {
            window.i18n.setLanguage(lang);
            this.updateButtonStates(lang);
            this.saveLanguagePreference(lang);
            
            // Update dropdown text
            const langButton = document.querySelector(`.lang-btn[data-lang="${lang}"]`);
            if (langButton) {
                this.updateDropdownText(langButton);
            }
            
            this.log(`✅ Manual language switch to ${lang} completed`);
            return true;
        } catch (error) {
            this.error('Manual language switch failed', error);
            return false;
        }
    }
    
    /**
     * Get current language
     */
    getCurrentLanguage() {
        return window.i18n ? window.i18n.currentLang : null;
    }
    
    /**
     * Check if system is ready
     */
    isReady() {
        return this.isInitialized && window.i18n && window.i18n.isLoaded;
    }
}

// Initialize the language switcher
const languageSwitcher = new LanguageSwitcher();

// Expose for testing and debugging
window.languageSwitcher = languageSwitcher;

// Expose convenient test functions
window.switchToEnglish = () => languageSwitcher.switchLanguage('en');
window.switchToAmazigh = () => languageSwitcher.switchLanguage('am');
window.getCurrentLang = () => languageSwitcher.getCurrentLanguage();

// Add additional initialization attempts
window.addEventListener('load', function() {
    console.log('🌐 Window load event - attempting language switcher setup');
    setTimeout(() => {
        if (!languageSwitcher.isInitialized) {
            console.log('🌐 Language switcher not initialized, forcing setup...');
            languageSwitcher.setupLanguageButtons();
        }
    }, 1000);
});

// Listen for i18n system events
document.addEventListener('languageSystemLoaded', function() {
    console.log('🌐 Language system loaded event received');
    setTimeout(() => {
        if (!languageSwitcher.isInitialized) {
            console.log('🌐 Setting up language switcher after i18n loaded...');
            languageSwitcher.setupLanguageButtons();
        }
    }, 500);
});

console.log('🌐 Language Switcher module loaded - v4.0.0');
if (window.jsLoadingDebug) {
    window.jsLoadingDebug.log('Language switcher module loaded and objects created');
}
