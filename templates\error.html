{% extends 'base.html' %}

{% block title %}{{ translate('Error', session.get('lang', 'en')) }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow">
                <div class="card-body p-5 text-center">
                    <i class="bi bi-exclamation-triangle text-warning display-1 mb-4"></i>
                    <h1 class="card-title mb-4">{{ translate('Error', session.get('lang', 'en')) }}</h1>
                    <p class="lead mb-4">{{ error }}</p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="bi bi-house-door me-1"></i> {{ translate('Back to Home', session.get('lang', 'en')) }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
