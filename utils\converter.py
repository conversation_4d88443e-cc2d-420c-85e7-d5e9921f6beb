"""
محول النصوص من الحروف اللاتينية إلى حروف تيفيناغ.

هذا الملف يوفر وظائف لتحويل النصوص المكتوبة بالحروف اللاتينية إلى حروف تيفيناغ الأمازيغية.
يدعم تحويل النصوص البسيطة والنصوص المعقدة التي تحتوي على جداول وعلامات ترقيم وأنماط مختلفة.

الوظائف الرئيسية:
- تحويل النصوص العادية
- تحويل النصوص التي تحتوي على جداول HTML أو Markdown
- معالجة خاصة لحرف "e" حسب قواعد اللغة الأمازيغية
- الحفاظ على تنسيق النص الأصلي

الاستخدام:
    من utils.converter import latin_to_tifinagh

    نص_تيفيناغ = latin_to_tifinagh(نص_لاتيني)
"""

class TifinaghConverter:
    """
    صف لتحويل النصوص من الحروف اللاتينية إلى حروف تيفيناغ.

    يوفر هذا الصف وظائف لتحويل النصوص المكتوبة بالحروف اللاتينية إلى حروف تيفيناغ الأمازيغية.
    يدعم تحويل النصوص البسيطة والنصوص المعقدة التي تحتوي على جداول وعلامات ترقيم وأنماط مختلفة.

    الخصائص:
        mapping (dict): قاموس يحتوي على تعيين الحروف اللاتينية إلى حروف تيفيناغ المقابلة.
        always_keep_e_words (list): قائمة بالكلمات التي يجب الاحتفاظ بحرف "e" فيها دائمًا.
        always_remove_e_words (list): قائمة بالكلمات التي يجب حذف حرف "e" منها دائمًا.
        vowels (list): قائمة بالحروف المتحركة.
        consonants (list): قائمة بالحروف الساكنة.
    """

    def __init__(self):
        """
        تهيئة محول النصوص من الحروف اللاتينية إلى حروف تيفيناغ.

        يقوم بإعداد قواميس التعيين وقوائم الكلمات الخاصة والحروف المتحركة والساكنة.
        """
        # قائمة بالكلمات التي يجب الاحتفاظ بحرف "e" فيها دائمًا
        self.always_keep_e_words = [
            'amen', 'tamen', 'awen', 'tawen', 'imen', 'timen', 'iwen', 'tiwen',
            'isen', 'tisen', 'asen', 'tasen', 'iden', 'tiden', 'aden', 'taden',
            'agen', 'tagen', 'igen', 'tigen', 'ahen', 'tahen', 'ihen', 'tihen',
            'alen', 'talen', 'ilen', 'tilen', 'anen', 'tanen', 'inen', 'tinen',
            'aren', 'taren', 'iren', 'tiren', 'afen', 'tafen', 'ifen', 'tifen',
            'aɣen', 'taɣen', 'iɣen', 'tiɣen'
        ]

        # قائمة بالكلمات التي يجب حذف حرف "e" منها دائمًا
        self.always_remove_e_words = [
            'amun', 'tamunt', 'imun', 'timun', 'asun', 'tasunt', 'isun', 'tisun',
            'adun', 'tadunt', 'idun', 'tidun', 'arun', 'tarunt', 'irun', 'tirun',
            'afun', 'tafunt', 'ifun', 'tifun', 'aɣun', 'taɣunt', 'iɣun', 'tiɣun'
        ]

        # الحروف المتحركة (vowels)
        self.vowels = ['a', 'i', 'u', 'e', 'o']

        # الحروف الساكنة (consonants)
        self.consonants = [
            'b', 'c', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'v', 'w', 'x', 'y', 'z',
            'ɣ', 'ḥ', 'ɛ', 'ε', 'ḍ', 'ṣ', 'ṭ', 'ẓ', 'š', 'ḇ', 'ṯ', 'ḏ', 'ṛ'
        ]

        self.mapping = {
            'a': 'ⴰ',  # TIFINAGH LETTER YA (U+2D30)
            'b': 'ⴱ',  # TIFINAGH LETTER YAB (U+2D31)
            'c': 'ⵛ',  # TIFINAGH LETTER YACH (U+2D5B)
            'd': 'ⴷ',  # TIFINAGH LETTER YAD (U+2D37)
            'e': 'ⴻ',  # TIFINAGH LETTER YE (U+2D3B)
            'f': 'ⴼ',  # TIFINAGH LETTER YAF (U+2D3C)
            'g': 'ⴳ',  # TIFINAGH LETTER YAG (U+2D33)
            'h': 'ⵀ',  # TIFINAGH LETTER YAH (U+2D40)
            'i': 'ⵉ',  # TIFINAGH LETTER YI (U+2D49)
            'j': 'ⵊ',  # TIFINAGH LETTER YAJ (U+2D4A)
            'k': 'ⴽ',  # TIFINAGH LETTER YAK (U+2D3D)
            'l': 'ⵍ',  # TIFINAGH LETTER YAL (U+2D4D)
            'm': 'ⵎ',  # TIFINAGH LETTER YAM (U+2D4E)
            'n': 'ⵏ',  # TIFINAGH LETTER YAN (U+2D4F)
            'o': 'ⵓ',  # TIFINAGH LETTER YU (U+2D53) - used for 'o'
            'p': 'ⵃ',  # TIFINAGH LETTER YAH (U+2D43) - as specified in the new mapping
            'q': 'ⵇ',  # TIFINAGH LETTER YAQ (U+2D47)
            'r': 'ⵔ',  # TIFINAGH LETTER YAR (U+2D54)
            's': 'ⵙ',  # TIFINAGH LETTER YAS (U+2D59)
            't': 'ⵜ',  # TIFINAGH LETTER YAT (U+2D5C)
            'u': 'ⵓ',  # TIFINAGH LETTER YU (U+2D53)
            'v': 'ⵖ',  # TIFINAGH LETTER YAGHH (U+2D56) - approximation for 'v'
            'w': 'ⵡ',  # TIFINAGH LETTER YAW (U+2D61)
            'x': 'ⵅ',  # TIFINAGH LETTER YAKH (U+2D45) - approximation for 'x'
            'y': 'ⵢ',  # TIFINAGH LETTER YAY (U+2D62)
            'z': 'ⵣ',  # TIFINAGH LETTER YAZ (U+2D63)
            # Special characters
            'ɣ': 'ⵖ',  # TIFINAGH LETTER YAGHH (U+2D56)
            'ḥ': 'ⵃ',  # TIFINAGH LETTER YAH (U+2D43)
            'ɛ': 'ⵄ',  # TIFINAGH LETTER YAƐ (U+2D44)
            'ε': 'ⵄ',  # TIFINAGH LETTER YAƐ (U+2D44) - alternative
            'ḍ': 'ⴹ',  # TIFINAGH LETTER YAḌ (U+2D39)
            'ṣ': 'ⵚ',  # TIFINAGH LETTER YAṢ (U+2D5A)
            'ṭ': 'ⵟ',  # TIFINAGH LETTER YAṬ (U+2D5F)
            'ẓ': 'ⵥ',  # TIFINAGH LETTER YAZH (U+2D65)
            # Additional special characters
            'kˇ': 'ⴽⵯ',  # TIFINAGH LETTER YAK with LABIALIZATION MARK
            'gˇ': 'ⴳⵯ',  # TIFINAGH LETTER YAG with LABIALIZATION MARK
            'dj': 'ⴷⵊ',  # TIFINAGH LETTER YAD + TIFINAGH LETTER YAJ
            'tc': 'ⵜⵛ',  # TIFINAGH LETTER YAT + TIFINAGH LETTER YACH
            'ḇ': 'ⴲ',   # TIFINAGH LETTER YAB with DOT BELOW
            '†': 'ⴿ',   # TIFINAGH LETTER YAKHH
            'ṯ': 'ⵝ',   # TIFINAGH LETTER YAT with DOT BELOW
            'ḏ': 'ⴸ',   # TIFINAGH LETTER YAD with DOT BELOW
            'ṛ': 'ⵕ',   # TIFINAGH LETTER YARR
            'š': 'ⴴ',   # TIFINAGH LETTER YAHH
            'ã': 'ⵚ',   # TIFINAGH LETTER YAṢ - alternative
            'ï': 'ⵟ',   # TIFINAGH LETTER YAṬ - alternative
            'ä': 'ⴹ',   # TIFINAGH LETTER YAḌ - alternative
            'ç': 'ⵥ',   # TIFINAGH LETTER YAZH - alternative
            'å': 'ⵯ',   # TIFINAGH MODIFIER LETTER LABIALIZATION MARK
            'œ': 'ⵕ',   # TIFINAGH LETTER YARR - alternative
            'ø': 'ⵖ',   # TIFINAGH LETTER YAGHH - alternative
            'ë': 'ⵕ',   # TIFINAGH LETTER YARR - alternative
            # Uppercase variants (same as lowercase in Tifinagh)
            'A': 'ⴰ', 'B': 'ⴱ', 'C': 'ⵛ', 'D': 'ⴷ', 'E': 'ⴻ',
            'F': 'ⴼ', 'G': 'ⴳ', 'H': 'ⵀ', 'I': 'ⵉ', 'J': 'ⵊ',
            'K': 'ⴽ', 'L': 'ⵍ', 'M': 'ⵎ', 'N': 'ⵏ', 'O': 'ⵓ',
            'P': 'ⵒ', 'Q': 'ⵇ', 'R': 'ⵔ', 'S': 'ⵙ', 'T': 'ⵜ',
            'U': 'ⵓ', 'V': 'ⵖ', 'W': 'ⵡ', 'X': 'ⵅ', 'Y': 'ⵢ',
            'Z': 'ⵣ',
            # Additional uppercase variants
            'Ṛ': 'ⵕ', 'Ɣ': 'ⵖ', 'Ḥ': 'ⵃ',
            # Punctuation and spaces remain the same
            ' ': ' ', '.': '.', ',': ',', ';': ';', ':': ':',
            '!': '!', '?': '?', '"': '"', "'": "'", '(': '(',
            ')': ')', '[': '[', ']': ']', '{': '{', '}': '}',
            '-': '-', '_': '_', '+': '+', '=': '=', '/': '/',
            '\\': '\\', '@': '@', '#': '#', '$': '$', '%': '%',
            '^': '^', '&': '&', '*': '*', '<': '<', '>': '>',
            '|': '|', '~': '~', '`': '`'
        }

    def convert(self, text: str) -> str:
        """
        تحويل النص من الأحرف اللاتينية إلى أحرف تيفيناغ.

        Args:
            text (str): النص المراد تحويله

        Returns:
            str: النص المحول إلى أحرف تيفيناغ
        """
        if not text:
            return ""

        # التحقق من وجود جداول في النص
        import re

        # البحث عن أنماط الجداول (HTML أو Markdown)
        # نمط أكثر شمولاً لجداول HTML
        html_table_pattern = r'<table[^>]*>.*?</table>'

        # أنماط محسنة لجداول Markdown
        # نمط 1: جداول Markdown القياسية مع سطر فاصل
        markdown_table_pattern = r'\|[^\n]*\|[ \t]*\n\|[ :|\-]+\|[ \t]*\n(\|[^\n]*\|[ \t]*\n)+'

        # نمط 2: جداول Markdown بدون سطر فاصل
        alt_markdown_table_pattern = r'\|[^\n]*\|[ \t]*\n(\|[^\n]*\|[ \t]*\n)+'

        # نمط 3: جداول Markdown غير منتظمة (عدد مختلف من الأعمدة)
        irregular_markdown_table_pattern = r'(\|[^\n]*\n)+'

        # تحقق من وجود جداول HTML
        html_tables = re.findall(html_table_pattern, text, re.DOTALL)

        # تحقق من وجود جداول Markdown
        markdown_tables = re.findall(markdown_table_pattern, text, re.DOTALL)

        # تحقق من وجود جداول Markdown بدون سطر فاصل
        alt_markdown_tables = re.findall(alt_markdown_table_pattern, text, re.DOTALL)

        # تحقق من وجود جداول Markdown غير منتظمة
        irregular_markdown_tables = re.findall(irregular_markdown_table_pattern, text, re.DOTALL)

        # دمج جميع جداول Markdown
        markdown_tables.extend(alt_markdown_tables)
        markdown_tables.extend(irregular_markdown_tables)

        # تحويل جداول Markdown إلى HTML إذا وجدت
        for i, md_table in enumerate(markdown_tables):
            try:
                # تقسيم الجدول إلى أسطر
                lines = md_table.strip().split('\n')

                # تجاهل الجداول غير الصالحة
                if len(lines) < 2:
                    continue

                # إنشاء جدول HTML
                html_table = '<table border="1" cellpadding="5" cellspacing="0" class="converted-table">\n'

                # تحديد ما إذا كان الجدول يحتوي على سطر فاصل
                has_separator = False
                separator_index = -1

                # تحسين اكتشاف سطر الفاصل
                for j, line in enumerate(lines):
                    # تحقق من وجود سطر فاصل (يحتوي على - و : فقط)
                    if re.match(r'^\s*\|[\s\-:|]+\|\s*$', line) and '-' in line:
                        has_separator = True
                        separator_index = j
                        break

                # تحديد عدد الأعمدة المتوقع (من السطر الأول أو سطر الفاصل)
                expected_columns = 0
                if has_separator:
                    # استخدام سطر الفاصل لتحديد عدد الأعمدة
                    separator_cells = lines[separator_index].strip('|').split('|')
                    expected_columns = len(separator_cells)
                elif len(lines) > 0:
                    # استخدام السطر الأول لتحديد عدد الأعمدة
                    first_row_cells = lines[0].strip('|').split('|')
                    expected_columns = len(first_row_cells)

                if has_separator:
                    # جدول مع سطر فاصل - الصف الأول هو العناوين
                    # معالجة الصف الأول (العناوين)
                    if separator_index > 0:
                        headers = lines[0].strip('|').split('|')
                        html_table += '<thead>\n<tr>\n'

                        # التأكد من أن عدد العناوين يتطابق مع عدد الأعمدة المتوقع
                        while len(headers) < expected_columns:
                            headers.append('')  # إضافة خلايا فارغة إذا كان هناك نقص

                        # اقتصار العناوين على العدد المتوقع إذا كان هناك زيادة
                        headers = headers[:expected_columns]

                        for header in headers:
                            html_table += f'<th>{header.strip()}</th>\n'
                        html_table += '</tr>\n</thead>\n'

                    # معالجة الصفوف بعد سطر الفاصل
                    if separator_index >= 0 and len(lines) > separator_index + 1:
                        html_table += '<tbody>\n'
                        for line in lines[separator_index + 1:]:
                            # تجاهل الأسطر الفارغة
                            if not line.strip():
                                continue

                            cells = line.strip('|').split('|')

                            # التأكد من أن عدد الخلايا يتطابق مع عدد الأعمدة المتوقع
                            while len(cells) < expected_columns:
                                cells.append('')  # إضافة خلايا فارغة إذا كان هناك نقص

                            # اقتصار الخلايا على العدد المتوقع إذا كان هناك زيادة
                            cells = cells[:expected_columns]

                            html_table += '<tr>\n'
                            for cell in cells:
                                html_table += f'<td>{cell.strip()}</td>\n'
                            html_table += '</tr>\n'
                        html_table += '</tbody>\n'
                else:
                    # جدول بدون سطر فاصل - كل الصفوف هي بيانات
                    html_table += '<tbody>\n'
                    for line in lines:
                        # تجاهل الأسطر الفارغة
                        if not line.strip():
                            continue

                        cells = line.strip('|').split('|')

                        # التأكد من أن عدد الخلايا يتطابق مع عدد الأعمدة المتوقع
                        while len(cells) < expected_columns:
                            cells.append('')  # إضافة خلايا فارغة إذا كان هناك نقص

                        # اقتصار الخلايا على العدد المتوقع إذا كان هناك زيادة
                        cells = cells[:expected_columns]

                        html_table += '<tr>\n'
                        for cell in cells:
                            html_table += f'<td>{cell.strip()}</td>\n'
                        html_table += '</tr>\n'
                    html_table += '</tbody>\n'

                html_table += '</table>'

                # استبدال جدول Markdown بجدول HTML
                text = text.replace(md_table, html_table)

                # إضافة جدول HTML المحول إلى قائمة جداول HTML
                html_tables.append(html_table)
            except Exception as e:
                # في حالة حدوث خطأ، احتفظ بالجدول الأصلي
                print(f"Error converting markdown table: {e}")

        # إذا وجدت جداول، قم بمعالجتها بشكل خاص
        if html_tables or markdown_tables:
            # حفظ الجداول واستبدالها بعلامات مؤقتة
            placeholders = {}

            # معالجة جداول HTML
            for i, table in enumerate(html_tables):
                placeholder = f"__HTML_TABLE_{i}__"
                placeholders[placeholder] = table
                text = text.replace(table, placeholder)

            # معالجة جداول Markdown
            for i, table in enumerate(markdown_tables):
                placeholder = f"__MD_TABLE_{i}__"
                placeholders[placeholder] = table
                text = text.replace(table, placeholder)

            # تحويل النص بدون الجداول
            # تقسيم النص إلى كلمات للتعامل مع حالات خاصة
            words = re.findall(r'\S+|\s+', text)
            processed_words = []

            for word in words:
                # التحقق مما إذا كانت الكلمة علامة مؤقتة للجدول
                if word in placeholders:
                    # إعادة الجدول كما هو
                    processed_words.append(placeholders[word])
                # إذا كانت الكلمة مجرد مسافات، نحتفظ بها كما هي
                elif word.isspace():
                    processed_words.append(word)
                else:
                    # معالجة كل كلمة على حدة
                    processed_word = self._process_word(word)
                    processed_words.append(processed_word)

            # إعادة تجميع النص بعد المعالجة
            return ''.join(processed_words)
        else:
            # إذا لم توجد جداول، استخدم المعالجة العادية
            # تقسيم النص إلى كلمات للتعامل مع حالات خاصة
            words = re.findall(r'\S+|\s+', text)
            processed_words = []

            for word in words:
                # إذا كانت الكلمة مجرد مسافات، نحتفظ بها كما هي
                if word.isspace():
                    processed_words.append(word)
                else:
                    # معالجة كل كلمة على حدة
                    processed_word = self._process_word(word)
                    processed_words.append(processed_word)

            # إعادة تجميع النص بعد المعالجة
            return ''.join(processed_words)

    def _process_word(self, word: str) -> str:
        """
        معالجة كلمة واحدة وتطبيق قواعد التحويل عليها.

        Args:
            word (str): الكلمة المراد معالجتها

        Returns:
            str: الكلمة بعد المعالجة
        """
        if not word:
            return ""

        # تنظيف الكلمة من علامات الترقيم في البداية والنهاية للتعرف عليها بشكل صحيح
        # نحتفظ بالعلامات لإعادتها لاحقًا
        prefix = ""
        suffix = ""
        clean_word = word

        # استخراج علامات الترقيم من بداية الكلمة
        i = 0
        while i < len(clean_word) and not clean_word[i].isalnum():
            prefix += clean_word[i]
            i += 1
        clean_word = clean_word[i:]

        # استخراج علامات الترقيم من نهاية الكلمة
        i = len(clean_word) - 1
        while i >= 0 and not clean_word[i].isalnum():
            suffix = clean_word[i] + suffix
            i -= 1
        clean_word = clean_word[:i+1] if i >= 0 else ""

        # إذا كانت الكلمة فارغة بعد التنظيف، نعيد العلامات كما هي
        if not clean_word:
            return prefix + suffix

        # التحقق من الكلمات الخاصة قبل معالجة الأحرف
        current_word_lower = clean_word.lower()

        # التحويل الأساسي باستخدام قاموس التعيين
        result = []
        for i, char in enumerate(clean_word):
            # إذا كان الحرف هو "e"، نطبق قواعد خاصة
            if char.lower() == 'e':
                # الحصول على الأحرف المحيطة
                prev_char = clean_word[i-1].lower() if i > 0 else ''
                next_char = clean_word[i+1].lower() if i < len(clean_word) - 1 else ''
                prev_prev_char = clean_word[i-2].lower() if i > 1 else ''
                next_next_char = clean_word[i+2].lower() if i < len(clean_word) - 2 else ''

                # في البداية، نفترض أننا سنحذف الحرف "e"
                e_result = ''

                # إذا كانت الكلمة في قائمة الكلمات التي يجب الاحتفاظ بحرف "e" فيها دائمًا
                if current_word_lower in self.always_keep_e_words:
                    e_result = "ⴻ"
                # إذا كانت الكلمة في قائمة الكلمات التي يجب حذف حرف "e" منها دائمًا
                elif current_word_lower in self.always_remove_e_words:
                    e_result = ''
                # إذا لم تكن الكلمة في أي من القائمتين، نطبق القواعد العامة
                else:
                    # إذا كان الحرف "e" في بداية الكلمة، نحتفظ به
                    if i == 0:
                        e_result = "ⴻ"
                    # إذا كان الحرف "e" في نهاية الكلمة، نحتفظ به
                    elif i == len(clean_word) - 1:
                        e_result = "ⴻ"
                    # إذا كان الحرف "e" بين حرفين صامتين متطابقين
                    elif prev_char == next_char and prev_char != 'e':
                        e_result = "ⴻ"
                    # إذا كان الحرف "e" بين ثلاثة صوامت متطابقة
                    elif ((prev_prev_char == prev_char and prev_char == next_char and prev_char != 'e') or
                          (prev_char == next_char and next_char == next_next_char and prev_char != 'e')):
                        e_result = "ⴻ"
                    # إذا كان الحرف "e" في وسط أربعة حروف، اثنين منهم متماثلين، والآخرين مختلفين ولكنهم متماثلين
                    # مثل: "ttedd"، "qqenn"، "tteqq"، يتم حذف "e"
                    elif ((prev_prev_char == prev_char and next_char == next_next_char and prev_char != next_char) or
                          (prev_prev_char == next_char and prev_char == next_next_char and prev_prev_char != prev_char)):
                        e_result = '' # يتم حذف الحرف "e"
                    # إذا جاء في وسط أربعة حروف من حرف "t"، لا يتم حذفه
                    elif prev_prev_char == 't' and prev_char == 't' and next_char == 't' and next_next_char == 't':
                        e_result = "ⴻ"
                    # إذا كان الحرف "e" بعد حرف متحرك، نحتفظ به
                    elif prev_char in self.vowels:
                        e_result = "ⴻ"
                    # إذا كان الحرف "e" قبل حرف متحرك، نحتفظ به
                    elif next_char in self.vowels:
                        e_result = "ⴻ"
                    # إذا كان الحرف "e" بين حرفين ساكنين مختلفين، نحتفظ به في بعض الحالات
                    elif prev_char in self.consonants and next_char in self.consonants and prev_char != next_char:
                        # قواعد إضافية للحالات الخاصة
                        if (prev_char in 'bcdfghjklmnpqrstvwxyz' and next_char in 'bcdfghjklmnpqrstvwxyz'):
                            # إذا كان بين حرفين ساكنين صعب نطقهما معًا، نحتفظ بحرف "e"
                            difficult_combinations = ['bg', 'bj', 'bk', 'bp', 'bq', 'bt', 'bv', 'bz',
                                                    'cg', 'cj', 'ck', 'cp', 'cq', 'ct', 'cv', 'cz',
                                                    'db', 'dc', 'dg', 'dj', 'dk', 'dp', 'dq', 'dt', 'dv', 'dz',
                                                    'fb', 'fc', 'fd', 'fg', 'fj', 'fk', 'fp', 'fq', 'ft', 'fv', 'fz',
                                                    'gb', 'gc', 'gd', 'gf', 'gj', 'gk', 'gp', 'gq', 'gt', 'gv', 'gz',
                                                    'jb', 'jc', 'jd', 'jf', 'jg', 'jk', 'jp', 'jq', 'jt', 'jv', 'jz',
                                                    'kb', 'kc', 'kd', 'kf', 'kg', 'kj', 'kp', 'kq', 'kt', 'kv', 'kz',
                                                    'pb', 'pc', 'pd', 'pf', 'pg', 'pj', 'pk', 'pq', 'pt', 'pv', 'pz',
                                                    'qb', 'qc', 'qd', 'qf', 'qg', 'qj', 'qk', 'qp', 'qt', 'qv', 'qz',
                                                    'tb', 'tc', 'td', 'tf', 'tg', 'tj', 'tk', 'tp', 'tq', 'tv', 'tz',
                                                    'vb', 'vc', 'vd', 'vf', 'vg', 'vj', 'vk', 'vp', 'vq', 'vt', 'vz',
                                                    'zb', 'zc', 'zd', 'zf', 'zg', 'zj', 'zk', 'zp', 'zq', 'zt', 'zv']
                            if prev_char + next_char in difficult_combinations:
                                e_result = "ⴻ"

                result.append(e_result)
            # إذا كان الحرف هو "-" (الشرطة)، نطبق قواعد خاصة
            elif char == '-':
                # التحقق مما إذا كانت الشرطة وسط كلمة (بين حرفين)
                is_between_letters = False

                # التحقق من الحرف السابق (إذا كان موجودًا)
                if i > 0:
                    prev_dash_char = clean_word[i-1]
                    # التحقق مما إذا كان الحرف السابق حرفًا أبجديًا
                    if prev_dash_char.isalpha():
                        # التحقق من الحرف التالي (إذا كان موجودًا)
                        if i < len(clean_word) - 1:
                            next_dash_char = clean_word[i+1]
                            # التحقق مما إذا كان الحرف التالي حرفًا أبجديًا
                            if next_dash_char.isalpha():
                                # الشرطة بين حرفين أبجديين، نحذفها
                                is_between_letters = True

                if is_between_letters:
                    # حذف الشرطة عندما تكون وسط كلمة
                    result.append('')
                else:
                    # الاحتفاظ بالشرطة في الحالات الأخرى
                    result.append('-')
            else:
                # البحث عن الحرف في قاموس التعيين
                tifinagh_char = self.mapping.get(char)

                # إذا كان الحرف موجودًا في القاموس، أضفه إلى النتيجة
                if tifinagh_char is not None:
                    result.append(tifinagh_char)
                else:
                    # إذا كان الحرف غير موجود في القاموس، احتفظ به كما هو
                    result.append(char)

        # إعادة تجميع الكلمة مع العلامات
        return prefix + ''.join(result) + suffix


def latin_to_tifinagh(text: str) -> str:
    """
    تحويل النص اللاتيني إلى نص تيفيناغ.

    Args:
        text (str): النص المراد تحويله

    Returns:
        str: النص المحول إلى أحرف تيفيناغ
    """
    converter = TifinaghConverter()
    return converter.convert(text)
