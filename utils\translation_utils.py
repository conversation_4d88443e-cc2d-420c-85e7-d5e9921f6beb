"""
وحدة مساعدة للتعامل مع الترجمات في التطبيق
"""
import os
import json
import logging
import time
from datetime import datetime

# إعداد التسجيل
logger = logging.getLogger(__name__)

# مسار ملف الترجمات
TRANSLATIONS_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                'data', 'i18n.json')

def load_translations():
    """
    تحميل الترجمات من ملف JSON
    """
    try:
        if os.path.exists(TRANSLATIONS_FILE):
            with open(TRANSLATIONS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            logger.warning(f"ملف الترجمات غير موجود: {TRANSLATIONS_FILE}")
            return {}
    except Exception as e:
        logger.error(f"خطأ في تحميل الترجمات: {e}")
        return {}

def save_translations(translations):
    """
    حفظ الترجمات في ملف JSON
    """
    try:
        # إنشاء مجلد البيانات إذا لم يكن موجودًا
        os.makedirs(os.path.dirname(TRANSLATIONS_FILE), exist_ok=True)
        
        # حفظ الترجمات
        with open(TRANSLATIONS_FILE, 'w', encoding='utf-8') as f:
            json.dump(translations, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        logger.error(f"خطأ في حفظ الترجمات: {e}")
        return False

def save_translation_batch(batch_data):
    """
    حفظ مجموعة من الترجمات دفعة واحدة
    """
    translations = load_translations()
    
    for key, value in batch_data.items():
        if key not in translations:
            translations[key] = {}
        
        for lang, text in value.items():
            translations[key][lang] = text
    
    return save_translations(translations)

def add_translation(key, language, text):
    """
    إضافة أو تحديث ترجمة لمفتاح معين بلغة معينة
    """
    translations = load_translations()
    
    if key not in translations:
        translations[key] = {}
    
    translations[key][language] = text
    
    return save_translations(translations)

def delete_translation(key):
    """
    حذف ترجمة بواسطة المفتاح
    """
    translations = load_translations()
    
    if key in translations:
        del translations[key]
        return save_translations(translations)
    
    return False

def get_translation_statistics():
    """
    الحصول على إحصائيات الترجمة
    """
    translations = load_translations()
    
    languages = set()
    for key, value in translations.items():
        for lang in value.keys():
            languages.add(lang)
    
    stats = {
        'total_keys': len(translations),
        'languages': {}
    }
    
    for lang in languages:
        translated = sum(1 for key, value in translations.items() if lang in value)
        stats['languages'][lang] = {
            'translated': translated,
            'percentage': round((translated / max(1, len(translations))) * 100, 2)
        }
    
    return stats

def get_all_application_texts():
    """
    الحصول على جميع نصوص التطبيق
    """
    return load_translations()

def update_translations_from_extracted_texts(extracted_texts):
    """
    تحديث الترجمات من النصوص المستخرجة
    """
    translations = load_translations()
    
    for key, value in extracted_texts.items():
        if key not in translations:
            translations[key] = value
    
    return save_translations(translations)