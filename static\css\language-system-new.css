/**
 * تصميم نظام تبديل اللغة الجديد
 * New Language System Styles - Clean & Modern
 * Version: 2.0.0
 */

/* ===== متغيرات CSS ===== */
:root {
    --lang-primary: #0d6efd;
    --lang-primary-hover: #0b5ed7;
    --lang-secondary: #6c757d;
    --lang-success: #198754;
    --lang-danger: #dc3545;
    --lang-warning: #ffc107;
    --lang-info: #0dcaf0;
    --lang-light: #f8f9fa;
    --lang-dark: #212529;
    
    --lang-border-radius: 0.375rem;
    --lang-border-radius-lg: 0.5rem;
    --lang-border-radius-pill: 50rem;
    
    --lang-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --lang-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --lang-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    
    --lang-transition: all 0.15s ease-in-out;
    --lang-transition-fast: all 0.1s ease-in-out;
    --lang-transition-slow: all 0.3s ease-in-out;
}

/* ===== الحاوي الأساسي ===== */
.language-selector,
[data-language-system] {
    position: relative;
    display: inline-block;
}

/* ===== زر تبديل اللغة ===== */
.language-selector .dropdown-toggle,
[data-language-system] .dropdown-toggle {
    min-width: 140px;
    border-radius: var(--lang-border-radius-pill);
    transition: var(--lang-transition-slow);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
    border: 1px solid var(--lang-primary);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: var(--lang-primary);
    box-shadow: var(--lang-shadow);
}

.language-selector .dropdown-toggle:hover,
[data-language-system] .dropdown-toggle:hover {
    transform: translateY(-2px);
    box-shadow: var(--lang-shadow-lg);
    border-color: var(--lang-primary-hover);
    background: linear-gradient(135deg, #f8fafc 0%, #e9ecef 100%);
}

.language-selector .dropdown-toggle:focus,
[data-language-system] .dropdown-toggle:focus {
    outline: 2px solid var(--lang-primary);
    outline-offset: 2px;
    box-shadow: var(--lang-shadow-lg);
}

.language-selector .dropdown-toggle:active,
[data-language-system] .dropdown-toggle:active {
    transform: translateY(0);
    box-shadow: var(--lang-shadow-sm);
}

/* ===== حالة التحميل ===== */
.language-selector .dropdown-toggle.loading,
[data-language-system] .dropdown-toggle.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.language-selector .dropdown-toggle.loading::after,
[data-language-system] .dropdown-toggle.loading::after {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid var(--lang-primary);
    border-radius: 50%;
    animation: lang-spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes lang-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== القائمة المنسدلة ===== */
.language-selector .dropdown-menu,
[data-language-system] .dropdown-menu {
    border-radius: var(--lang-border-radius-lg);
    border: none;
    box-shadow: var(--lang-shadow-lg);
    padding: 0.5rem 0;
    min-width: 200px;
    background: white;
    backdrop-filter: blur(10px);
    animation: lang-dropdown-show 0.2s ease-out;
}

@keyframes lang-dropdown-show {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* ===== عناصر القائمة ===== */
.language-selector .dropdown-item,
[data-language-system] .dropdown-item,
.language-selector .lang-btn,
[data-language-system] .lang-btn {
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: var(--lang-transition);
    border-radius: 0;
    color: var(--lang-dark);
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-weight: 500;
}

.language-selector .dropdown-item:hover,
[data-language-system] .dropdown-item:hover,
.language-selector .lang-btn:hover,
[data-language-system] .lang-btn:hover {
    background-color: var(--lang-light);
    transform: translateX(5px);
    color: var(--lang-primary);
}

.language-selector .dropdown-item:focus,
[data-language-system] .dropdown-item:focus,
.language-selector .lang-btn:focus,
[data-language-system] .lang-btn:focus {
    outline: 2px solid var(--lang-primary);
    outline-offset: -2px;
    background-color: var(--lang-light);
}

.language-selector .dropdown-item.active,
[data-language-system] .dropdown-item.active,
.language-selector .lang-btn.active,
[data-language-system] .lang-btn.active {
    background: linear-gradient(135deg, var(--lang-primary) 0%, #6610f2 100%);
    color: white;
    font-weight: 600;
}

.language-selector .dropdown-item.active:hover,
[data-language-system] .dropdown-item.active:hover,
.language-selector .lang-btn.active:hover,
[data-language-system] .lang-btn.active:hover {
    background: linear-gradient(135deg, var(--lang-primary-hover) 0%, #5a0fc8 100%);
    transform: translateX(3px);
}

/* ===== أيقونات اللغة ===== */
.language-flag,
.tifinagh-text {
    font-size: 1.2em;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.language-name {
    flex-grow: 1;
    font-weight: inherit;
}

/* ===== أيقونة التحديد ===== */
.language-check,
.bi-check2 {
    margin-left: auto;
    opacity: 0;
    transition: var(--lang-transition);
    color: currentColor;
    flex-shrink: 0;
}

.dropdown-item.active .language-check,
.dropdown-item.active .bi-check2,
.lang-btn.active .language-check,
.lang-btn.active .bi-check2 {
    opacity: 1;
}

/* ===== النص الحالي ===== */
.current-language,
[data-current-language] {
    font-weight: 500;
    transition: var(--lang-transition);
}

/* ===== تحسينات للأجهزة المحمولة ===== */
@media (max-width: 768px) {
    .language-selector .dropdown-menu,
    [data-language-system] .dropdown-menu {
        min-width: 250px;
        max-width: 90vw;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }
    
    .language-selector .dropdown-toggle,
    [data-language-system] .dropdown-toggle {
        min-width: 120px;
        font-size: 0.9rem;
    }
    
    .language-selector .dropdown-item,
    [data-language-system] .dropdown-item,
    .language-selector .lang-btn,
    [data-language-system] .lang-btn {
        padding: 1rem 1.25rem;
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .language-selector .dropdown-toggle,
    [data-language-system] .dropdown-toggle {
        min-width: 100px;
        padding: 0.5rem 1rem;
    }
    
    .language-selector .dropdown-menu,
    [data-language-system] .dropdown-menu {
        min-width: 200px;
    }
}

/* ===== تحسينات اللمس ===== */
@media (hover: none) and (pointer: coarse) {
    .language-selector .dropdown-item,
    [data-language-system] .dropdown-item,
    .language-selector .lang-btn,
    [data-language-system] .lang-btn {
        padding: 1rem 1.25rem;
        min-height: 48px;
    }
    
    .language-selector .dropdown-toggle,
    [data-language-system] .dropdown-toggle {
        min-height: 44px;
        padding: 0.75rem 1rem;
    }
}

/* ===== تحسينات الوصولية ===== */
@media (prefers-reduced-motion: reduce) {
    .language-selector *,
    [data-language-system] * {
        transition: none !important;
        animation: none !important;
    }
}

@media (prefers-contrast: high) {
    .language-selector .dropdown-toggle,
    [data-language-system] .dropdown-toggle {
        border-width: 2px;
    }
    
    .language-selector .dropdown-item.active,
    [data-language-system] .dropdown-item.active,
    .language-selector .lang-btn.active,
    [data-language-system] .lang-btn.active {
        outline: 2px solid white;
        outline-offset: -2px;
    }
}

/* ===== الوضع المظلم ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --lang-light: #343a40;
        --lang-dark: #f8f9fa;
    }
    
    .language-selector .dropdown-menu,
    [data-language-system] .dropdown-menu {
        background: #2d3748;
        color: #f8f9fa;
    }
    
    .language-selector .dropdown-toggle,
    [data-language-system] .dropdown-toggle {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: #f8f9fa;
        border-color: var(--lang-primary);
    }
    
    .language-selector .dropdown-item,
    [data-language-system] .dropdown-item,
    .language-selector .lang-btn,
    [data-language-system] .lang-btn {
        color: #f8f9fa;
    }
    
    .language-selector .dropdown-item:hover,
    [data-language-system] .dropdown-item:hover,
    .language-selector .lang-btn:hover,
    [data-language-system] .lang-btn:hover {
        background-color: #4a5568;
    }
}

/* ===== تأثيرات إضافية ===== */
.language-selector .dropdown-divider,
[data-language-system] .dropdown-divider {
    margin: 0.5rem 0;
    border-color: rgba(0, 0, 0, 0.1);
}

/* ===== تحسينات الطباعة ===== */
@media print {
    .language-selector,
    [data-language-system] {
        display: none !important;
    }
}

/* ===== تأثيرات التركيز المحسنة ===== */
.language-selector .dropdown-toggle:focus-visible,
[data-language-system] .dropdown-toggle:focus-visible {
    outline: 2px solid var(--lang-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(13, 110, 253, 0.25);
}

.language-selector .dropdown-item:focus-visible,
[data-language-system] .dropdown-item:focus-visible,
.language-selector .lang-btn:focus-visible,
[data-language-system] .lang-btn:focus-visible {
    outline: 2px solid var(--lang-primary);
    outline-offset: -2px;
    box-shadow: inset 0 0 0 2px rgba(13, 110, 253, 0.25);
}

/* ===== تحسينات الأداء ===== */
.language-selector,
[data-language-system] {
    contain: layout style;
    will-change: transform;
}

.language-selector .dropdown-menu,
[data-language-system] .dropdown-menu {
    contain: layout style;
    transform: translateZ(0); /* تفعيل تسريع الأجهزة */
}
