import os
import sys
import json
import logging
import bcrypt
import shutil
import platform
import time
import zipfile
import socket
from datetime import datetime
from functools import wraps
from flask import Blueprint, render_template, request, jsonify, redirect, url_for, session, flash, abort, send_file
from werkzeug.utils import secure_filename

# استيراد مدير الترجمة الجديد
import i18n_manager

# إعداد التسجيل
logger = logging.getLogger(__name__)

# Setup logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create admin blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# Path to data files
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
STATS_FILE = os.path.join(DATA_DIR, 'stats.json')
SETTINGS_FILE = os.path.join(DATA_DIR, 'settings.json')
USERS_FILE = os.path.join(DATA_DIR, 'users.json')

# Default admin credentials (should be moved to environment variables in production)
DEFAULT_ADMIN_USERNAME = 'admin'
DEFAULT_ADMIN_PASSWORD = 'tifinagh2024'

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            return redirect(url_for('admin.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

# Function to load users
def load_users():
    """Load users from the users.json file"""
    try:
        if os.path.exists(USERS_FILE):
            with open(USERS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # Create default users file if it doesn't exist
            default_users = {
                DEFAULT_ADMIN_USERNAME: {
                    'password': DEFAULT_ADMIN_PASSWORD,
                    'role': 'admin',
                    'last_login': ''
                }
            }
            os.makedirs(os.path.dirname(USERS_FILE), exist_ok=True)
            with open(USERS_FILE, 'w', encoding='utf-8') as f:
                json.dump(default_users, f, ensure_ascii=False, indent=4)
            return default_users
    except Exception as e:
        logger.error(f"Error loading users: {str(e)}")
        return {DEFAULT_ADMIN_USERNAME: {'password': DEFAULT_ADMIN_PASSWORD, 'role': 'admin', 'last_login': ''}}

# Function to save users
def save_users(users_dict):
    """Save users to the users.json file"""
    try:
        os.makedirs(os.path.dirname(USERS_FILE), exist_ok=True)
        with open(USERS_FILE, 'w', encoding='utf-8') as f:
            json.dump(users_dict, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        logger.error(f"Error saving users: {str(e)}")
        return False

# Function to load settings
def load_settings():
    """Load settings from the settings.json file"""
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            default_settings = {
                'bilingual_mode': True,
                'default_language': 'en',
                'enable_english': True,
                'enable_amazigh': True,
                'enable_file_conversions': True,
                'enable_web_proxy': True,
                'maintenance_mode': False,
                'maintenance_message': 'The site is currently under maintenance. Please check back later.',
                'welcome_message': 'Convert Latin script to Tifinagh script instantly with our powerful tools',
                'primary_color': '#0d6efd',
                'secondary_color': '#6c757d',
                'logo_path': 'static/images/tifinagh-logo.svg',
                'show_logo': True,
                'show_footer': True,
                'show_social': True,
                'cookie_consent': True,
                'contact_email': '<EMAIL>',
                'social_enabled': {
                    'facebook': True,
                    'twitter': True,
                    'instagram': True,
                    'youtube': True,
                    'linkedin': True,
                    'email': True
                },
                'social_links': {
                    'facebook': '',
                    'twitter': '',
                    'instagram': '',
                    'youtube': '',
                    'linkedin': ''
                }
            }
            os.makedirs(os.path.dirname(SETTINGS_FILE), exist_ok=True)
            with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(default_settings, f, ensure_ascii=False, indent=4)
            return default_settings
    except Exception as e:
        logger.error(f"Error loading settings: {str(e)}")
        return {'bilingual_mode': True}

# Function to save settings
def save_settings(settings_dict):
    """Save settings to the settings.json file"""
    try:
        os.makedirs(os.path.dirname(SETTINGS_FILE), exist_ok=True)
        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(settings_dict, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        logger.error(f"Error saving settings: {str(e)}")
        return False

# Function to load stats
def load_stats():
    """Load stats from the stats.json file"""
    try:
        if os.path.exists(STATS_FILE):
            with open(STATS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            default_stats = {
                'text_conversions': 0,
                'file_conversions': 0,
                'web_conversions': 0,
                'unique_visitors': 0,
                'recent_activities': [],
                'daily_stats': {},
                'monthly_stats': {}
            }
            os.makedirs(os.path.dirname(STATS_FILE), exist_ok=True)
            with open(STATS_FILE, 'w', encoding='utf-8') as f:
                json.dump(default_stats, f, ensure_ascii=False, indent=4)
            return default_stats
    except Exception as e:
        logger.error(f"Error loading stats: {str(e)}")
        return {
            'text_conversions': 0,
            'file_conversions': 0,
            'web_conversions': 0,
            'unique_visitors': 0,
            'recent_activities': []
        }

# Function to save stats
def save_stats(stats_dict):
    """Save stats to the stats.json file"""
    try:
        os.makedirs(os.path.dirname(STATS_FILE), exist_ok=True)
        with open(STATS_FILE, 'w', encoding='utf-8') as f:
            json.dump(stats_dict, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        logger.error(f"Error saving stats: {str(e)}")
        return False

# Function to load policies
def load_policies():
    """Load policies from JSON file"""
    policies_file = os.path.join(DATA_DIR, 'policies.json')
    try:
        if os.path.exists(policies_file):
            with open(policies_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # Default policies
            default_policies = {
                'privacy_enabled': False,
                'privacy_title': 'Privacy Policy',
                'privacy_content': get_default_privacy_policy(),
                'terms_enabled': False,
                'terms_title': 'Terms of Use',
                'terms_content': get_default_terms(),
                'cookie_enabled': False,
                'cookie_title': 'Cookie Policy',
                'cookie_content': get_default_cookie_policy(),
                'show_consent_banner': True,
                'consent_title': 'Cookie Consent',
                'consent_message': 'This website uses cookies to ensure you get the best experience on our website.',
                'accept_button_text': 'Accept All',
                'reject_button_text': 'Reject All',
                'settings_button_text': 'Cookie Settings',
                'banner_position': 'bottom',
                'banner_theme': 'light'
            }
            os.makedirs(os.path.dirname(policies_file), exist_ok=True)
            with open(policies_file, 'w', encoding='utf-8') as f:
                json.dump(default_policies, f, ensure_ascii=False, indent=4)
            return default_policies
    except Exception as e:
        logger.error(f"Error loading policies: {str(e)}")
        return {}

# Function to save policies
def save_policies(policies_dict):
    """Save policies to JSON file"""
    policies_file = os.path.join(DATA_DIR, 'policies.json')
    try:
        os.makedirs(os.path.dirname(policies_file), exist_ok=True)
        with open(policies_file, 'w', encoding='utf-8') as f:
            json.dump(policies_dict, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        logger.error(f"Error saving policies: {str(e)}")
        return False

# Function to get default privacy policy
def get_default_privacy_policy():
    """Get default privacy policy text"""
    return """
<h1>Privacy Policy</h1>
<p>Last updated: [Current Date]</p>

<h2>Introduction</h2>
<p>Your privacy is important to us. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our Tifinagh Converter website.</p>

<h2>Information We Collect</h2>
<p>We do not collect personal information unless explicitly provided by you. The text you convert is processed in your browser and is not stored on our servers.</p>

<h2>How We Use Your Information</h2>
<p>We may use non-personal information for:</p>
<ul>
    <li>Improving our website and services</li>
    <li>Analyzing usage patterns</li>
    <li>Maintaining and improving the functionality of our website</li>
</ul>

<h2>Cookies</h2>
<p>We may use cookies to enhance your experience on our website. You can set your browser to refuse all or some browser cookies, but this may prevent some parts of our website from functioning properly.</p>

<h2>Third-Party Services</h2>
<p>We do not share your information with third parties except as necessary to provide our services or as required by law.</p>

<h2>Data Security</h2>
<p>We implement reasonable security measures to protect your information. However, no method of transmission over the Internet or electronic storage is 100% secure.</p>

<h2>Changes to This Privacy Policy</h2>
<p>We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.</p>

<h2>Contact Us</h2>
<p>If you have any questions about this Privacy Policy, please contact us.</p>
"""

# Function to get default terms of use
def get_default_terms():
    """Get default terms of use text"""
    return """
<h1>Terms of Use</h1>
<p>Last updated: [Current Date]</p>

<h2>Acceptance of Terms</h2>
<p>By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement.</p>

<h2>Use License</h2>
<p>Permission is granted to temporarily use this website for personal, non-commercial purposes only. This is the grant of a license, not a transfer of title.</p>

<h2>Disclaimer</h2>
<p>The materials on this website are provided on an 'as is' basis. We make no warranties, expressed or implied, and hereby disclaim and negate all other warranties including, without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</p>

<h2>Limitations</h2>
<p>In no event shall we or our suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on this website, even if we or an authorized representative has been notified orally or in writing of the possibility of such damage.</p>

<h2>Accuracy of Materials</h2>
<p>The materials appearing on this website could include technical, typographical, or photographic errors. We do not warrant that any of the materials on this website are accurate, complete, or current.</p>

<h2>Links</h2>
<p>We have not reviewed all of the sites linked to this website and are not responsible for the contents of any such linked site. The inclusion of any link does not imply endorsement by us of the site. Use of any such linked website is at the user's own risk.</p>

<h2>Modifications</h2>
<p>We may revise these terms of service for this website at any time without notice. By using this website, you are agreeing to be bound by the then current version of these terms of service.</p>

<h2>Governing Law</h2>
<p>These terms and conditions are governed by and construed in accordance with the laws and you irrevocably submit to the exclusive jurisdiction of the courts in that location.</p>
"""

# Function to get default cookie policy
def get_default_cookie_policy():
    """Get default cookie policy text"""
    return """
<h1>Cookie Policy</h1>
<p>Last updated: [Current Date]</p>

<h2>What Are Cookies</h2>
<p>Cookies are small pieces of text sent to your web browser by a website you visit. A cookie file is stored in your web browser and allows the website or a third-party to recognize you and make your next visit easier and the website more useful to you.</p>

<h2>How We Use Cookies</h2>
<p>We use cookies for the following purposes:</p>
<ul>
    <li>To enable certain functions of the website</li>
    <li>To provide analytics</li>
    <li>To store your preferences</li>
</ul>

<h2>Types of Cookies We Use</h2>
<p>Essential cookies: These cookies are necessary for the website to function properly. They enable basic functions like page navigation and access to secure areas of the website.</p>
<p>Preference cookies: These cookies enable the website to remember information that changes the way the website behaves or looks, like your preferred language or the region you are in.</p>
<p>Analytics cookies: These cookies help us understand how visitors interact with the website by collecting and reporting information anonymously.</p>

<h2>Your Choices Regarding Cookies</h2>
<p>If you prefer to avoid the use of cookies on the website, first you must disable the use of cookies in your browser and then delete the cookies saved in your browser associated with this website. You may use this option for preventing the use of cookies at any time.</p>

<h2>Changes to This Cookie Policy</h2>
<p>We may update our Cookie Policy from time to time. We will notify you of any changes by posting the new Cookie Policy on this page.</p>

<h2>Contact Us</h2>
<p>If you have any questions about our Cookie Policy, please contact us.</p>
"""

# Function to add activity
def add_activity(description, activity_type="System", status="Info"):
    """Add activity to stats"""
    stats = load_stats()

    # Map status to color
    status_color_map = {
        "Info": "info",
        "Success": "success",
        "Warning": "warning",
        "Error": "danger"
    }

    # Create activity entry
    activity = {
        "time": datetime.now().strftime("%H:%M"),
        "description": description,
        "type": activity_type,
        "status": status,
        "status_color": status_color_map.get(status, "secondary")
    }

    # Add to recent activities
    if "recent_activities" not in stats:
        stats["recent_activities"] = []

    stats["recent_activities"].insert(0, activity)

    # Keep only the last 20 activities
    stats["recent_activities"] = stats["recent_activities"][:20]

    # Update last updated timestamp
    stats["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Save stats
    save_stats(stats)

    return True

# Function to create backup
def create_backup():
    """Create a backup of all data files"""
    try:
        # Create backup directory if it doesn't exist
        backup_dir = os.path.join(DATA_DIR, 'backups')
        os.makedirs(backup_dir, exist_ok=True)

        # Create timestamp for backup name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_{timestamp}"
        backup_path = os.path.join(backup_dir, backup_name)
        os.makedirs(backup_path, exist_ok=True)

        # Copy all JSON files from data directory to backup directory
        for filename in os.listdir(DATA_DIR):
            if filename.endswith('.json'):
                src = os.path.join(DATA_DIR, filename)
                dst = os.path.join(backup_path, filename)
                shutil.copy2(src, dst)

        # Create a zip file of the backup
        shutil.make_archive(backup_path, 'zip', backup_path)

        # Remove the directory, keep only the zip file
        shutil.rmtree(backup_path)

        # Add activity
        add_activity(f"Backup created: {backup_name}.zip", "System", "Success")

        return f"{backup_name}.zip"
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        add_activity(f"Backup creation failed: {str(e)}", "System", "Error")
        return None

# Function to optimize data files
def optimize_data_files():
    """Optimize data files by removing unnecessary data and reorganizing"""
    try:
        # Create backup before optimization
        create_backup()

        # Optimize stats file
        stats = load_stats()

        # Keep only the last 20 activities
        if "recent_activities" in stats:
            stats["recent_activities"] = stats["recent_activities"][:20]

        # Remove old daily stats (older than 30 days)
        if "daily_stats" in stats:
            current_date = datetime.now()
            daily_stats = {}
            for date_str, data in stats["daily_stats"].items():
                try:
                    date = datetime.strptime(date_str, "%Y-%m-%d")
                    days_diff = (current_date - date).days
                    if days_diff <= 30:
                        daily_stats[date_str] = data
                except ValueError:
                    # Skip invalid date formats
                    pass
            stats["daily_stats"] = daily_stats

        # Save optimized stats
        save_stats(stats)

        # Add activity
        add_activity("Data files optimized", "System", "Success")

        return True
    except Exception as e:
        logger.error(f"Error optimizing data files: {str(e)}")
        add_activity(f"Data file optimization failed: {str(e)}", "System", "Error")
        return False

# Login route
@admin_bp.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        users = load_users()

        if username in users:
            # التحقق من كلمة المرور
            stored_password = users[username]['password']

            # التحقق مما إذا كانت كلمة المرور مشفرة بالفعل
            if stored_password.startswith('$2b$'):
                # كلمة المرور مشفرة، استخدم bcrypt للتحقق
                try:
                    password_match = bcrypt.checkpw(password.encode('utf-8'), stored_password.encode('utf-8'))
                except Exception as e:
                    logger.error(f"Error checking password: {str(e)}")
                    password_match = False
            else:
                # كلمة المرور غير مشفرة، قارن مباشرة
                password_match = (stored_password == password)

                # تشفير كلمة المرور للاستخدام المستقبلي
                if password_match:
                    try:
                        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                        users[username]['password'] = hashed_password
                        save_users(users)
                    except Exception as e:
                        logger.error(f"Error hashing password: {str(e)}")

            if password_match:
                session['admin_logged_in'] = True
                session['admin_username'] = username

                # تحديث وقت آخر تسجيل دخول
                users[username]['last_login'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                save_users(users)

                # إضافة نشاط
                add_activity(f"Admin login: {username}", "System", "Success")

                return redirect(url_for('admin.dashboard'))

        # تسجيل محاولة تسجيل دخول فاشلة
        add_activity(f"Failed login attempt: {username}", "System", "Error")
        error = 'اسم المستخدم أو كلمة المرور غير صحيحة'

    return render_template('admin/login.html', error=error)

# Logout route
@admin_bp.route('/logout')
def logout():
    session.pop('admin_logged_in', None)
    session.pop('admin_username', None)
    return redirect(url_for('admin.login'))

# Dashboard route
@admin_bp.route('/')
@login_required
def dashboard():
    stats = load_stats()

    # تحميل إعدادات التطبيق
    settings = load_settings()

    # بيانات وهمية لحالة النظام
    system_status = {
        'cpu_usage': 25.5,
        'memory_usage': 40.2,
        'disk_space': 60.8,
        'last_backup': datetime.now().strftime('%Y-%m-%d %H:%M')
    }

    # بيانات وهمية للرسم البياني
    chart_labels = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]
    chart_data = {
        'text_conversions': [65, 59, 80, 81, 56, 55],
        'file_conversions': [28, 48, 40, 19, 86, 27],
        'web_conversions': [10, 20, 30, 40, 50, 60]
    }

    # الأنشطة الأخيرة
    recent_activities = stats.get('recent_activities', [])

    # آخر تسجيل دخول
    users = load_users()
    current_user = session.get('admin_username', 'admin')
    last_login = users.get(current_user, {}).get('last_login', 'Never')

    return render_template('admin/dashboard.html',
                          stats=stats,
                          settings=settings,
                          system_status=system_status,
                          chart_labels=chart_labels,
                          chart_data=chart_data,
                          recent_activities=recent_activities,
                          last_login=last_login,
                          active_page='dashboard')

# Settings route
@admin_bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    if request.method == 'POST':
        settings_dict = load_settings()

        # Update settings from form
        settings_dict['bilingual_mode'] = request.form.get('bilingual_mode') == 'on'
        settings_dict['enable_file_conversions'] = request.form.get('enable_file_conversions') == 'on'
        settings_dict['enable_web_proxy'] = request.form.get('enable_web_proxy') == 'on'
        settings_dict['maintenance_mode'] = request.form.get('maintenance_mode') == 'on'

        # Save updated settings
        if save_settings(settings_dict):
            flash('تم حفظ الإعدادات بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ الإعدادات', 'danger')

        return redirect(url_for('admin.settings'))

    # GET request
    settings_dict = load_settings()
    return render_template('admin/settings.html', settings=settings_dict, active_page='settings')

# Users management route
@admin_bp.route('/users', methods=['GET', 'POST'])
@login_required
def users():
    users_dict = load_users()

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'add_user':
            username = request.form.get('username')
            password = request.form.get('password')
            confirm_password = request.form.get('confirm_password')
            role = request.form.get('role', 'user')

            if username and password and confirm_password:
                # التحقق من تطابق كلمة المرور مع التأكيد
                if password != confirm_password:
                    flash('كلمة المرور وتأكيدها غير متطابقين', 'danger')
                    return redirect(url_for('admin.users'))

                if username not in users_dict:
                    # تشفير كلمة المرور
                    try:
                        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

                        # إضافة المستخدم الجديد
                        users_dict[username] = {
                            'password': hashed_password,
                            'role': role,
                            'last_login': 'Never'
                        }

                        if save_users(users_dict):
                            flash(f'تم إضافة المستخدم {username} بنجاح', 'success')
                            add_activity(f"تم إضافة مستخدم جديد: {username}", "Admin", "Success")
                        else:
                            flash('حدث خطأ أثناء حفظ المستخدم الجديد', 'danger')
                    except Exception as e:
                        logger.error(f"Error hashing password: {str(e)}")
                        flash(f'حدث خطأ أثناء تشفير كلمة المرور: {str(e)}', 'danger')
                else:
                    flash('اسم المستخدم موجود بالفعل', 'danger')
            else:
                flash('يرجى إدخال اسم المستخدم وكلمة المرور وتأكيدها', 'danger')

        elif action == 'delete_user':
            username = request.form.get('username')

            if username in users_dict:
                # التحقق من أنه ليس المستخدم الوحيد
                if len(users_dict) <= 1:
                    flash('لا يمكن حذف المستخدم الوحيد', 'danger')
                # التحقق من أنه ليس المستخدم الحالي
                elif username == session.get('admin_username'):
                    flash('لا يمكنك حذف حسابك الحالي', 'danger')
                else:
                    del users_dict[username]
                    if save_users(users_dict):
                        flash(f'تم حذف المستخدم {username} بنجاح', 'success')
                        add_activity(f"تم حذف المستخدم: {username}", "Admin", "Success")
                    else:
                        flash('حدث خطأ أثناء حذف المستخدم', 'danger')
            else:
                flash('المستخدم غير موجود', 'danger')

        elif action == 'change_password':
            username = request.form.get('username')
            current_password = request.form.get('current_password')
            new_password = request.form.get('new_password')
            confirm_password = request.form.get('confirm_password')

            if username and new_password and confirm_password:
                # التحقق من تطابق كلمة المرور الجديدة مع التأكيد
                if new_password != confirm_password:
                    flash('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'danger')
                    return redirect(url_for('admin.users'))

                # التحقق من كلمة المرور الحالية إذا كانت موجودة
                if current_password and username == session.get('admin_username'):
                    stored_password = users_dict[username]['password']

                    # التحقق مما إذا كانت كلمة المرور مشفرة بالفعل
                    if stored_password.startswith('$2b$'):
                        # كلمة المرور مشفرة، استخدم bcrypt للتحقق
                        try:
                            password_match = bcrypt.checkpw(current_password.encode('utf-8'), stored_password.encode('utf-8'))
                        except Exception as e:
                            logger.error(f"Error checking password: {str(e)}")
                            password_match = False
                    else:
                        # كلمة المرور غير مشفرة، قارن مباشرة
                        password_match = (stored_password == current_password)

                    if not password_match:
                        flash('كلمة المرور الحالية غير صحيحة', 'danger')
                        return redirect(url_for('admin.users'))

                # تشفير كلمة المرور الجديدة
                try:
                    hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                    users_dict[username]['password'] = hashed_password

                    if save_users(users_dict):
                        flash('تم تغيير كلمة المرور بنجاح', 'success')
                        add_activity(f"تم تغيير كلمة مرور المستخدم: {username}", "Admin", "Success")
                    else:
                        flash('حدث خطأ أثناء حفظ كلمة المرور الجديدة', 'danger')
                except Exception as e:
                    logger.error(f"Error hashing password: {str(e)}")
                    flash(f'حدث خطأ أثناء تشفير كلمة المرور: {str(e)}', 'danger')
            else:
                flash('يرجى إدخال جميع الحقول المطلوبة', 'danger')

        return redirect(url_for('admin.users'))

    # GET request
    return render_template('admin/users.html', users=users_dict, active_page='users')

# API Routes for admin dashboard data
@admin_bp.route('/api/stats')
@login_required
def get_stats():
    stats = load_stats()
    return jsonify(stats)

@admin_bp.route('/api/settings', methods=['GET', 'PUT'])
@login_required
def api_settings():
    if request.method == 'GET':
        settings_dict = load_settings()
        return jsonify(settings_dict)
    elif request.method == 'PUT':
        data = request.get_json()
        if data:
            settings_dict = load_settings()
            # Update settings from JSON
            for key, value in data.items():
                settings_dict[key] = value

            if save_settings(settings_dict):
                return jsonify({'success': True, 'message': 'تم تحديث الإعدادات بنجاح'})
            else:
                return jsonify({'success': False, 'message': 'حدث خطأ أثناء حفظ الإعدادات'}), 500
        else:
            return jsonify({'success': False, 'message': 'لم يتم توفير بيانات'}), 400

# Stats management route
@admin_bp.route('/stats')
@login_required
def stats():
    stats_data = load_stats()
    return render_template('admin/stats.html', stats=stats_data, active_page='stats')

# Reset stats
@admin_bp.route('/stats/reset', methods=['POST'])
@login_required
def reset_stats():
    try:
        stats = load_stats()

        # Reset stats based on form data
        reset_type = request.form.get('reset_type')

        if reset_type == 'all':
            stats['text_conversions'] = 0
            stats['file_conversions'] = 0
            stats['web_conversions'] = 0
            stats['unique_visitors'] = 0
            stats['recent_activities'] = []
            stats['daily_stats'] = {}
            stats['monthly_stats'] = {}
            flash('تم إعادة تعيين جميع الإحصائيات بنجاح', 'success')

        elif reset_type == 'daily':
            stats['daily_stats'] = {}
            flash('تم إعادة تعيين الإحصائيات اليومية بنجاح', 'success')

        elif reset_type == 'monthly':
            stats['monthly_stats'] = {}
            flash('تم إعادة تعيين الإحصائيات الشهرية بنجاح', 'success')

        elif reset_type == 'activities':
            stats['recent_activities'] = []
            flash('تم إعادة تعيين سجل الأنشطة بنجاح', 'success')

        # Save the updated stats
        with open(STATS_FILE, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=4)

    except Exception as e:
        logger.error(f"Error resetting stats: {str(e)}")
        flash(f'حدث خطأ أثناء إعادة تعيين الإحصائيات: {str(e)}', 'danger')

    return redirect(url_for('admin.stats'))

# Tools & Diagnostics route
@admin_bp.route('/tools')
@login_required
def admin_tools():
    import platform

    try:
        # محاولة استيراد psutil إذا كان متاحًا
        try:
            import psutil
            cpu_usage = psutil.cpu_percent()
            memory_total = round(psutil.virtual_memory().total / (1024 * 1024 * 1024), 2)  # GB
            memory_used = round(psutil.virtual_memory().used / (1024 * 1024 * 1024), 2)  # GB
            memory_percent = psutil.virtual_memory().percent
            disk_total = round(psutil.disk_usage('/').total / (1024 * 1024 * 1024), 2)  # GB
            disk_used = round(psutil.disk_usage('/').used / (1024 * 1024 * 1024), 2)  # GB
            disk_percent = psutil.disk_usage('/').percent
        except ImportError:
            # إذا لم يكن psutil متاحًا، استخدم قيمًا افتراضية
            cpu_usage = 0
            memory_total = 0
            memory_used = 0
            memory_percent = 0
            disk_total = 0
            disk_used = 0
            disk_percent = 0

        # معلومات النظام
        system_info = {
            'os': platform.system(),
            'os_version': platform.version(),
            'python_version': platform.python_version(),
            'cpu_usage': cpu_usage,
            'memory_total': memory_total,
            'memory_used': memory_used,
            'memory_percent': memory_percent,
            'disk_total': disk_total,
            'disk_used': disk_used,
            'disk_percent': disk_percent,
        }

        # التحقق من المكتبات المطلوبة
        required_libraries = {
            'flask': True,
            'python-docx': True,
            'beautifulsoup4': True,
            'requests': True,
            'psutil': 'psutil' in sys.modules
        }

        # قراءة آخر الأخطاء من ملف السجل (إذا كان موجودًا)
        log_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.log')
        recent_errors = []

        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # البحث عن آخر 10 أخطاء
                    error_count = 0
                    for line in reversed(lines):
                        if 'ERROR' in line:
                            recent_errors.append(line.strip())
                            error_count += 1
                            if error_count >= 10:
                                break
            except Exception as e:
                recent_errors = [f'Error reading log file: {str(e)}']
        else:
            recent_errors = ['Log file not found']

        # تحميل إعدادات التطبيق
        settings = load_settings()

        return render_template('admin/tools.html',
                              system_info=system_info,
                              required_libraries=required_libraries,
                              recent_errors=recent_errors,
                              settings=settings,
                              active_page='tools')
    except Exception as e:
        logger.error(f"Error retrieving system information: {str(e)}")
        # إنشاء قيم افتراضية للمتغيرات المطلوبة
        system_info = {
            'os': 'Unknown',
            'os_version': 'Unknown',
            'python_version': 'Unknown',
            'cpu_usage': 0,
            'memory_total': 0,
            'memory_used': 0,
            'memory_percent': 0,
            'disk_total': 0,
            'disk_used': 0,
            'disk_percent': 0,
            'flask_version': 'Unknown',
            'hostname': 'Unknown',
            'server_time': 'Unknown',
            'uptime': 'Unknown'
        }
        required_libraries = {
            'flask': True,
            'python-docx': False,
            'beautifulsoup4': False,
            'requests': False,
            'psutil': False
        }
        recent_errors = [f'Error retrieving system information: {str(e)}']
        settings = load_settings()

        return render_template('admin/tools.html',
                              system_info=system_info,
                              required_libraries=required_libraries,
                              recent_errors=recent_errors,
                              settings=settings,
                              error=f'Error retrieving system information: {str(e)}',
                              active_page='tools')

# Interface Customization route
@admin_bp.route('/interface', methods=['GET', 'POST'])
@login_required
def admin_interface():
    if request.method == 'POST':
        settings_dict = load_settings()

        # تحديث إعدادات الواجهة
        if 'welcome_message' in request.form:
            settings_dict['welcome_message'] = request.form.get('welcome_message')

        if 'primary_color' in request.form:
            settings_dict['primary_color'] = request.form.get('primary_color')

        if 'secondary_color' in request.form:
            settings_dict['secondary_color'] = request.form.get('secondary_color')

        if 'default_font' in request.form:
            settings_dict['default_font'] = request.form.get('default_font')

        # تحديث إعدادات العرض
        settings_dict['show_logo'] = request.form.get('show_logo') == 'on'
        settings_dict['show_footer'] = request.form.get('show_footer') == 'on'
        settings_dict['show_social'] = request.form.get('show_social') == 'on'
        settings_dict['maintenance_mode'] = request.form.get('maintenance_mode') == 'on'
        settings_dict['maintenance_message'] = request.form.get('maintenance_message', 'The site is currently under maintenance. Please check back later.')

        # تحديث روابط التواصل الاجتماعي
        settings_dict['social_links'] = {
            'facebook': request.form.get('facebook_url', ''),
            'twitter': request.form.get('twitter_url', ''),
            'instagram': request.form.get('instagram_url', ''),
            'youtube': request.form.get('youtube_url', ''),
            'linkedin': request.form.get('linkedin_url', '')
        }

        # معالجة تحميل الشعار
        if 'logo' in request.files:
            logo_file = request.files['logo']
            if logo_file and logo_file.filename:
                # تأمين اسم الملف
                filename = secure_filename(logo_file.filename)
                # التأكد من أن الملف هو صورة
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg')):
                    # حفظ الملف
                    logo_path = os.path.join('static', 'images', filename)
                    logo_file.save(logo_path)
                    # تحديث مسار الشعار في الإعدادات
                    settings_dict['logo_path'] = logo_path

        # حفظ الإعدادات المحدثة
        if save_settings(settings_dict):
            flash('تم حفظ إعدادات الواجهة بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ إعدادات الواجهة', 'danger')

        return redirect(url_for('admin.admin_interface'))

    # GET request
    settings_dict = load_settings()

    # التأكد من وجود إعدادات الواجهة
    if 'welcome_message' not in settings_dict:
        settings_dict['welcome_message'] = 'مرحبًا بك في محول تيفيناغ!'

    if 'primary_color' not in settings_dict:
        settings_dict['primary_color'] = '#0d6efd'  # لون Bootstrap الأساسي

    if 'secondary_color' not in settings_dict:
        settings_dict['secondary_color'] = '#6c757d'  # لون Bootstrap الثانوي

    if 'logo_path' not in settings_dict:
        settings_dict['logo_path'] = 'static/images/tifinagh-logo.svg'

    # استيراد وحدة التعامل مع مواقع التواصل الاجتماعي
    try:
        from social_media import get_social_media
        social_media = get_social_media()
    except Exception as e:
        logger.error(f"Error loading social media settings: {str(e)}")
        social_media = {}

    return render_template('admin/interface.html', settings=settings_dict, social_media=social_media, active_page='interface')

# Translations Management route
@admin_bp.route('/translations', methods=['GET', 'POST'])
@login_required
def admin_translations():
    from utils.translation_utils import get_all_application_texts, add_translation, delete_translation

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'add_translation':
            key = request.form.get('key')
            en_text = request.form.get('en_text')
            am_text = request.form.get('am_text')

            if key and (en_text or am_text):
                success, message = add_translation(key, en_text, am_text)
                if success:
                    flash(message, 'success')
                else:
                    flash(message, 'danger')
            else:
                flash('يرجى إدخال المفتاح والنص على الأقل بإحدى اللغتين', 'danger')

        elif action == 'delete_translation':
            key = request.form.get('key')

            if key:
                success, message = delete_translation(key)
                if success:
                    flash(message, 'success')
                else:
                    flash(message, 'danger')
            else:
                flash('لم يتم تحديد مفتاح للحذف', 'danger')

        elif action == 'toggle_bilingual':
            settings_dict = load_settings()
            settings_dict['bilingual_mode'] = request.form.get('bilingual_mode') == 'on'

            if save_settings(settings_dict):
                flash('تم تحديث إعدادات اللغة بنجاح', 'success')
            else:
                flash('حدث خطأ أثناء تحديث إعدادات اللغة', 'danger')

        return redirect(url_for('admin.admin_translations'))

    # GET request
    # تحميل الترجمات الحالية
    from utils.translation_utils import load_translations
    translations = load_translations()

    # الحصول على جميع النصوص المستخدمة في التطبيق
    all_texts = get_all_application_texts()

    # تحميل إعدادات التطبيق
    settings = load_settings()

    return render_template('admin/translations.html',
                          translations=translations,
                          all_texts=all_texts,
                          settings=settings,
                          active_page='translations')

# Policies & Legal route
@admin_bp.route('/policies', methods=['GET', 'POST'])
@login_required
def admin_policies():
    if request.method == 'POST':
        policies_dict = load_policies()

        # تحديث سياسة الخصوصية
        if 'privacy_content' in request.form:
            policies_dict['privacy_content'] = request.form.get('privacy_content')
            policies_dict['privacy_title'] = request.form.get('privacy_title')
            policies_dict['privacy_enabled'] = request.form.get('privacy_enabled') == 'on'

        # تحديث شروط الاستخدام
        if 'terms_content' in request.form:
            policies_dict['terms_content'] = request.form.get('terms_content')
            policies_dict['terms_title'] = request.form.get('terms_title')
            policies_dict['terms_enabled'] = request.form.get('terms_enabled') == 'on'

        # تحديث سياسة ملفات تعريف الارتباط
        if 'cookie_content' in request.form:
            policies_dict['cookie_content'] = request.form.get('cookie_content')
            policies_dict['cookie_title'] = request.form.get('cookie_title')
            policies_dict['cookie_enabled'] = request.form.get('cookie_enabled') == 'on'

        # تحديث إعدادات شريط ملفات تعريف الارتباط
        if 'consent_title' in request.form:
            policies_dict['show_consent_banner'] = request.form.get('show_consent_banner') == 'on'
            policies_dict['consent_title'] = request.form.get('consent_title')
            policies_dict['consent_message'] = request.form.get('consent_message')
            policies_dict['accept_button_text'] = request.form.get('accept_button_text')
            policies_dict['reject_button_text'] = request.form.get('reject_button_text')
            policies_dict['settings_button_text'] = request.form.get('settings_button_text')
            policies_dict['banner_position'] = request.form.get('banner_position')
            policies_dict['banner_theme'] = request.form.get('banner_theme')

        # حفظ الإعدادات المحدثة
        if save_policies(policies_dict):
            flash('تم حفظ الإعدادات القانونية بنجاح', 'success')
            add_activity('تم تحديث السياسات القانونية', 'Admin', 'Success')
        else:
            flash('حدث خطأ أثناء حفظ الإعدادات القانونية', 'danger')

        return redirect(url_for('admin.admin_policies'))

    # GET request
    policies_dict = load_policies()

    # تحميل النصوص الافتراضية إذا لم تكن موجودة
    if not policies_dict.get('privacy_content'):
        policies_dict['privacy_content'] = get_default_privacy_policy()

    if not policies_dict.get('terms_content'):
        policies_dict['terms_content'] = get_default_terms()

    if not policies_dict.get('cookie_content'):
        policies_dict['cookie_content'] = get_default_cookie_policy()

    return render_template('admin/policies.html',
                          policies=policies_dict,
                          default_privacy_policy=get_default_privacy_policy(),
                          default_terms=get_default_terms(),
                          default_cookie_policy=get_default_cookie_policy(),
                          active_page='policies')

# Translation API Routes
@admin_bp.route('/api/save_language_settings', methods=['POST'])
@login_required
def admin_save_language_settings():
    """Save language settings"""
    try:
        settings_dict = load_settings()

        # تحديث إعدادات اللغة
        settings_dict['default_language'] = request.form.get('default_language', 'en')
        settings_dict['bilingual_mode'] = request.form.get('bilingual_mode') == 'on'
        settings_dict['enable_english'] = request.form.get('enable_english') == 'on'
        settings_dict['enable_amazigh'] = request.form.get('enable_amazigh') == 'on'

        if save_settings(settings_dict):
            flash('تم حفظ إعدادات اللغة بنجاح', 'success')
            add_activity('تم تحديث إعدادات اللغة', 'Admin', 'Success')
        else:
            flash('حدث خطأ أثناء حفظ إعدادات اللغة', 'danger')

        return redirect(url_for('admin.admin_translations'))
    except Exception as e:
        logger.error(f"Error saving language settings: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_translations'))

# مسارات إدارة الترجمات الجديدة
@admin_bp.route('/i18n')
@login_required
def admin_i18n():
    """صفحة إدارة الترجمات الجديدة"""
    # تحميل الترجمات
    translations = i18n_manager.load_translations()

    # الحصول على إحصائيات الترجمة
    stats = i18n_manager.get_translation_statistics()

    return render_template('admin/i18n.html',
                          translations=translations,
                          stats=stats,
                          active_page='translations')

@admin_bp.route('/api/i18n/update', methods=['POST'])
@login_required
def admin_i18n_update():
    """تحديث ترجمة"""
    try:
        data = request.get_json()
        key = data.get('key')
        am_text = data.get('am_text', '')

        if not key:
            return jsonify({'success': False, 'message': 'المفتاح مطلوب'})

        success, message = i18n_manager.update_translation(key, am_text)

        if success:
            add_activity(f'تم تحديث الترجمة: {key}', 'Admin', 'Success')
        else:
            add_activity(f'فشل تحديث الترجمة: {key}', 'Admin', 'Error')

        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"خطأ في تحديث الترجمة: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/i18n/add', methods=['POST'])
@login_required
def admin_i18n_add():
    """إضافة ترجمة جديدة"""
    try:
        data = request.get_json()
        key = data.get('key')
        en_text = data.get('en_text', '')
        am_text = data.get('am_text', '')

        if not key or not en_text:
            return jsonify({'success': False, 'message': 'المفتاح والنص الإنجليزي مطلوبان'})

        success, message = i18n_manager.add_translation(key, en_text, am_text)

        if success:
            add_activity(f'تمت إضافة ترجمة جديدة: {key}', 'Admin', 'Success')
        else:
            add_activity(f'فشل إضافة الترجمة: {key}', 'Admin', 'Error')

        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"خطأ في إضافة الترجمة: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/i18n/delete', methods=['POST'])
@login_required
def admin_i18n_delete():
    """حذف ترجمة"""
    try:
        data = request.get_json()
        key = data.get('key')

        if not key:
            return jsonify({'success': False, 'message': 'المفتاح مطلوب'})

        success, message = i18n_manager.delete_translation(key)

        if success:
            add_activity(f'تم حذف الترجمة: {key}', 'Admin', 'Success')
        else:
            add_activity(f'فشل حذف الترجمة: {key}', 'Admin', 'Error')

        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"خطأ في حذف الترجمة: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/i18n/extract', methods=['POST'])
@login_required
def admin_i18n_extract():
    """استخراج النصوص من ملفات الموقع"""
    try:
        # استخراج النصوص من مجلد templates
        extracted_count, added_count = i18n_manager.extract_and_add_i18n_keys('templates')

        add_activity(f'تم استخراج {extracted_count} نص وإضافة {added_count} نص جديد', 'Admin', 'Success')

        return jsonify({
            'success': True,
            'message': f'تم استخراج {extracted_count} نص وإضافة {added_count} نص جديد',
            'extracted': extracted_count,
            'added': added_count
        })
    except Exception as e:
        logger.error(f"خطأ في استخراج النصوص: {str(e)}")
        add_activity(f'فشل استخراج النصوص: {str(e)}', 'Admin', 'Error')
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/i18n/export')
@login_required
def admin_i18n_export():
    """تصدير الترجمات كملف JSON"""
    try:
        # تحميل الترجمات
        translations = i18n_manager.load_translations()

        # إنشاء ملف مؤقت
        temp_file = os.path.join(DATA_DIR, 'i18n_export.json')

        # حفظ الترجمات في الملف المؤقت
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(translations, f, ensure_ascii=False, indent=2)

        # إرسال الملف للتنزيل
        return send_file(temp_file, as_attachment=True, download_name='i18n.json')
    except Exception as e:
        logger.error(f"خطأ في تصدير الترجمات: {str(e)}")
        flash(f'حدث خطأ أثناء تصدير الترجمات: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_i18n'))

@admin_bp.route('/api/i18n/stats')
@login_required
def admin_i18n_stats():
    """الحصول على إحصائيات الترجمة"""
    try:
        stats = i18n_manager.get_translation_statistics()
        return jsonify({'success': True, 'stats': stats})
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات الترجمة: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/save_translation', methods=['POST'])
@login_required
def admin_save_translation():
    """Save translation"""
    try:
        from utils.translation_utils import add_translation

        data = request.get_json()
        mode = data.get('mode')
        original_key = data.get('original_key')
        key = data.get('key')
        am = data.get('am')

        if not key:
            return jsonify({'success': False, 'message': 'المفتاح مطلوب'})

        # إذا كان تحديثًا وتم تغيير المفتاح، قم بحذف المفتاح القديم
        if mode == 'edit' and original_key and original_key != key:
            from utils.translation_utils import delete_translation
            delete_translation(original_key)

        # إضافة أو تحديث الترجمة
        success, message = add_translation(key, key, am)

        if success:
            add_activity(f'تم حفظ الترجمة: {key}', 'Admin', 'Success')
        else:
            add_activity(f'فشل حفظ الترجمة: {key}', 'Admin', 'Error')

        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"Error saving translation: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/delete_translation', methods=['POST'])
@login_required
def admin_delete_translation():
    """Delete translation"""
    try:
        from utils.translation_utils import delete_translation

        data = request.get_json()
        key = data.get('key')

        if not key:
            return jsonify({'success': False, 'message': 'المفتاح مطلوب'})

        success, message = delete_translation(key)

        if success:
            add_activity(f'تم حذف الترجمة: {key}', 'Admin', 'Success')
        else:
            add_activity(f'فشل حذف الترجمة: {key}', 'Admin', 'Error')

        return jsonify({'success': success, 'message': message})
    except Exception as e:
        logger.error(f"Error deleting translation: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# User Management API Routes
@admin_bp.route('/api/save_user', methods=['POST'])
@login_required
def admin_save_user():
    """Save user"""
    try:
        data = request.get_json()
        mode = data.get('mode')
        original_username = data.get('original_username')
        username = data.get('username')
        password = data.get('password')
        role = data.get('role')

        if not username:
            return jsonify({'success': False, 'message': 'اسم المستخدم مطلوب'})

        users_dict = load_users()

        # التحقق من وجود المستخدم في حالة الإضافة
        if mode == 'add' and username in users_dict:
            return jsonify({'success': False, 'message': 'اسم المستخدم موجود بالفعل'})

        # التحقق من كلمة المرور في حالة الإضافة
        if mode == 'add' and not password:
            return jsonify({'success': False, 'message': 'كلمة المرور مطلوبة للمستخدمين الجدد'})

        # إذا كان تحديثًا وتم تغيير اسم المستخدم، قم بحذف المستخدم القديم
        if mode == 'edit' and original_username and original_username != username:
            if original_username in users_dict:
                user_data = users_dict[original_username]
                del users_dict[original_username]
            else:
                return jsonify({'success': False, 'message': 'المستخدم الأصلي غير موجود'})
        elif mode == 'edit' and original_username:
            # استخدام بيانات المستخدم الحالية
            user_data = users_dict[original_username]
        else:
            # إنشاء مستخدم جديد
            user_data = {
                'role': 'editor',
                'last_login': ''
            }

        # تحديث بيانات المستخدم
        user_data['role'] = role

        # تحديث كلمة المرور إذا تم توفيرها
        if password:
            try:
                hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                user_data['password'] = hashed_password
            except Exception as e:
                logger.error(f"Error hashing password: {str(e)}")
                return jsonify({'success': False, 'message': f'حدث خطأ أثناء تشفير كلمة المرور: {str(e)}'})

        # حفظ المستخدم
        users_dict[username] = user_data

        if save_users(users_dict):
            if mode == 'add':
                add_activity(f'تم إضافة مستخدم جديد: {username}', 'Admin', 'Success')
                return jsonify({'success': True, 'message': f'تم إضافة المستخدم {username} بنجاح'})
            else:
                add_activity(f'تم تحديث المستخدم: {username}', 'Admin', 'Success')
                return jsonify({'success': True, 'message': f'تم تحديث المستخدم {username} بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'حدث خطأ أثناء حفظ بيانات المستخدم'})
    except Exception as e:
        logger.error(f"Error saving user: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/reset_password', methods=['POST'])
@login_required
def admin_reset_password():
    """Reset user password"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'success': False, 'message': 'اسم المستخدم وكلمة المرور مطلوبان'})

        users_dict = load_users()

        if username not in users_dict:
            return jsonify({'success': False, 'message': 'المستخدم غير موجود'})

        # تشفير كلمة المرور الجديدة
        try:
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            users_dict[username]['password'] = hashed_password
        except Exception as e:
            logger.error(f"Error hashing password: {str(e)}")
            return jsonify({'success': False, 'message': f'حدث خطأ أثناء تشفير كلمة المرور: {str(e)}'})

        if save_users(users_dict):
            add_activity(f'تم إعادة تعيين كلمة مرور المستخدم: {username}', 'Admin', 'Success')
            return jsonify({'success': True, 'message': f'تم إعادة تعيين كلمة مرور المستخدم {username} بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'حدث خطأ أثناء حفظ كلمة المرور الجديدة'})
    except Exception as e:
        logger.error(f"Error resetting password: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/delete_user', methods=['POST'])
@login_required
def admin_delete_user():
    """Delete user"""
    try:
        data = request.get_json()
        username = data.get('username')

        if not username:
            return jsonify({'success': False, 'message': 'اسم المستخدم مطلوب'})

        # لا يمكن حذف المستخدم admin
        if username == 'admin':
            return jsonify({'success': False, 'message': 'لا يمكن حذف المستخدم الرئيسي'})

        users_dict = load_users()

        if username not in users_dict:
            return jsonify({'success': False, 'message': 'المستخدم غير موجود'})

        # حذف المستخدم
        del users_dict[username]

        if save_users(users_dict):
            add_activity(f'تم حذف المستخدم: {username}', 'Admin', 'Success')
            return jsonify({'success': True, 'message': f'تم حذف المستخدم {username} بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'حدث خطأ أثناء حذف المستخدم'})
    except Exception as e:
        logger.error(f"Error deleting user: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/export_translations')
@login_required
def admin_export_translations():
    """Export translations"""
    try:
        from utils.translation_utils import load_translations
        import tempfile

        translations = load_translations()

        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(delete=False, suffix='.json') as temp:
            temp_path = temp.name
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(translations, f, ensure_ascii=False, indent=2)

        add_activity('تم تصدير الترجمات', 'Admin', 'Success')

        return send_file(temp_path, as_attachment=True, download_name='translations.json', mimetype='application/json')
    except Exception as e:
        logger.error(f"Error exporting translations: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'}), 500

@admin_bp.route('/api/import_translations', methods=['POST'])
@login_required
def admin_import_translations():
    """Import translations"""
    try:
        from utils.translation_utils import load_translations, save_translations

        if 'file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم توفير ملف'})

        file = request.files['file']

        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'})

        if not file.filename.endswith('.json'):
            return jsonify({'success': False, 'message': 'يجب أن يكون الملف بتنسيق JSON'})

        # قراءة الملف
        try:
            imported_translations = json.loads(file.read().decode('utf-8'))
        except json.JSONDecodeError:
            return jsonify({'success': False, 'message': 'الملف ليس بتنسيق JSON صالح'})

        # تحميل الترجمات الحالية
        current_translations = load_translations()

        # تحديد ما إذا كان يجب استبدال الترجمات الموجودة
        overwrite = request.form.get('overwrite') == 'true'

        if overwrite:
            # استبدال جميع الترجمات
            new_translations = imported_translations
        else:
            # دمج الترجمات مع الحفاظ على الترجمات الموجودة
            new_translations = current_translations.copy()
            for key, value in imported_translations.items():
                if key not in new_translations:
                    new_translations[key] = value

        # حفظ الترجمات
        if save_translations(new_translations):
            add_activity('تم استيراد الترجمات', 'Admin', 'Success')
            return jsonify({'success': True, 'message': 'تم استيراد الترجمات بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل حفظ الترجمات المستوردة'})
    except Exception as e:
        logger.error(f"Error importing translations: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# Tools & Diagnostics API Routes
@admin_bp.route('/api/refresh_system_info', methods=['GET'])
@login_required
def admin_refresh_system_info():
    """Refresh system information"""
    try:
        import platform
        import datetime
        import socket
        import time
        
        # استخدام importlib.metadata بدلاً من pkg_resources
        try:
            import importlib.metadata
        except ImportError:
            # استمرار بدون أي من الحزمتين
            pass

        # محاولة استيراد psutil إذا كان متاحًا
        try:
            import psutil
            cpu_usage = psutil.cpu_percent()
            memory_total = round(psutil.virtual_memory().total / (1024 * 1024 * 1024), 2)  # GB
            memory_used = round(psutil.virtual_memory().used / (1024 * 1024 * 1024), 2)  # GB
            memory_percent = psutil.virtual_memory().percent
            disk_total = round(psutil.disk_usage('/').total / (1024 * 1024 * 1024), 2)  # GB
            disk_used = round(psutil.disk_usage('/').used / (1024 * 1024 * 1024), 2)  # GB
            disk_percent = psutil.disk_usage('/').percent
            boot_time = datetime.datetime.fromtimestamp(psutil.boot_time()).strftime("%Y-%m-%d %H:%M:%S")
            uptime_seconds = time.time() - psutil.boot_time()
            uptime_days = int(uptime_seconds / 86400)
            uptime_hours = int((uptime_seconds % 86400) / 3600)
            uptime_minutes = int((uptime_seconds % 3600) / 60)
            uptime = f"{uptime_days}d {uptime_hours}h {uptime_minutes}m"
        except ImportError:
            # إذا لم يكن psutil متاحًا، استخدم قيمًا افتراضية
            cpu_usage = 0
            memory_total = 0
            memory_used = 0
            memory_percent = 0
            disk_total = 0
            disk_used = 0
            disk_percent = 0
            boot_time = "Unknown"
            uptime = "Unknown"        # الحصول على إصدار Flask
        try:
            # محاولة استخدام importlib.metadata
            try:
                import importlib.metadata
                flask_version = importlib.metadata.version('flask')
            except ImportError:
                # استخدام طريقة بديلة للحصول على إصدار Flask
                try:
                    import flask
                    flask_version = flask.__version__
                except (ImportError, AttributeError):
                    flask_version = "Unknown"
        except Exception:
            flask_version = "Unknown"

        # معلومات النظام
        system_info = {
            'os': platform.system(),
            'os_version': platform.version(),
            'python_version': platform.python_version(),
            'flask_version': flask_version,
            'hostname': socket.gethostname(),
            'server_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'uptime': uptime,
            'cpu_usage': cpu_usage,
            'memory_total': memory_total,
            'memory_used': memory_used,
            'memory_percent': memory_percent,
            'disk_total': disk_total,
            'disk_used': disk_used,
            'disk_percent': disk_percent,
        }

        add_activity('تم تحديث معلومات النظام', 'Admin', 'Success')
        return jsonify({'success': True, 'system_info': system_info})
    except Exception as e:
        logger.error(f"Error refreshing system information: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/install_dependencies', methods=['POST'])
@login_required
def admin_install_dependencies():
    """Install missing dependencies"""
    try:
        import subprocess

        # قائمة المكتبات المطلوبة
        required_packages = [
            'flask',
            'python-docx',
            'beautifulsoup4',
            'requests',
            'psutil'
        ]

        # تثبيت المكتبات المفقودة
        installed_packages = []
        for package in required_packages:
            try:
                # التحقق مما إذا كانت المكتبة مثبتة بالفعل
                __import__(package.replace('-', '_'))
            except ImportError:
                # تثبيت المكتبة
                try:
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                    installed_packages.append(package)
                except subprocess.CalledProcessError as e:
                    logger.error(f"Error installing {package}: {str(e)}")
                    return jsonify({'success': False, 'message': f'فشل تثبيت {package}: {str(e)}'})

        if installed_packages:
            add_activity(f'تم تثبيت المكتبات: {", ".join(installed_packages)}', 'Admin', 'Success')
            return jsonify({'success': True, 'message': f'تم تثبيت المكتبات بنجاح: {", ".join(installed_packages)}'})
        else:
            return jsonify({'success': True, 'message': 'جميع المكتبات المطلوبة مثبتة بالفعل'})
    except Exception as e:
        logger.error(f"Error installing dependencies: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/refresh_logs', methods=['GET'])
@login_required
def admin_refresh_logs():
    """Refresh error logs"""
    try:
        # قراءة آخر الأخطاء من ملف السجل (إذا كان موجودًا)
        log_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.log')
        recent_errors = []

        if os.path.exists(log_file):
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # البحث عن آخر 10 أخطاء
                    error_count = 0
                    for line in reversed(lines):
                        if 'ERROR' in line:
                            recent_errors.append(line.strip())
                            error_count += 1
                            if error_count >= 10:
                                break
            except Exception as e:
                recent_errors = [f'Error reading log file: {str(e)}']
        else:
            recent_errors = ['Log file not found']

        add_activity('تم تحديث سجلات الأخطاء', 'Admin', 'Success')
        return jsonify({'success': True, 'recent_errors': recent_errors})
    except Exception as e:
        logger.error(f"Error refreshing logs: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/clear_error_logs', methods=['POST'])
@login_required
def admin_clear_error_logs():
    """Clear error logs"""
    try:
        # مسح ملف السجل
        log_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.log')

        if os.path.exists(log_file):
            try:
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.write('')
                add_activity('تم مسح سجلات الأخطاء', 'Admin', 'Success')
                return jsonify({'success': True, 'message': 'تم مسح سجلات الأخطاء بنجاح'})
            except Exception as e:
                logger.error(f"Error clearing log file: {str(e)}")
                return jsonify({'success': False, 'message': f'فشل مسح ملف السجل: {str(e)}'})
        else:
            return jsonify({'success': False, 'message': 'ملف السجل غير موجود'})
    except Exception as e:
        logger.error(f"Error clearing logs: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/clear_app_cache', methods=['POST'])
@login_required
def admin_clear_app_cache():
    """Clear application cache"""
    try:
        # مسح ملفات الكاش
        cache_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cache')

        if os.path.exists(cache_dir) and os.path.isdir(cache_dir):
            try:
                for filename in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, filename)
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                add_activity('تم مسح ذاكرة التخزين المؤقت', 'Admin', 'Success')
                return jsonify({'success': True, 'message': 'تم مسح ذاكرة التخزين المؤقت بنجاح'})
            except Exception as e:
                logger.error(f"Error clearing cache: {str(e)}")
                return jsonify({'success': False, 'message': f'فشل مسح ذاكرة التخزين المؤقت: {str(e)}'})
        else:
            # إنشاء مجلد الكاش إذا لم يكن موجودًا
            try:
                os.makedirs(cache_dir, exist_ok=True)
                return jsonify({'success': True, 'message': 'تم إنشاء مجلد الكاش'})
            except Exception as e:
                logger.error(f"Error creating cache directory: {str(e)}")
                return jsonify({'success': False, 'message': f'فشل إنشاء مجلد الكاش: {str(e)}'})
    except Exception as e:
        logger.error(f"Error clearing cache: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/create_backup_file', methods=['POST'])
@login_required
def admin_create_backup_file():
    """Create a backup of all data files"""
    try:
        # استيراد shutil للتعامل مع الملفات والمجلدات
        import datetime

        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"backup_{timestamp}.zip"
        backup_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backups')

        # إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجودًا
        os.makedirs(backup_path, exist_ok=True)

        # قائمة الملفات والمجلدات المراد نسخها احتياطيًا
        backup_items = [
            'data',
            'static',
            'templates',
            'app.py',
            'admin_routes.py',
            'translation_utils.py'
        ]

        # إنشاء ملف الضغط
        backup_file = os.path.join(backup_path, backup_filename)
        with zipfile.ZipFile(backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for item in backup_items:
                item_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), item)
                if os.path.exists(item_path):
                    if os.path.isdir(item_path):
                        for root, _, files in os.walk(item_path):
                            for file in files:
                                file_path = os.path.join(root, file)
                                zipf.write(file_path, os.path.relpath(file_path, os.path.dirname(os.path.dirname(__file__))))
                    else:
                        zipf.write(item_path, item)

        add_activity(f'تم إنشاء نسخة احتياطية: {backup_filename}', 'Admin', 'Success')
        return jsonify({
            'success': True,
            'message': f'تم إنشاء النسخة الاحتياطية بنجاح: {backup_filename}',
            'download_url': url_for('static', filename=f'backups/{backup_filename}')
        })
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/optimize_database_files', methods=['POST'])
@login_required
def admin_optimize_database_files():
    """Optimize database files"""
    try:
        # تحسين ملفات البيانات
        data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')

        if os.path.exists(data_dir) and os.path.isdir(data_dir):
            try:
                # قراءة وإعادة كتابة ملفات JSON لتحسينها
                for filename in os.listdir(data_dir):
                    if filename.endswith('.json'):
                        file_path = os.path.join(data_dir, filename)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            with open(file_path, 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                        except Exception as e:
                            logger.error(f"Error optimizing file {filename}: {str(e)}")

                add_activity('تم تحسين ملفات البيانات', 'Admin', 'Success')
                return jsonify({'success': True, 'message': 'تم تحسين ملفات البيانات بنجاح'})
            except Exception as e:
                logger.error(f"Error optimizing database: {str(e)}")
                return jsonify({'success': False, 'message': f'فشل تحسين ملفات البيانات: {str(e)}'})
        else:
            return jsonify({'success': False, 'message': 'مجلد البيانات غير موجود'})
    except Exception as e:
        logger.error(f"Error optimizing database: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

@admin_bp.route('/api/send_test_email', methods=['POST'])
@login_required
def admin_send_test_email():
    """Send a test email"""
    try:
        data = request.get_json()
        email = data.get('email')

        if not email:
            return jsonify({'success': False, 'message': 'عنوان البريد الإلكتروني مطلوب'})

        # إرسال بريد إلكتروني تجريبي
        try:
            # هذه مجرد محاكاة لإرسال البريد الإلكتروني
            # في التطبيق الحقيقي، يجب استخدام مكتبة مثل smtplib
            time.sleep(2)  # محاكاة تأخير إرسال البريد الإلكتروني

            add_activity(f'تم إرسال بريد إلكتروني تجريبي إلى: {email}', 'Admin', 'Success')
            return jsonify({'success': True, 'message': f'تم إرسال بريد إلكتروني تجريبي إلى: {email}'})
        except Exception as e:
            logger.error(f"Error sending test email: {str(e)}")
            return jsonify({'success': False, 'message': f'فشل إرسال البريد الإلكتروني: {str(e)}'})
    except Exception as e:
        logger.error(f"Error sending test email: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# Policies API Routes
@admin_bp.route('/api/save_privacy_policy', methods=['POST'])
@login_required
def admin_save_privacy_policy():
    """Save privacy policy"""
    try:
        policies_dict = load_policies()

        # تحديث سياسة الخصوصية
        policies_dict['privacy_content'] = request.form.get('privacy_content')
        policies_dict['privacy_title'] = request.form.get('privacy_title')
        policies_dict['privacy_enabled'] = request.form.get('privacy_enabled') == 'on'

        if save_policies(policies_dict):
            add_activity('تم تحديث سياسة الخصوصية', 'Admin', 'Success')
            flash('تم حفظ سياسة الخصوصية بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ سياسة الخصوصية', 'danger')

        return redirect(url_for('admin.admin_policies'))
    except Exception as e:
        logger.error(f"Error saving privacy policy: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_policies'))

@admin_bp.route('/api/save_terms', methods=['POST'])
@login_required
def admin_save_terms():
    """Save terms of use"""
    try:
        policies_dict = load_policies()

        # تحديث شروط الاستخدام
        policies_dict['terms_content'] = request.form.get('terms_content')
        policies_dict['terms_title'] = request.form.get('terms_title')
        policies_dict['terms_enabled'] = request.form.get('terms_enabled') == 'on'

        if save_policies(policies_dict):
            add_activity('تم تحديث شروط الاستخدام', 'Admin', 'Success')
            flash('تم حفظ شروط الاستخدام بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ شروط الاستخدام', 'danger')

        return redirect(url_for('admin.admin_policies'))
    except Exception as e:
        logger.error(f"Error saving terms: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_policies'))

@admin_bp.route('/api/save_cookie_policy', methods=['POST'])
@login_required
def admin_save_cookie_policy():
    """Save cookie policy"""
    try:
        policies_dict = load_policies()

        # تحديث سياسة ملفات تعريف الارتباط
        policies_dict['cookie_content'] = request.form.get('cookie_content')
        policies_dict['cookie_title'] = request.form.get('cookie_title')
        policies_dict['cookie_enabled'] = request.form.get('cookie_enabled') == 'on'

        if save_policies(policies_dict):
            add_activity('تم تحديث سياسة ملفات تعريف الارتباط', 'Admin', 'Success')
            flash('تم حفظ سياسة ملفات تعريف الارتباط بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ سياسة ملفات تعريف الارتباط', 'danger')

        return redirect(url_for('admin.admin_policies'))
    except Exception as e:
        logger.error(f"Error saving cookie policy: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_policies'))

@admin_bp.route('/api/save_consent', methods=['POST'])
@login_required
def admin_save_consent():
    """Save consent banner settings"""
    try:
        policies_dict = load_policies()

        # تحديث إعدادات شريط ملفات تعريف الارتباط
        policies_dict['show_consent_banner'] = request.form.get('show_consent_banner') == 'on'
        policies_dict['consent_title'] = request.form.get('consent_title')
        policies_dict['consent_message'] = request.form.get('consent_message')
        policies_dict['accept_button_text'] = request.form.get('accept_button_text')
        policies_dict['reject_button_text'] = request.form.get('reject_button_text')
        policies_dict['settings_button_text'] = request.form.get('settings_button_text')
        policies_dict['banner_position'] = request.form.get('banner_position')
        policies_dict['banner_theme'] = request.form.get('banner_theme')

        if save_policies(policies_dict):
            add_activity('تم تحديث إعدادات شريط موافقة ملفات تعريف الارتباط', 'Admin', 'Success')
            flash('تم حفظ إعدادات شريط موافقة ملفات تعريف الارتباط بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ إعدادات شريط موافقة ملفات تعريف الارتباط', 'danger')

        return redirect(url_for('admin.admin_policies'))
    except Exception as e:
        logger.error(f"Error saving consent settings: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_policies'))

@admin_bp.route('/api/restart_application', methods=['POST'])
@login_required
def admin_restart_application():
    """Restart the application"""
    try:
        add_activity('تم إعادة تشغيل التطبيق', 'Admin', 'Success')

        # في بيئة الإنتاج، يمكن استخدام طرق مختلفة لإعادة تشغيل التطبيق
        # هنا نقوم فقط بإرجاع استجابة نجاح
        return jsonify({'success': True, 'message': 'تم إرسال طلب إعادة تشغيل التطبيق'})
    except Exception as e:
        logger.error(f"Error restarting application: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# Interface Customization API Routes
@admin_bp.route('/api/save_logo', methods=['POST'])
@login_required
def admin_save_logo():
    """Save logo"""
    try:
        settings_dict = load_settings()

        # معالجة تحميل الشعار
        if 'logo' in request.files:
            logo_file = request.files['logo']
            if logo_file and logo_file.filename:
                # تأمين اسم الملف
                filename = secure_filename(logo_file.filename)
                # التأكد من أن الملف هو صورة
                if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg')):
                    # حفظ الملف
                    logo_path = os.path.join('static', 'images', filename)
                    logo_file.save(logo_path)
                    # تحديث مسار الشعار في الإعدادات
                    settings_dict['logo_path'] = logo_path

                    if save_settings(settings_dict):
                        add_activity('تم تحديث شعار الموقع', 'Admin', 'Success')
                        flash('تم تحديث شعار الموقع بنجاح', 'success')
                    else:
                        flash('حدث خطأ أثناء حفظ شعار الموقع', 'danger')
                else:
                    flash('يجب أن يكون الملف بتنسيق صورة (PNG, JPG, JPEG, GIF, SVG)', 'danger')
            else:
                flash('لم يتم اختيار ملف', 'danger')
        else:
            flash('لم يتم توفير ملف', 'danger')

        return redirect(url_for('admin.admin_interface'))
    except Exception as e:
        logger.error(f"Error saving logo: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_interface'))

@admin_bp.route('/api/save_colors', methods=['POST'])
@login_required
def admin_save_colors():
    """Save colors"""
    try:
        settings_dict = load_settings()

        # تحديث الألوان
        settings_dict['primary_color'] = request.form.get('primary_color', '#0d6efd')
        settings_dict['secondary_color'] = request.form.get('secondary_color', '#6c757d')

        if save_settings(settings_dict):
            add_activity('تم تحديث ألوان الموقع', 'Admin', 'Success')
            flash('تم تحديث ألوان الموقع بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ ألوان الموقع', 'danger')

        return redirect(url_for('admin.admin_interface'))
    except Exception as e:
        logger.error(f"Error saving colors: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_interface'))

@admin_bp.route('/api/save_social', methods=['POST'])
@login_required
def admin_save_social():
    """Save social links"""
    try:
        # استيراد وحدة التعامل مع مواقع التواصل الاجتماعي
        from social_media import get_social_media, update_social_media, SUPPORTED_SOCIAL_MEDIA

        # الحصول على إعدادات مواقع التواصل الاجتماعي الحالية
        social_media = get_social_media()

        # تحديث إعدادات مواقع التواصل الاجتماعي
        for site_id in SUPPORTED_SOCIAL_MEDIA:
            # تحديث حالة التفعيل
            social_media[site_id]['enabled'] = request.form.get(f'enable_{site_id}') == 'on'

            # تحديث الرابط
            social_media[site_id]['url'] = request.form.get(f'{site_id}_url', '').strip()

            # إضافة معلومات إضافية من SUPPORTED_SOCIAL_MEDIA
            social_media[site_id]['name'] = SUPPORTED_SOCIAL_MEDIA[site_id]['name']
            social_media[site_id]['icon'] = SUPPORTED_SOCIAL_MEDIA[site_id]['icon']
            social_media[site_id]['color'] = SUPPORTED_SOCIAL_MEDIA[site_id]['color']

        # حفظ الإعدادات
        if update_social_media(social_media):
            add_activity('تم تحديث روابط التواصل الاجتماعي', 'Admin', 'Success')
            flash('تم تحديث روابط التواصل الاجتماعي بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ روابط التواصل الاجتماعي', 'danger')

        return redirect(url_for('admin.admin_interface'))
    except Exception as e:
        logger.error(f"Error saving social links: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_interface'))

@admin_bp.route('/api/save_display', methods=['POST'])
@login_required
def admin_save_display():
    """Save display settings"""
    try:
        settings_dict = load_settings()

        # تحديث إعدادات العرض
        settings_dict['welcome_message'] = request.form.get('welcome_message', 'مرحبًا بك في محول تيفيناغ!')
        settings_dict['show_logo'] = request.form.get('show_logo') == 'on'
        settings_dict['show_footer'] = request.form.get('show_footer') == 'on'
        settings_dict['show_social'] = request.form.get('show_social') == 'on'
        settings_dict['maintenance_mode'] = request.form.get('maintenance_mode') == 'on'
        settings_dict['maintenance_message'] = request.form.get('maintenance_message', 'The site is currently under maintenance. Please check back later.')

        if save_settings(settings_dict):
            add_activity('تم تحديث إعدادات العرض', 'Admin', 'Success')
            flash('تم تحديث إعدادات العرض بنجاح', 'success')
        else:
            flash('حدث خطأ أثناء حفظ إعدادات العرض', 'danger')

        return redirect(url_for('admin.admin_interface'))
    except Exception as e:
        logger.error(f"Error saving display settings: {str(e)}")
        flash(f'حدث خطأ: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_interface'))

@admin_bp.route('/api/reset_translations', methods=['POST'])
@login_required
def admin_reset_translations():
    """Reset translations to default"""
    try:
        from utils.translation_utils import save_translations

        # إنشاء ترجمات افتراضية
        default_translations = {
            "Welcome": {"en": "Welcome", "am": "ⴰⵣⵓⵍ"},
            "Tifinagh Converter": {"en": "Tifinagh Converter", "am": "ⴰⵙⵏⴼⴰⵍ ⵏ ⵜⵉⴼⵉⵏⴰⵖ"},
            "Convert": {"en": "Convert", "am": "ⵙⵏⴼⵍ"},
            "Clear": {"en": "Clear", "am": "ⵙⴼⴹ"},
            "Copy": {"en": "Copy", "am": "ⵙⵏⵖⵍ"},
            "Download": {"en": "Download", "am": "ⴰⴳⵎ"},
            "Select File": {"en": "Select File", "am": "ⴼⵔⵏ ⴰⴼⴰⵢⵍⵓ"}
        }

        # حفظ الترجمات الافتراضية
        if save_translations(default_translations):
            add_activity('تم إعادة تعيين الترجمات إلى القيم الافتراضية', 'Admin', 'Success')
            return jsonify({'success': True, 'message': 'تم إعادة تعيين الترجمات إلى القيم الافتراضية'})
        else:
            return jsonify({'success': False, 'message': 'فشل إعادة تعيين الترجمات'})
    except Exception as e:
        logger.error(f"Error resetting translations: {str(e)}")
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'})

# API Routes for admin dashboard
@admin_bp.route('/api/create_backup', methods=['POST'])
@login_required
def admin_create_backup():
    """Create a backup of all data files"""
    try:
        backup_file = create_backup()
        if backup_file:
            return jsonify({
                'success': True,
                'message': 'تم إنشاء نسخة احتياطية بنجاح',
                'download_url': url_for('admin.download_backup', filename=backup_file)
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل إنشاء نسخة احتياطية'
            }), 500
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@admin_bp.route('/api/optimize_database', methods=['POST'])
@login_required
def admin_optimize_database():
    """Optimize data files"""
    try:
        success = optimize_data_files()
        if success:
            return jsonify({
                'success': True,
                'message': 'تم تحسين ملفات البيانات بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل تحسين ملفات البيانات'
            }), 500
    except Exception as e:
        logger.error(f"Error optimizing data files: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@admin_bp.route('/api/clear_cache', methods=['POST'])
@login_required
def admin_clear_cache():
    """Clear application cache"""
    try:
        # إنشاء مجلد cache إذا لم يكن موجودًا
        cache_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cache')
        if os.path.exists(cache_dir):
            # حذف جميع الملفات في مجلد cache
            for filename in os.listdir(cache_dir):
                file_path = os.path.join(cache_dir, filename)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                except Exception as e:
                    logger.error(f"Error deleting {file_path}: {str(e)}")

        add_activity('تم مسح ذاكرة التخزين المؤقت', 'Admin', 'Success')

        return jsonify({
            'success': True,
            'message': 'تم مسح ذاكرة التخزين المؤقت بنجاح'
        })
    except Exception as e:
        logger.error(f"Error clearing cache: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@admin_bp.route('/api/clear_logs', methods=['POST'])
@login_required
def admin_clear_logs():
    """Clear error logs"""
    try:
        # تحديث الإحصائيات لإزالة سجل الأنشطة
        stats = load_stats()
        stats['recent_activities'] = []
        save_stats(stats)

        # مسح ملف السجل إذا كان موجودًا
        log_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.log')
        if os.path.exists(log_file):
            with open(log_file, 'w') as f:
                f.write('')

        add_activity('تم مسح سجلات الأخطاء', 'Admin', 'Success')

        return jsonify({
            'success': True,
            'message': 'تم مسح سجلات الأخطاء بنجاح'
        })
    except Exception as e:
        logger.error(f"Error clearing logs: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@admin_bp.route('/api/test_email', methods=['POST'])
@login_required
def admin_test_email():
    """Send test email"""
    try:
        data = request.get_json()
        email = data.get('email')

        if not email:
            return jsonify({
                'success': False,
                'message': 'لم يتم توفير عنوان البريد الإلكتروني'
            }), 400

        # هنا يمكن إضافة كود لإرسال بريد إلكتروني تجريبي
        # لكن في هذه النسخة سنكتفي بتسجيل النشاط

        add_activity(f'تم إرسال بريد إلكتروني تجريبي إلى {email}', 'Admin', 'Success')

        return jsonify({
            'success': True,
            'message': f'تم إرسال بريد إلكتروني تجريبي إلى {email}'
        })
    except Exception as e:
        logger.error(f"Error sending test email: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@admin_bp.route('/api/restart_app', methods=['POST'])
@login_required
def admin_restart_app():
    """Restart application (simulation)"""
    try:
        # في بيئة الإنتاج، يمكن استخدام أوامر نظام التشغيل لإعادة تشغيل التطبيق
        # لكن هنا سنكتفي بتسجيل النشاط

        add_activity('تم إعادة تشغيل التطبيق', 'Admin', 'Success')

        return jsonify({
            'success': True,
            'message': 'تم إعادة تشغيل التطبيق بنجاح'
        })
    except Exception as e:
        logger.error(f"Error restarting application: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

@admin_bp.route('/backups/<filename>')
@login_required
def download_backup(filename):
    """Download backup file"""
    try:
        backup_dir = os.path.join(DATA_DIR, 'backups')
        return send_file(os.path.join(backup_dir, filename), as_attachment=True)
    except Exception as e:
        logger.error(f"Error downloading backup: {str(e)}")
        flash(f'حدث خطأ أثناء تنزيل النسخة الاحتياطية: {str(e)}', 'danger')
        return redirect(url_for('admin.admin_tools'))